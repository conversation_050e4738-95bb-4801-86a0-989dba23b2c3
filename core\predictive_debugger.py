"""
Predictive Debugger for the AI Code Assistant.

This module provides predictive debugging capabilities, analyzing code execution
patterns and predicting potential runtime issues before they occur.
"""

import time
import logging
import threading
import ast
import re
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class PredictedIssue:
    """Represents a predicted runtime issue."""
    issue_type: str
    severity: str  # critical, high, medium, low
    probability: float  # 0.0 to 1.0
    description: str
    location: Dict[str, int]  # line, column
    potential_causes: List[str]
    suggested_solutions: List[str]
    execution_context: Dict[str, Any]
    confidence: float

@dataclass
class ExecutionPath:
    """Represents a potential execution path."""
    path_id: str
    conditions: List[str]
    variables: Dict[str, Any]
    function_calls: List[str]
    potential_issues: List[PredictedIssue]
    probability: float

@dataclass
class DebugPrediction:
    """Result of predictive debugging analysis."""
    predicted_issues: List[PredictedIssue]
    execution_paths: List[ExecutionPath]
    risk_assessment: Dict[str, float]
    recommendations: List[str]
    analysis_time: float
    confidence_score: float

class PredictiveDebugger:
    """Predictive debugging system that anticipates runtime issues."""

    def __init__(self, workspace_dir: Path, model_manager=None):
        """Initialize the predictive debugger.
        
        Args:
            workspace_dir: The workspace directory
            model_manager: Optional model manager for AI-assisted prediction
        """
        self.workspace_dir = workspace_dir
        self.model_manager = model_manager
        self.prediction_patterns: Dict[str, Any] = {}
        self.execution_history: List[Dict[str, Any]] = []
        self.debug_history: List[DebugPrediction] = []
        self.lock = threading.RLock()
        
        # Initialize prediction patterns
        self._initialize_prediction_patterns()

    def _initialize_prediction_patterns(self):
        """Initialize patterns for predicting runtime issues."""
        self.prediction_patterns = {
            "null_pointer": {
                "indicators": ["None", "null", "undefined"],
                "contexts": ["attribute_access", "method_call"],
                "severity": "high",
                "probability_base": 0.7
            },
            "index_error": {
                "indicators": ["[", "index", "range"],
                "contexts": ["list_access", "string_access"],
                "severity": "high",
                "probability_base": 0.6
            },
            "type_error": {
                "indicators": ["int", "str", "float", "+", "-", "*", "/"],
                "contexts": ["type_mismatch", "operation"],
                "severity": "medium",
                "probability_base": 0.5
            },
            "infinite_loop": {
                "indicators": ["while", "for", "True"],
                "contexts": ["loop_condition", "break_missing"],
                "severity": "critical",
                "probability_base": 0.8
            },
            "memory_leak": {
                "indicators": ["list", "dict", "append", "global"],
                "contexts": ["unbounded_growth", "global_accumulation"],
                "severity": "medium",
                "probability_base": 0.4
            },
            "division_by_zero": {
                "indicators": ["/", "//", "%"],
                "contexts": ["division", "modulo"],
                "severity": "high",
                "probability_base": 0.6
            }
        }

    def predict_issues(self, code: str, language: str = "python", 
                      execution_context: Optional[Dict[str, Any]] = None) -> DebugPrediction:
        """Predict potential runtime issues in code.
        
        Args:
            code: The code to analyze
            language: Programming language
            execution_context: Optional execution context information
            
        Returns:
            Debug prediction with potential issues
        """
        with self.lock:
            start_time = time.time()
            logger.info(f"Predicting runtime issues in {language} code")
            
            predicted_issues = []
            execution_paths = []
            
            if language == "python":
                predicted_issues.extend(self._predict_python_issues(code, execution_context))
                execution_paths.extend(self._analyze_python_execution_paths(code))
            elif language in ["javascript", "typescript"]:
                predicted_issues.extend(self._predict_javascript_issues(code, execution_context))
            else:
                predicted_issues.extend(self._predict_generic_issues(code, execution_context))
            
            # Calculate risk assessment
            risk_assessment = self._calculate_risk_assessment(predicted_issues)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(predicted_issues, execution_paths)
            
            # Calculate overall confidence
            confidence_score = self._calculate_confidence_score(predicted_issues, execution_paths)
            
            analysis_time = time.time() - start_time
            
            prediction = DebugPrediction(
                predicted_issues=predicted_issues,
                execution_paths=execution_paths,
                risk_assessment=risk_assessment,
                recommendations=recommendations,
                analysis_time=analysis_time,
                confidence_score=confidence_score
            )
            
            # Store in history
            self.debug_history.append(prediction)
            
            logger.info(f"Predicted {len(predicted_issues)} potential issues in {analysis_time:.3f}s")
            return prediction

    def _predict_python_issues(self, code: str, execution_context: Optional[Dict[str, Any]]) -> List[PredictedIssue]:
        """Predict Python-specific runtime issues."""
        issues = []
        
        try:
            tree = ast.parse(code)
            
            # Analyze AST for potential issues
            for node in ast.walk(tree):
                if isinstance(node, ast.Subscript):
                    # Potential index errors
                    issue = self._analyze_subscript_access(node, code)
                    if issue:
                        issues.append(issue)
                
                elif isinstance(node, ast.Attribute):
                    # Potential attribute errors
                    issue = self._analyze_attribute_access(node, code)
                    if issue:
                        issues.append(issue)
                
                elif isinstance(node, ast.BinOp) and isinstance(node.op, (ast.Div, ast.FloorDiv, ast.Mod)):
                    # Potential division by zero
                    issue = self._analyze_division_operation(node, code)
                    if issue:
                        issues.append(issue)
                
                elif isinstance(node, ast.While):
                    # Potential infinite loops
                    issue = self._analyze_while_loop(node, code)
                    if issue:
                        issues.append(issue)
                
                elif isinstance(node, ast.Call):
                    # Potential function call issues
                    issue = self._analyze_function_call(node, code)
                    if issue:
                        issues.append(issue)
        
        except SyntaxError:
            # If code has syntax errors, predict that as an issue
            issues.append(PredictedIssue(
                issue_type="syntax_error",
                severity="critical",
                probability=1.0,
                description="Code contains syntax errors",
                location={"line": 1, "column": 0},
                potential_causes=["Invalid Python syntax"],
                suggested_solutions=["Fix syntax errors before execution"],
                execution_context={},
                confidence=1.0
            ))
        
        return issues

    def _analyze_subscript_access(self, node: ast.Subscript, code: str) -> Optional[PredictedIssue]:
        """Analyze subscript access for potential index errors."""
        # Check if accessing with a variable that might be out of bounds
        if isinstance(node.slice, ast.Name):
            return PredictedIssue(
                issue_type="index_error",
                severity="high",
                probability=0.6,
                description=f"Potential IndexError when accessing with variable '{node.slice.id}'",
                location={"line": node.lineno, "column": node.col_offset},
                potential_causes=[
                    "Index variable might be out of bounds",
                    "Container might be empty",
                    "Index calculation error"
                ],
                suggested_solutions=[
                    "Add bounds checking before access",
                    "Use try-except block",
                    "Validate index range"
                ],
                execution_context={"variable": node.slice.id},
                confidence=0.7
            )
        
        return None

    def _analyze_attribute_access(self, node: ast.Attribute, code: str) -> Optional[PredictedIssue]:
        """Analyze attribute access for potential AttributeError."""
        # Check if accessing attribute on a variable that might be None
        if isinstance(node.value, ast.Name):
            return PredictedIssue(
                issue_type="attribute_error",
                severity="high",
                probability=0.5,
                description=f"Potential AttributeError when accessing '{node.attr}' on '{node.value.id}'",
                location={"line": node.lineno, "column": node.col_offset},
                potential_causes=[
                    "Variable might be None",
                    "Object might not have the attribute",
                    "Type mismatch"
                ],
                suggested_solutions=[
                    "Check if variable is not None before access",
                    "Use hasattr() to check attribute existence",
                    "Add type checking"
                ],
                execution_context={"variable": node.value.id, "attribute": node.attr},
                confidence=0.6
            )
        
        return None

    def _analyze_division_operation(self, node: ast.BinOp, code: str) -> Optional[PredictedIssue]:
        """Analyze division operations for potential division by zero."""
        # Check if dividing by a variable or expression
        if isinstance(node.right, ast.Name):
            return PredictedIssue(
                issue_type="division_by_zero",
                severity="high",
                probability=0.6,
                description=f"Potential ZeroDivisionError when dividing by '{node.right.id}'",
                location={"line": node.lineno, "column": node.col_offset},
                potential_causes=[
                    "Divisor variable might be zero",
                    "Calculation error leading to zero",
                    "Uninitialized variable"
                ],
                suggested_solutions=[
                    "Check if divisor is not zero before division",
                    "Use try-except block",
                    "Validate input values"
                ],
                execution_context={"divisor": node.right.id},
                confidence=0.7
            )
        
        return None

    def _analyze_while_loop(self, node: ast.While, code: str) -> Optional[PredictedIssue]:
        """Analyze while loops for potential infinite loops."""
        # Simple heuristic: check if condition is always True
        if isinstance(node.test, ast.Constant) and node.test.value is True:
            # Check if there's a break statement in the loop
            has_break = any(isinstance(n, ast.Break) for n in ast.walk(node))
            
            if not has_break:
                return PredictedIssue(
                    issue_type="infinite_loop",
                    severity="critical",
                    probability=0.9,
                    description="Potential infinite loop: while True without break",
                    location={"line": node.lineno, "column": node.col_offset},
                    potential_causes=[
                        "No break condition in while True loop",
                        "Break condition never reached",
                        "Logic error in loop termination"
                    ],
                    suggested_solutions=[
                        "Add break condition",
                        "Use finite loop condition",
                        "Add timeout mechanism"
                    ],
                    execution_context={},
                    confidence=0.9
                )
        
        return None

    def _analyze_function_call(self, node: ast.Call, code: str) -> Optional[PredictedIssue]:
        """Analyze function calls for potential issues."""
        # Check for calls that might fail
        if isinstance(node.func, ast.Name):
            func_name = node.func.id
            
            # Check for potentially problematic function calls
            if func_name in ["int", "float"]:
                return PredictedIssue(
                    issue_type="value_error",
                    severity="medium",
                    probability=0.4,
                    description=f"Potential ValueError in {func_name}() conversion",
                    location={"line": node.lineno, "column": node.col_offset},
                    potential_causes=[
                        "Invalid input for type conversion",
                        "Non-numeric string passed to numeric conversion",
                        "Empty or None value"
                    ],
                    suggested_solutions=[
                        "Validate input before conversion",
                        "Use try-except block",
                        "Check for valid format"
                    ],
                    execution_context={"function": func_name},
                    confidence=0.5
                )
        
        return None

    def _predict_javascript_issues(self, code: str, execution_context: Optional[Dict[str, Any]]) -> List[PredictedIssue]:
        """Predict JavaScript-specific runtime issues."""
        issues = []
        lines = code.splitlines()
        
        for line_num, line in enumerate(lines, 1):
            # Check for potential null/undefined access
            if re.search(r'\w+\.\w+', line) and 'null' not in line and 'undefined' not in line:
                issues.append(PredictedIssue(
                    issue_type="null_reference",
                    severity="high",
                    probability=0.5,
                    description="Potential null/undefined reference error",
                    location={"line": line_num, "column": 0},
                    potential_causes=[
                        "Object might be null or undefined",
                        "Property might not exist",
                        "Asynchronous operation not completed"
                    ],
                    suggested_solutions=[
                        "Add null/undefined checks",
                        "Use optional chaining (?.)",
                        "Validate object before access"
                    ],
                    execution_context={},
                    confidence=0.6
                ))
            
            # Check for potential type coercion issues
            if re.search(r'==(?!=)', line):
                issues.append(PredictedIssue(
                    issue_type="type_coercion",
                    severity="medium",
                    probability=0.7,
                    description="Potential type coercion issue with == operator",
                    location={"line": line_num, "column": line.find('==')},
                    potential_causes=[
                        "Unexpected type conversion",
                        "Loose equality comparison",
                        "Different types being compared"
                    ],
                    suggested_solutions=[
                        "Use strict equality (===)",
                        "Explicit type conversion",
                        "Type checking before comparison"
                    ],
                    execution_context={},
                    confidence=0.8
                ))
        
        return issues

    def _predict_generic_issues(self, code: str, execution_context: Optional[Dict[str, Any]]) -> List[PredictedIssue]:
        """Predict generic runtime issues."""
        issues = []
        lines = code.splitlines()
        
        # Check for potential resource leaks
        for line_num, line in enumerate(lines, 1):
            if 'open(' in line and 'with' not in line:
                issues.append(PredictedIssue(
                    issue_type="resource_leak",
                    severity="medium",
                    probability=0.6,
                    description="Potential resource leak: file not properly closed",
                    location={"line": line_num, "column": line.find('open(')},
                    potential_causes=[
                        "File handle not closed",
                        "Exception before close()",
                        "Missing finally block"
                    ],
                    suggested_solutions=[
                        "Use 'with' statement",
                        "Add try-finally block",
                        "Explicit close() call"
                    ],
                    execution_context={},
                    confidence=0.7
                ))
        
        return issues

    def _analyze_python_execution_paths(self, code: str) -> List[ExecutionPath]:
        """Analyze potential execution paths in Python code."""
        paths = []
        
        try:
            tree = ast.parse(code)
            
            # Simple path analysis for if statements
            for node in ast.walk(tree):
                if isinstance(node, ast.If):
                    # Create paths for if and else branches
                    if_path = ExecutionPath(
                        path_id=f"if_{node.lineno}",
                        conditions=[f"if condition at line {node.lineno} is True"],
                        variables={},
                        function_calls=[],
                        potential_issues=[],
                        probability=0.5
                    )
                    paths.append(if_path)
                    
                    if node.orelse:
                        else_path = ExecutionPath(
                            path_id=f"else_{node.lineno}",
                            conditions=[f"if condition at line {node.lineno} is False"],
                            variables={},
                            function_calls=[],
                            potential_issues=[],
                            probability=0.5
                        )
                        paths.append(else_path)
        
        except SyntaxError:
            pass
        
        return paths

    def _calculate_risk_assessment(self, issues: List[PredictedIssue]) -> Dict[str, float]:
        """Calculate overall risk assessment."""
        if not issues:
            return {"overall_risk": 0.0, "critical_risk": 0.0, "high_risk": 0.0}
        
        severity_weights = {"critical": 1.0, "high": 0.7, "medium": 0.4, "low": 0.1}
        
        total_risk = 0.0
        critical_risk = 0.0
        high_risk = 0.0
        
        for issue in issues:
            risk_value = issue.probability * severity_weights.get(issue.severity, 0.1)
            total_risk += risk_value
            
            if issue.severity == "critical":
                critical_risk += risk_value
            elif issue.severity == "high":
                high_risk += risk_value
        
        # Normalize risks
        max_possible_risk = len(issues) * 1.0
        
        return {
            "overall_risk": min(total_risk / max_possible_risk, 1.0) if max_possible_risk > 0 else 0.0,
            "critical_risk": critical_risk,
            "high_risk": high_risk,
            "issue_count": len(issues)
        }

    def _generate_recommendations(self, issues: List[PredictedIssue], 
                                paths: List[ExecutionPath]) -> List[str]:
        """Generate recommendations based on predicted issues."""
        recommendations = []
        
        # Group issues by type
        issue_types = {}
        for issue in issues:
            if issue.issue_type not in issue_types:
                issue_types[issue.issue_type] = []
            issue_types[issue.issue_type].append(issue)
        
        # Generate type-specific recommendations
        for issue_type, type_issues in issue_types.items():
            if issue_type == "index_error":
                recommendations.append("Add bounds checking before array/list access")
            elif issue_type == "null_reference":
                recommendations.append("Implement null/undefined checks before object access")
            elif issue_type == "division_by_zero":
                recommendations.append("Validate divisor values before arithmetic operations")
            elif issue_type == "infinite_loop":
                recommendations.append("Add proper loop termination conditions")
            elif issue_type == "resource_leak":
                recommendations.append("Use proper resource management (with statements, try-finally)")
        
        # General recommendations based on risk level
        critical_issues = [i for i in issues if i.severity == "critical"]
        if critical_issues:
            recommendations.append("Address critical issues immediately before deployment")
        
        high_issues = [i for i in issues if i.severity == "high"]
        if len(high_issues) > 3:
            recommendations.append("Consider comprehensive code review due to multiple high-risk issues")
        
        return recommendations

    def _calculate_confidence_score(self, issues: List[PredictedIssue], 
                                  paths: List[ExecutionPath]) -> float:
        """Calculate overall confidence in predictions."""
        if not issues:
            return 0.5
        
        # Average confidence of all issues
        avg_confidence = sum(issue.confidence for issue in issues) / len(issues)
        
        # Adjust based on number of issues (more issues = higher confidence in analysis)
        issue_factor = min(len(issues) / 10.0, 1.0)
        
        # Combine factors
        overall_confidence = (avg_confidence * 0.7 + issue_factor * 0.3)
        
        return max(0.0, min(1.0, overall_confidence))

    def get_debug_statistics(self) -> Dict[str, Any]:
        """Get predictive debugging statistics."""
        with self.lock:
            if not self.debug_history:
                return {"total_predictions": 0}
            
            total_predictions = len(self.debug_history)
            total_issues = sum(len(p.predicted_issues) for p in self.debug_history)
            avg_confidence = sum(p.confidence_score for p in self.debug_history) / total_predictions
            avg_time = sum(p.analysis_time for p in self.debug_history) / total_predictions
            
            # Count issue types
            issue_types = {}
            for prediction in self.debug_history:
                for issue in prediction.predicted_issues:
                    issue_types[issue.issue_type] = issue_types.get(issue.issue_type, 0) + 1
            
            return {
                "total_predictions": total_predictions,
                "total_issues_predicted": total_issues,
                "average_confidence": avg_confidence,
                "average_analysis_time": avg_time,
                "issue_types": issue_types,
                "patterns_count": len(self.prediction_patterns)
            }

    def clear_history(self):
        """Clear debug prediction history."""
        with self.lock:
            self.debug_history.clear()
            self.execution_history.clear()
            logger.info("Cleared predictive debugger history")
