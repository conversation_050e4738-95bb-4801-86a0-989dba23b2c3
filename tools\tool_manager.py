import json
from dataclasses import dataclass, field
from typing import Callable, Dict, Any, List, Optional

@dataclass
class Tool:
    """Represents a tool with its metadata and execution function."""
    name: str
    description: str
    function: Callable
    parameters: Dict[str, Any] = field(default_factory=dict) # JSON schema for parameters

class ToolManager:
    """Manages the registration and retrieval of tools."""

    def __init__(self):
        self._tools: Dict[str, Tool] = {}

    @property
    def tools(self):
        return self._tools

    def register_tool(self, tool: Tool):
        """Registers a tool with the manager."""
        if not isinstance(tool, Tool):
            raise TypeError("Only instances of Tool can be registered.")
        if tool.name in self._tools:
            raise ValueError(f"Tool with name '{tool.name}' already registered.")
        self._tools[tool.name] = tool

    def get_tool(self, name: str) -> Optional[Tool]:
        """Retrieves a tool by its name."""
        return self._tools.get(name)

    def get_all_tools(self) -> List[Tool]:
        """Returns a list of all registered tools."""
        return list(self._tools.values())

    def get_tool_schema(self, name: str) -> Optional[Dict[str, Any]]:
        """Returns the JSON schema for a specific tool's parameters."""
        tool = self.get_tool(name)
        return tool.parameters if tool else None

    def get_all_tool_schemas(self) -> Dict[str, Dict[str, Any]]:
        """Returns a dictionary of all registered tool schemas."""
        return {name: tool.parameters for name, tool in self._tools.items()}

    def get_tool_descriptions(self) -> List[Dict[str, str]]:
        """Returns a list of tool descriptions suitable for prompt generation."""
        descriptions = []
        for tool in self._tools.values():
            descriptions.append({
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.parameters
            })
        return descriptions

    def generate_tool_prompt_examples(self) -> str:
        """Generates example usage for all registered tools."""
        examples = []
        for tool in self._tools.values():
            example_args = {}
            for param_name, param_schema in tool.parameters.get("properties", {}).items():
                # Simple example value generation based on type
                if param_schema.get("type") == "string":
                    example_args[param_name] = f"example_{param_name}"
                elif param_schema.get("type") == "integer":
                    example_args[param_name] = 123
                elif param_schema.get("type") == "boolean":
                    example_args[param_name] = True
                elif param_schema.get("type") == "array":
                    example_args[param_name] = ["item1", "item2"]
                else:
                    example_args[param_name] = "..." # Fallback for complex types

            # Format the example arguments as JSON
            args_json = json.dumps(example_args, indent=2)

            examples.append(f"""
To use {tool.name} ({tool.description}):
```{tool.name}
{args_json}
```""")
        return "\n".join(examples)