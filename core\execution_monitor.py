"""
Execution Monitor for tracking step-by-step execution progress and performance.
"""

import time
import threading
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class ExecutionResult:
    """Result of an execution operation."""
    success: bool
    operation: str
    start_time: float
    end_time: float 
    duration: float = field(init=False)
    output: Any = None
    error: Optional[str] = None
    metrics: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Calculate duration after initialization."""
        self.duration = self.end_time - self.start_time

class ExecutionStatus(Enum):
    """Status of execution monitoring."""
    IDLE = "idle"
    MONITORING = "monitoring"
    PAUSED = "paused"
    ERROR = "error"

@dataclass
class ExecutionEvent:
    """Record of an execution event."""
    timestamp: float
    event_type: str
    details: Dict[str, Any]
    duration: Optional[float] = None
    status: str = "success"
    error: Optional[str] = None

class ExecutionMonitor:
    """Monitor for tracking execution progress and performance."""
    
    def __init__(self, workspace_dir: Optional[Path] = None):
        """Initialize the execution monitor.
        
        Args:
            workspace_dir: Optional workspace directory for execution
        """
        self.workspace_dir = workspace_dir or Path.cwd()
        self.status = ExecutionStatus.IDLE
        self.events: List[ExecutionEvent] = []
        self.start_time: Optional[float] = None
        self.lock = threading.RLock()
        self.current_operation: Optional[str] = None
        
        # Performance metrics
        self.metrics = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'total_duration': 0.0,
            'average_operation_time': 0.0,
            'error_rate': 0.0
        }
        
        logger.info("Initialized ExecutionMonitor")
    
    def start_monitoring(self):
        """Start execution monitoring."""
        with self.lock:
            self.status = ExecutionStatus.MONITORING
            self.start_time = time.time()
            self.events.clear()
            self.metrics = {k: 0 for k in self.metrics}
            
            self._record_event("monitoring_started", {
                "timestamp": self.start_time
            })
            
            logger.info("Started execution monitoring")
    
    def stop_monitoring(self):
        """Stop execution monitoring."""
        with self.lock:
            if self.status != ExecutionStatus.MONITORING:
                return
                
            end_time = time.time()
            duration = end_time - (self.start_time or end_time)
            
            self._record_event("monitoring_stopped", {
                "duration": duration,
                "total_events": len(self.events)
            })
            
            self.status = ExecutionStatus.IDLE
            self._update_final_metrics(duration)
            
            logger.info(f"Stopped monitoring after {duration:.2f}s")
    
    def pause_monitoring(self):
        """Pause execution monitoring."""
        with self.lock:
            if self.status != ExecutionStatus.MONITORING:
                return
                
            self.status = ExecutionStatus.PAUSED
            self._record_event("monitoring_paused", {})
            logger.info("Paused execution monitoring")
    
    def resume_monitoring(self):
        """Resume execution monitoring."""
        with self.lock:
            if self.status != ExecutionStatus.PAUSED:
                return
                
            self.status = ExecutionStatus.MONITORING
            self._record_event("monitoring_resumed", {})
            logger.info("Resumed execution monitoring")
    
    def record_operation_start(self, operation: str, details: Dict[str, Any] = None):
        """Record the start of an operation.
        
        Args:
            operation: Name/description of the operation
            details: Optional operation details
        """
        with self.lock:
            if self.status != ExecutionStatus.MONITORING:
                return
                
            self.current_operation = operation
            self._record_event(
                "operation_started",
                {"operation": operation, **(details or {})}
            )
            
            logger.debug(f"Started operation: {operation}")
    
    def record_operation_end(self, operation: str, success: bool = True, error: str = None):
        """Record the end of an operation.
        
        Args:
            operation: Name/description of the operation
            success: Whether the operation succeeded
            error: Optional error message if operation failed
        """
        with self.lock:
            if self.status != ExecutionStatus.MONITORING:
                return
                
            if operation != self.current_operation:
                logger.warning(f"Operation end '{operation}' doesn't match current '{self.current_operation}'")
                return
                
            self._record_event(
                "operation_ended",
                {
                    "operation": operation,
                    "success": success,
                    "error": error
                }
            )
            
            # Update metrics
            self.metrics['total_operations'] += 1
            if success:
                self.metrics['successful_operations'] += 1
            else:
                self.metrics['failed_operations'] += 1
            
            self.current_operation = None
            
            if success:
                logger.debug(f"Completed operation: {operation}")
            else:
                logger.warning(f"Failed operation: {operation} - {error}")
    
    def record_metric(self, metric_name: str, value: Any):
        """Record a custom metric.
        
        Args:
            metric_name: Name of the metric
            value: Metric value
        """
        with self.lock:
            if self.status != ExecutionStatus.MONITORING:
                return
                
            self._record_event(
                "metric_recorded",
                {
                    "metric": metric_name,
                    "value": value
                }
            )
            
            logger.debug(f"Recorded metric {metric_name}: {value}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics.
        
        Returns:
            Dictionary of metrics
        """
        with self.lock:
            return self.metrics.copy()
    
    def get_events(self, event_type: str = None) -> List[ExecutionEvent]:
        """Get recorded events, optionally filtered by type.
        
        Args:
            event_type: Optional event type to filter by
            
        Returns:
            List of events
        """
        with self.lock:
            if event_type:
                return [e for e in self.events if e.event_type == event_type]
            return self.events.copy()
    
    def _record_event(self, event_type: str, details: Dict[str, Any]):
        """Record an execution event."""
        event = ExecutionEvent(
            timestamp=time.time(),
            event_type=event_type,
            details=details
        )
        self.events.append(event)
    
    def _update_final_metrics(self, total_duration: float):
        """Update final metrics upon stopping."""
        self.metrics['total_duration'] = total_duration
        
        if self.metrics['total_operations'] > 0:
            self.metrics['average_operation_time'] = (
                total_duration / self.metrics['total_operations']
            )
            self.metrics['error_rate'] = (
                self.metrics['failed_operations'] / self.metrics['total_operations']
            )
    
    def execute_and_monitor(self, code: str, language: str, timeout: float = 30.0) -> ExecutionResult:
        """Execute code and monitor its execution.
        
        Args:
            code: The code to execute
            language: Programming language of the code
            timeout: Maximum execution time in seconds
            
        Returns:
            Execution result with metrics
        """
        start_time = time.time()
        operation = f"execute_{language}_code"
        
        try:
            self.record_operation_start(operation, {
                "language": language,
                "code_size": len(code),
                "timeout": timeout
            })
            
            # Create temporary file for execution
            temp_file = Path(self.workspace_dir) / f"temp_execution_{int(start_time)}.{language}"
            try:
                temp_file.write_text(code)
                
                # Execute based on language
                if language.lower() == "python":
                    result = self._execute_python(temp_file, timeout)
                else:
                    result = self._execute_generic(temp_file, language, timeout)
                    
                end_time = time.time()
                execution_result = ExecutionResult(
                    success=result.get("success", False),
                    operation=operation,
                    start_time=start_time,
                    end_time=end_time,
                    output=result.get("output"),
                    error=result.get("error"),
                    metrics={
                        "execution_time": end_time - start_time,
                        "memory_usage": result.get("memory_usage", 0),
                        "cpu_usage": result.get("cpu_usage", 0)
                    }
                )
                
                self.record_operation_end(operation, execution_result.success, execution_result.error)
                return execution_result
                
            finally:
                if temp_file.exists():
                    temp_file.unlink()
                    
        except Exception as e:
            end_time = time.time()
            error_msg = f"Execution error: {str(e)}"
            logger.error(error_msg)
            
            self.record_operation_end(operation, False, error_msg)
            
            return ExecutionResult(
                success=False,
                operation=operation,
                start_time=start_time,
                end_time=end_time,
                error=error_msg,
                metrics={"execution_time": end_time - start_time}
            )
    
    def _execute_python(self, file_path: Path, timeout: float) -> Dict[str, Any]:
        """Execute Python code in a subprocess."""
        import subprocess
        import psutil
        
        try:
            process = subprocess.Popen(
                ["python", str(file_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            try:
                stdout, stderr = process.communicate(timeout=timeout)
                success = process.returncode == 0
                
                # Get resource usage
                process_info = psutil.Process(process.pid)
                memory_usage = process_info.memory_info().rss
                cpu_usage = process_info.cpu_percent()
                
                return {
                    "success": success,
                    "output": stdout if success else None,
                    "error": stderr if not success else None,
                    "memory_usage": memory_usage,
                    "cpu_usage": cpu_usage
                }
                
            except subprocess.TimeoutExpired:
                process.kill()
                return {
                    "success": False,
                    "error": f"Execution timed out after {timeout} seconds"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to execute Python code: {str(e)}"
            }
    
    def _execute_generic(self, file_path: Path, language: str, timeout: float) -> Dict[str, Any]:
        """Execute code for other languages."""
        # TODO: Implement execution for other languages
        return {
            "success": False,
            "error": f"Execution not implemented for language: {language}"
        }
    
    def get_status(self) -> Dict[str, Any]:
        """Get current monitoring status.
        
        Returns:
            Status information
        """
        with self.lock:
            return {
                'status': self.status.value,
                'current_operation': self.current_operation,
                'total_events': len(self.events),
                'monitoring_duration': (
                    time.time() - (self.start_time or time.time())
                    if self.status == ExecutionStatus.MONITORING
                    else 0
                ),
                'metrics': self.get_metrics()
            }
