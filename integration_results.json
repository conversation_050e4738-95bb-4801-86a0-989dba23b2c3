{"success_rate": 0.0, "passed": 0, "total": 12, "results": [{"step": "Agent Initialization", "success": false, "duration": 0.30060362815856934, "details": {"success": false, "error": "'FullStackProjectManager' object has no attribute '_get_react_package_json'"}}, {"step": "Tool Registration", "success": false, "duration": 0.0, "details": {"success": false, "error": "Agent not initialized"}}, {"step": "File Operations", "success": false, "duration": 0.0, "details": {"success": false, "error": "'NoneType' object has no attribute '_file_write'"}}, {"step": "Advanced Tools", "success": false, "duration": 0.0010144710540771484, "details": {"success": false, "error": "'NoneType' object has no attribute '_advanced_grep'"}}, {"step": "Workflow Management", "success": false, "duration": 0.0, "details": {"success": false, "error": "'NoneType' object has no attribute '_create_workflow_plan'"}}, {"step": "Full-Stack Projects", "success": false, "duration": 0.0, "details": {"success": false, "error": "'NoneType' object has no attribute '_create_fullstack_project'"}}, {"step": "Code Analysis", "success": false, "duration": 0.0, "details": {"success": false, "error": "'NoneType' object has no attribute '_analyze_code_complexity'"}}, {"step": "Terminal Management", "success": false, "duration": 0.0, "details": {"success": false, "error": "'NoneType' object has no attribute '_create_terminal_session'"}}, {"step": "Project Templates", "success": false, "duration": 0.0, "details": {"success": false, "error": "'NoneType' object has no attribute '_create_project'"}}, {"step": "Erro<PERSON>", "success": false, "duration": 0.0, "details": {"success": false, "error": "'NoneType' object has no attribute '_file_read'"}}, {"step": "Performance", "success": false, "duration": 0.0, "details": {"success": false, "error": "'NoneType' object has no attribute '_file_write'"}}, {"step": "Concurrent Operations", "success": false, "duration": 0.002093076705932617, "details": {"success": false, "error": "Concurrent operations failed: [\"'NoneType' object has no attribute '_file_write'\", \"'NoneType' object has no attribute '_file_write'\", \"'NoneType' object has no attribute '_file_write'\"]"}}]}