"""
Optimization Recommender for the AI Code Assistant.

This module provides smart optimization recommendations based on code analysis,
performance metrics, and best practices.
"""

import time
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class OptimizationRecommendation:
    """Represents an optimization recommendation."""
    recommendation_id: str
    category: str  # performance, memory, readability, maintainability, security
    priority: str  # critical, high, medium, low
    title: str
    description: str
    rationale: str
    implementation_steps: List[str]
    estimated_effort: str  # trivial, easy, medium, hard
    expected_benefit: str
    code_example: Optional[str]
    confidence: float
    applicable_languages: List[str]

@dataclass
class OptimizationPlan:
    """Represents a comprehensive optimization plan."""
    plan_id: str
    recommendations: List[OptimizationRecommendation]
    execution_order: List[str]  # recommendation IDs in order
    total_estimated_effort: str
    expected_overall_benefit: str
    risk_assessment: Dict[str, Any]
    timeline_estimate: str

class OptimizationRecommender:
    """Smart optimization recommendation system."""

    def __init__(self, workspace_dir: Path, model_manager=None):
        """Initialize the optimization recommender.
        
        Args:
            workspace_dir: The workspace directory
            model_manager: Optional model manager for AI-assisted recommendations
        """
        self.workspace_dir = workspace_dir
        self.model_manager = model_manager
        self.recommendation_templates: Dict[str, Dict[str, Any]] = {}
        self.recommendation_history: List[OptimizationPlan] = []
        self.effectiveness_tracking: Dict[str, float] = {}
        self.lock = threading.RLock()
        
        # Initialize recommendation templates
        self._initialize_recommendation_templates()

    def _initialize_recommendation_templates(self):
        """Initialize optimization recommendation templates."""
        self.recommendation_templates = {
            "algorithm_optimization": {
                "category": "performance",
                "priority": "high",
                "title": "Algorithm Optimization",
                "description": "Optimize algorithm complexity for better performance",
                "rationale": "Current algorithm has suboptimal time complexity",
                "implementation_steps": [
                    "Analyze current algorithm complexity",
                    "Research more efficient algorithms",
                    "Implement optimized version",
                    "Test performance improvements"
                ],
                "estimated_effort": "medium",
                "expected_benefit": "50-90% performance improvement",
                "applicable_languages": ["python", "javascript", "java", "cpp"]
            },
            "data_structure_optimization": {
                "category": "performance",
                "priority": "medium",
                "title": "Data Structure Optimization",
                "description": "Use more efficient data structures",
                "rationale": "Current data structures are not optimal for the operations",
                "implementation_steps": [
                    "Identify inefficient data structure usage",
                    "Select appropriate data structures",
                    "Refactor code to use new structures",
                    "Validate functionality"
                ],
                "estimated_effort": "easy",
                "expected_benefit": "20-60% performance improvement",
                "applicable_languages": ["python", "javascript", "java", "cpp"]
            },
            "memory_optimization": {
                "category": "memory",
                "priority": "medium",
                "title": "Memory Usage Optimization",
                "description": "Reduce memory consumption and prevent leaks",
                "rationale": "Code shows patterns of inefficient memory usage",
                "implementation_steps": [
                    "Identify memory-intensive operations",
                    "Implement memory-efficient alternatives",
                    "Add proper resource cleanup",
                    "Monitor memory usage"
                ],
                "estimated_effort": "medium",
                "expected_benefit": "30-70% memory reduction",
                "applicable_languages": ["python", "javascript", "java", "cpp"]
            },
            "caching_implementation": {
                "category": "performance",
                "priority": "high",
                "title": "Implement Caching",
                "description": "Add caching to reduce redundant computations",
                "rationale": "Repeated expensive operations detected",
                "implementation_steps": [
                    "Identify cacheable operations",
                    "Choose appropriate caching strategy",
                    "Implement cache mechanism",
                    "Add cache invalidation logic"
                ],
                "estimated_effort": "medium",
                "expected_benefit": "80-95% improvement for cached operations",
                "applicable_languages": ["python", "javascript", "java", "cpp"]
            },
            "code_refactoring": {
                "category": "maintainability",
                "priority": "medium",
                "title": "Code Refactoring",
                "description": "Improve code structure and readability",
                "rationale": "Code complexity is high and maintainability is low",
                "implementation_steps": [
                    "Identify complex code sections",
                    "Break down large functions",
                    "Extract common functionality",
                    "Improve naming and documentation"
                ],
                "estimated_effort": "medium",
                "expected_benefit": "Improved maintainability and reduced bugs",
                "applicable_languages": ["python", "javascript", "java", "cpp"]
            },
            "security_hardening": {
                "category": "security",
                "priority": "critical",
                "title": "Security Hardening",
                "description": "Address security vulnerabilities",
                "rationale": "Security issues detected in code",
                "implementation_steps": [
                    "Audit security vulnerabilities",
                    "Implement input validation",
                    "Add proper error handling",
                    "Use secure coding practices"
                ],
                "estimated_effort": "medium",
                "expected_benefit": "Elimination of security risks",
                "applicable_languages": ["python", "javascript", "java", "cpp"]
            }
        }

    def generate_recommendations(self, analysis_results: Dict[str, Any], 
                               language: str = "python") -> OptimizationPlan:
        """Generate optimization recommendations based on analysis results.
        
        Args:
            analysis_results: Results from various analysis components
            language: Programming language
            
        Returns:
            Comprehensive optimization plan
        """
        with self.lock:
            start_time = time.time()
            logger.info(f"Generating optimization recommendations for {language}")
            
            recommendations = []
            
            # Analyze performance bottlenecks
            if "performance_analysis" in analysis_results:
                perf_recommendations = self._generate_performance_recommendations(
                    analysis_results["performance_analysis"], language
                )
                recommendations.extend(perf_recommendations)
            
            # Analyze code quality issues
            if "code_analysis" in analysis_results:
                quality_recommendations = self._generate_quality_recommendations(
                    analysis_results["code_analysis"], language
                )
                recommendations.extend(quality_recommendations)
            
            # Analyze security issues
            if "error_detection" in analysis_results:
                security_recommendations = self._generate_security_recommendations(
                    analysis_results["error_detection"], language
                )
                recommendations.extend(security_recommendations)
            
            # Analyze refactoring opportunities
            if "refactoring_analysis" in analysis_results:
                refactoring_recommendations = self._generate_refactoring_recommendations(
                    analysis_results["refactoring_analysis"], language
                )
                recommendations.extend(refactoring_recommendations)
            
            # Prioritize and order recommendations
            execution_order = self._prioritize_recommendations(recommendations)
            
            # Calculate effort and benefits
            total_effort = self._calculate_total_effort(recommendations)
            overall_benefit = self._calculate_overall_benefit(recommendations)
            
            # Assess risks
            risk_assessment = self._assess_implementation_risks(recommendations)
            
            # Estimate timeline
            timeline = self._estimate_timeline(recommendations)
            
            plan = OptimizationPlan(
                plan_id=f"opt_plan_{int(time.time())}",
                recommendations=recommendations,
                execution_order=execution_order,
                total_estimated_effort=total_effort,
                expected_overall_benefit=overall_benefit,
                risk_assessment=risk_assessment,
                timeline_estimate=timeline
            )
            
            # Store in history
            self.recommendation_history.append(plan)
            
            logger.info(f"Generated {len(recommendations)} recommendations in {time.time() - start_time:.3f}s")
            return plan

    def _generate_performance_recommendations(self, performance_analysis: Dict[str, Any], 
                                            language: str) -> List[OptimizationRecommendation]:
        """Generate performance-specific recommendations."""
        recommendations = []
        
        bottlenecks = performance_analysis.get("bottlenecks", [])
        
        for bottleneck in bottlenecks:
            if bottleneck.get("bottleneck_type") == "nested_loops":
                rec = self._create_recommendation_from_template(
                    "algorithm_optimization", bottleneck, language
                )
                recommendations.append(rec)
            
            elif bottleneck.get("bottleneck_type") == "inefficient_data_structure":
                rec = self._create_recommendation_from_template(
                    "data_structure_optimization", bottleneck, language
                )
                recommendations.append(rec)
            
            elif bottleneck.get("bottleneck_type") == "recursive_without_memoization":
                rec = self._create_recommendation_from_template(
                    "caching_implementation", bottleneck, language
                )
                recommendations.append(rec)
        
        # Check overall performance score
        metrics = performance_analysis.get("metrics", {})
        if metrics.get("performance_score", 1.0) < 0.6:
            rec = self._create_recommendation_from_template(
                "memory_optimization", {"description": "Low performance score"}, language
            )
            recommendations.append(rec)
        
        return recommendations

    def _generate_quality_recommendations(self, code_analysis: Dict[str, Any], 
                                        language: str) -> List[OptimizationRecommendation]:
        """Generate code quality recommendations."""
        recommendations = []
        
        # Check maintainability score
        if code_analysis.get("maintainability_score", 1.0) < 0.7:
            rec = self._create_recommendation_from_template(
                "code_refactoring", {"description": "Low maintainability score"}, language
            )
            recommendations.append(rec)
        
        # Check complexity
        if code_analysis.get("complexity_score", 0.0) > 0.7:
            rec = OptimizationRecommendation(
                recommendation_id=f"complexity_reduction_{int(time.time())}",
                category="maintainability",
                priority="medium",
                title="Reduce Code Complexity",
                description="Break down complex functions and simplify logic",
                rationale="High code complexity detected",
                implementation_steps=[
                    "Identify complex functions",
                    "Break down into smaller functions",
                    "Simplify conditional logic",
                    "Add clear documentation"
                ],
                estimated_effort="medium",
                expected_benefit="Improved maintainability and reduced bugs",
                code_example=None,
                confidence=0.8,
                applicable_languages=[language]
            )
            recommendations.append(rec)
        
        return recommendations

    def _generate_security_recommendations(self, error_detection: Dict[str, Any], 
                                         language: str) -> List[OptimizationRecommendation]:
        """Generate security-specific recommendations."""
        recommendations = []
        
        errors = error_detection.get("errors", [])
        security_errors = [e for e in errors if e.get("category") == "security"]
        
        if security_errors:
            rec = self._create_recommendation_from_template(
                "security_hardening", {"description": "Security issues detected"}, language
            )
            recommendations.append(rec)
        
        return recommendations

    def _generate_refactoring_recommendations(self, refactoring_analysis: Dict[str, Any], 
                                            language: str) -> List[OptimizationRecommendation]:
        """Generate refactoring recommendations."""
        recommendations = []
        
        opportunities = refactoring_analysis.get("opportunities", [])
        
        if len(opportunities) > 3:
            rec = self._create_recommendation_from_template(
                "code_refactoring", {"description": "Multiple refactoring opportunities"}, language
            )
            recommendations.append(rec)
        
        return recommendations

    def _create_recommendation_from_template(self, template_name: str, context: Dict[str, Any], 
                                           language: str) -> OptimizationRecommendation:
        """Create a recommendation from a template."""
        template = self.recommendation_templates[template_name]
        
        return OptimizationRecommendation(
            recommendation_id=f"{template_name}_{int(time.time())}",
            category=template["category"],
            priority=template["priority"],
            title=template["title"],
            description=template["description"],
            rationale=f"{template['rationale']} - {context.get('description', '')}",
            implementation_steps=template["implementation_steps"],
            estimated_effort=template["estimated_effort"],
            expected_benefit=template["expected_benefit"],
            code_example=None,  # Could be generated based on context
            confidence=0.8,
            applicable_languages=template["applicable_languages"]
        )

    def _prioritize_recommendations(self, recommendations: List[OptimizationRecommendation]) -> List[str]:
        """Prioritize recommendations and return execution order."""
        # Sort by priority and confidence
        priority_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        
        sorted_recommendations = sorted(
            recommendations,
            key=lambda r: (priority_order.get(r.priority, 0), r.confidence),
            reverse=True
        )
        
        return [rec.recommendation_id for rec in sorted_recommendations]

    def _calculate_total_effort(self, recommendations: List[OptimizationRecommendation]) -> str:
        """Calculate total estimated effort."""
        effort_weights = {"trivial": 1, "easy": 2, "medium": 4, "hard": 8}
        
        total_weight = sum(effort_weights.get(rec.estimated_effort, 2) for rec in recommendations)
        
        if total_weight <= 5:
            return "easy"
        elif total_weight <= 15:
            return "medium"
        else:
            return "hard"

    def _calculate_overall_benefit(self, recommendations: List[OptimizationRecommendation]) -> str:
        """Calculate overall expected benefit."""
        high_impact_count = len([r for r in recommendations if r.priority in ["critical", "high"]])
        total_count = len(recommendations)
        
        if high_impact_count / max(total_count, 1) > 0.7:
            return "Significant improvement in performance, security, and maintainability"
        elif high_impact_count / max(total_count, 1) > 0.4:
            return "Moderate improvement in code quality and performance"
        else:
            return "Minor improvements in code quality"

    def _assess_implementation_risks(self, recommendations: List[OptimizationRecommendation]) -> Dict[str, Any]:
        """Assess risks of implementing recommendations."""
        risk_assessment = {
            "overall_risk": "low",
            "breaking_changes_risk": "low",
            "performance_regression_risk": "low",
            "complexity_increase_risk": "low",
            "mitigation_strategies": []
        }
        
        # Assess based on recommendation types and priorities
        critical_count = len([r for r in recommendations if r.priority == "critical"])
        hard_effort_count = len([r for r in recommendations if r.estimated_effort == "hard"])
        
        if critical_count > 2 or hard_effort_count > 1:
            risk_assessment["overall_risk"] = "medium"
            risk_assessment["mitigation_strategies"].extend([
                "Implement changes incrementally",
                "Comprehensive testing at each step",
                "Maintain rollback capability"
            ])
        
        # Check for algorithm changes
        algo_changes = len([r for r in recommendations if "algorithm" in r.title.lower()])
        if algo_changes > 0:
            risk_assessment["performance_regression_risk"] = "medium"
            risk_assessment["mitigation_strategies"].append("Performance benchmarking before and after")
        
        return risk_assessment

    def _estimate_timeline(self, recommendations: List[OptimizationRecommendation]) -> str:
        """Estimate implementation timeline."""
        effort_to_days = {"trivial": 0.5, "easy": 1, "medium": 3, "hard": 7}
        
        total_days = sum(effort_to_days.get(rec.estimated_effort, 1) for rec in recommendations)
        
        if total_days <= 2:
            return "1-2 days"
        elif total_days <= 7:
            return "1 week"
        elif total_days <= 14:
            return "2 weeks"
        else:
            return "3+ weeks"

    def track_recommendation_effectiveness(self, recommendation_id: str, effectiveness_score: float):
        """Track the effectiveness of implemented recommendations.
        
        Args:
            recommendation_id: ID of the implemented recommendation
            effectiveness_score: Score from 0.0 to 1.0 indicating effectiveness
        """
        with self.lock:
            self.effectiveness_tracking[recommendation_id] = effectiveness_score
            logger.info(f"Tracked effectiveness for {recommendation_id}: {effectiveness_score}")

    def get_recommendation_statistics(self) -> Dict[str, Any]:
        """Get recommendation statistics."""
        with self.lock:
            if not self.recommendation_history:
                return {"total_plans": 0}
            
            total_plans = len(self.recommendation_history)
            total_recommendations = sum(len(plan.recommendations) for plan in self.recommendation_history)
            
            # Count categories
            categories = {}
            priorities = {}
            
            for plan in self.recommendation_history:
                for rec in plan.recommendations:
                    categories[rec.category] = categories.get(rec.category, 0) + 1
                    priorities[rec.priority] = priorities.get(rec.priority, 0) + 1
            
            # Calculate average effectiveness
            avg_effectiveness = 0.0
            if self.effectiveness_tracking:
                avg_effectiveness = sum(self.effectiveness_tracking.values()) / len(self.effectiveness_tracking)
            
            return {
                "total_plans": total_plans,
                "total_recommendations": total_recommendations,
                "categories": categories,
                "priorities": priorities,
                "average_effectiveness": avg_effectiveness,
                "tracked_implementations": len(self.effectiveness_tracking),
                "templates_available": len(self.recommendation_templates)
            }

    def get_personalized_recommendations(self, user_preferences: Dict[str, Any], 
                                       analysis_results: Dict[str, Any]) -> OptimizationPlan:
        """Generate personalized recommendations based on user preferences.
        
        Args:
            user_preferences: User preferences for optimization focus
            analysis_results: Analysis results
            
        Returns:
            Personalized optimization plan
        """
        # Generate base recommendations
        base_plan = self.generate_recommendations(analysis_results)
        
        # Filter and adjust based on preferences
        focus_areas = user_preferences.get("focus_areas", ["performance", "maintainability"])
        max_effort = user_preferences.get("max_effort", "medium")
        
        # Filter recommendations
        filtered_recommendations = []
        for rec in base_plan.recommendations:
            if rec.category in focus_areas:
                # Adjust priority based on user focus
                if rec.category == user_preferences.get("primary_focus", "performance"):
                    if rec.priority == "medium":
                        rec.priority = "high"
                
                # Filter by effort level
                effort_levels = {"trivial": 1, "easy": 2, "medium": 3, "hard": 4}
                max_effort_level = effort_levels.get(max_effort, 3)
                rec_effort_level = effort_levels.get(rec.estimated_effort, 2)
                
                if rec_effort_level <= max_effort_level:
                    filtered_recommendations.append(rec)
        
        # Create personalized plan
        personalized_plan = OptimizationPlan(
            plan_id=f"personalized_{int(time.time())}",
            recommendations=filtered_recommendations,
            execution_order=self._prioritize_recommendations(filtered_recommendations),
            total_estimated_effort=self._calculate_total_effort(filtered_recommendations),
            expected_overall_benefit=self._calculate_overall_benefit(filtered_recommendations),
            risk_assessment=self._assess_implementation_risks(filtered_recommendations),
            timeline_estimate=self._estimate_timeline(filtered_recommendations)
        )
        
        return personalized_plan

    def clear_history(self):
        """Clear recommendation history."""
        with self.lock:
            self.recommendation_history.clear()
            self.effectiveness_tracking.clear()
            logger.info("Cleared optimization recommendation history")
