"""
Setup script for the Advanced AI Agent.
"""

from setuptools import setup, find_packages
import os
import re

# Read the version from __init__.py
with open("__init__.py", "r") as f:
    version_match = re.search(r'__version__\s*=\s*["\']([^"\']+)["\']', f.read())
    if version_match:
        version = version_match.group(1)
    else:
        version = "0.0.0"

# Read the long description from README.md
long_description = "Advanced AI Agent - A powerful terminal AI coding agent with Gemini API integration."

# Define the required packages
required_packages = [
    "click>=8.0.0",
    "prompt_toolkit>=3.0.0",
    "rich>=13.0.0",
    "requests>=2.28.0",
    "google-generativeai>=0.3.0",
    "pillow>=9.0.0",
    "beautifulsoup4>=4.11.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "lxml>=4.9.0",
]

# Define the optional packages
optional_packages = {
    "rag": ["faiss-cpu>=1.7.0", "sentence-transformers>=2.2.0", "numpy>=1.21.0"],
    "browser": ["selenium>=4.0.0", "playwright>=1.30.0"],
    "vector-db": ["chromadb>=0.4.0"],
    "ai-apis": ["openai>=1.0.0", "anthropic>=0.7.0"],
    "dev": ["pytest>=7.0.0", "pytest-cov>=4.0.0", "black>=22.0.0", "flake8>=5.0.0", "mypy>=1.0.0"],
    "all": [
        "faiss-cpu>=1.7.0", "sentence-transformers>=2.2.0", "numpy>=1.21.0",
        "selenium>=4.0.0", "playwright>=1.30.0", "chromadb>=0.4.0",
        "openai>=1.0.0", "anthropic>=0.7.0"
    ]
}

setup(
    name="advanced_ai_agent",
    version=version,
    description="A powerful terminal AI coding agent with Gemini API integration",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="AI Developer",
    author_email="<EMAIL>",
    url="https://github.com/example/advanced_ai_agent",
    packages=find_packages(),
    py_modules=[
        "agent", "cli", "config", "conversation", "main"
    ],
    entry_points={
        "console_scripts": [
            "advanced-ai-agent=main:main",
        ],
    },
    install_requires=required_packages,
    extras_require=optional_packages,
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.8",
)
