"""
Logging utilities for the Advanced AI Agent.
"""

import os
import logging
from pathlib import Path
from typing import Optional

def setup_logger(log_dir: Optional[Path] = None, debug: bool = False, silent: bool = False) -> logging.Logger:
    """Set up the logger.

    Args:
        log_dir: The directory to save logs to. If None, will not save logs to file.
        debug: Whether to enable debug logging.
        silent: Whether to suppress console output completely.

    Returns:
        The logger.
    """
    # Create the logger
    logger = logging.getLogger("advanced_ai_agent")
    logger.setLevel(logging.DEBUG if debug else logging.INFO)

    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create a formatter
    formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    # Create a console handler if not in silent mode
    if not silent:
        console_handler = logging.StreamHandler()
        # Only show ERROR level or higher in console by default
        console_handler.setLevel(logging.DEBUG if debug else logging.ERROR)
        console_handler.setFormatter(formatter)

        # Add the console handler to the logger
        logger.addHandler(console_handler)

    # Create a file handler if log_dir is provided
    if log_dir is not None:
        # Create the log directory if it doesn't exist
        log_dir.mkdir(parents=True, exist_ok=True)

        # Create the file handler
        file_handler = logging.FileHandler(log_dir / "advanced_ai_agent.log")
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)

        # Add the file handler to the logger
        logger.addHandler(file_handler)

    return logger

def get_logger() -> logging.Logger:
    """Get the logger.

    Returns:
        The logger.
    """
    return logging.getLogger("advanced_ai_agent")
