"""
Learning System for the AI Code Assistant.

This module provides self-improving learning capabilities, analyzing patterns
from past interactions and continuously improving recommendations and analysis.
"""

import time
import json
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class LearningPattern:
    """Represents a learned pattern."""
    pattern_id: str
    pattern_type: str  # code_pattern, error_pattern, optimization_pattern
    description: str
    context: Dict[str, Any]
    confidence: float
    usage_count: int
    success_rate: float
    last_updated: float
    effectiveness_score: float

@dataclass
class LearningInsight:
    """Represents a learning insight."""
    insight_id: str
    insight_type: str  # performance, quality, user_behavior
    description: str
    supporting_evidence: List[str]
    confidence: float
    actionable_recommendations: List[str]
    impact_assessment: str

@dataclass
class LearningMetrics:
    """Metrics for the learning system."""
    total_interactions: int
    patterns_learned: int
    insights_generated: int
    accuracy_improvement: float
    recommendation_effectiveness: float
    user_satisfaction_trend: float

class LearningSystem:
    """Self-improving learning system for the AI Code Assistant."""

    def __init__(self, workspace_dir: Path, model_manager=None):
        """Initialize the learning system.
        
        Args:
            workspace_dir: The workspace directory
            model_manager: Optional model manager for AI-assisted learning
        """
        self.workspace_dir = workspace_dir
        self.model_manager = model_manager
        self.learned_patterns: Dict[str, LearningPattern] = {}
        self.insights: List[LearningInsight] = []
        self.interaction_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, List[float]] = defaultdict(list)
        self.user_feedback: List[Dict[str, Any]] = []
        self.lock = threading.RLock()
        
        # Learning configuration
        self.learning_config = {
            "min_pattern_confidence": 0.6,
            "pattern_decay_rate": 0.95,
            "learning_rate": 0.1,
            "max_patterns": 1000,
            "insight_generation_threshold": 10,
        }
        
        # Initialize learning components
        self._initialize_learning_components()

    def _initialize_learning_components(self):
        """Initialize learning system components."""
        # Load existing patterns if available
        self._load_learned_patterns()
        
        # Initialize pattern categories
        self.pattern_categories = {
            "code_quality": {
                "description": "Patterns related to code quality issues",
                "weight": 0.8,
                "patterns": []
            },
            "performance": {
                "description": "Patterns related to performance optimization",
                "weight": 0.9,
                "patterns": []
            },
            "user_preferences": {
                "description": "Patterns in user behavior and preferences",
                "weight": 0.7,
                "patterns": []
            },
            "error_resolution": {
                "description": "Patterns in error detection and resolution",
                "weight": 0.85,
                "patterns": []
            }
        }

    def learn_from_interaction(self, interaction_data: Dict[str, Any]):
        """Learn from a user interaction.
        
        Args:
            interaction_data: Data from the interaction including request, response, and feedback
        """
        with self.lock:
            logger.info("Learning from new interaction")
            
            # Store interaction
            interaction_data["timestamp"] = time.time()
            self.interaction_history.append(interaction_data)
            
            # Extract patterns
            patterns = self._extract_patterns_from_interaction(interaction_data)
            
            # Update existing patterns or create new ones
            for pattern in patterns:
                self._update_or_create_pattern(pattern)
            
            # Update performance metrics
            self._update_performance_metrics(interaction_data)
            
            # Generate insights if threshold reached
            if len(self.interaction_history) % self.learning_config["insight_generation_threshold"] == 0:
                self._generate_insights()
            
            # Decay old patterns
            self._decay_patterns()
            
            logger.info(f"Learning completed. Total patterns: {len(self.learned_patterns)}")

    def _extract_patterns_from_interaction(self, interaction_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract learning patterns from an interaction."""
        patterns = []
        
        request = interaction_data.get("request", {})
        response = interaction_data.get("response", {})
        feedback = interaction_data.get("feedback", {})
        
        # Extract code quality patterns
        if "code_analysis" in response:
            code_analysis = response["code_analysis"]
            if code_analysis.get("maintainability_score", 0) < 0.6:
                patterns.append({
                    "type": "code_quality",
                    "subtype": "low_maintainability",
                    "context": {
                        "language": request.get("language", "unknown"),
                        "complexity": code_analysis.get("complexity_score", 0),
                        "line_count": len(request.get("code", "").splitlines())
                    },
                    "outcome": feedback.get("satisfaction", 0.5)
                })
        
        # Extract performance patterns
        if "performance_analysis" in response:
            perf_analysis = response["performance_analysis"]
            bottlenecks = perf_analysis.get("bottlenecks", [])
            if bottlenecks:
                for bottleneck in bottlenecks:
                    patterns.append({
                        "type": "performance",
                        "subtype": bottleneck.get("bottleneck_type", "unknown"),
                        "context": {
                            "language": request.get("language", "unknown"),
                            "severity": bottleneck.get("severity", "medium"),
                            "impact": bottleneck.get("impact_score", 0.5)
                        },
                        "outcome": feedback.get("usefulness", 0.5)
                    })
        
        # Extract user preference patterns
        user_prefs = request.get("preferences", {})
        if user_prefs:
            patterns.append({
                "type": "user_preferences",
                "subtype": "preference_pattern",
                "context": {
                    "focus_areas": user_prefs.get("focus_areas", []),
                    "max_effort": user_prefs.get("max_effort", "medium"),
                    "request_type": request.get("request_type", "unknown")
                },
                "outcome": feedback.get("satisfaction", 0.5)
            })
        
        # Extract error resolution patterns
        if "error_detection" in response:
            error_detection = response["error_detection"]
            errors = error_detection.get("errors", [])
            if errors:
                for error in errors:
                    patterns.append({
                        "type": "error_resolution",
                        "subtype": error.get("error_type", "unknown"),
                        "context": {
                            "language": request.get("language", "unknown"),
                            "severity": error.get("severity", "medium"),
                            "category": error.get("category", "unknown")
                        },
                        "outcome": feedback.get("accuracy", 0.5)
                    })
        
        return patterns

    def _update_or_create_pattern(self, pattern_data: Dict[str, Any]):
        """Update existing pattern or create new one."""
        pattern_key = f"{pattern_data['type']}_{pattern_data['subtype']}"
        
        if pattern_key in self.learned_patterns:
            # Update existing pattern
            existing_pattern = self.learned_patterns[pattern_key]
            existing_pattern.usage_count += 1
            
            # Update success rate using exponential moving average
            alpha = self.learning_config["learning_rate"]
            new_outcome = pattern_data["outcome"]
            existing_pattern.success_rate = (
                (1 - alpha) * existing_pattern.success_rate + alpha * new_outcome
            )
            
            # Update confidence based on usage count and success rate
            existing_pattern.confidence = min(
                existing_pattern.confidence + 0.1 * existing_pattern.success_rate,
                1.0
            )
            
            existing_pattern.last_updated = time.time()
            
        else:
            # Create new pattern
            new_pattern = LearningPattern(
                pattern_id=pattern_key,
                pattern_type=pattern_data["type"],
                description=f"{pattern_data['type']} pattern: {pattern_data['subtype']}",
                context=pattern_data["context"],
                confidence=0.5,
                usage_count=1,
                success_rate=pattern_data["outcome"],
                last_updated=time.time(),
                effectiveness_score=pattern_data["outcome"]
            )
            
            self.learned_patterns[pattern_key] = new_pattern
            
            # Add to appropriate category
            if pattern_data["type"] in self.pattern_categories:
                self.pattern_categories[pattern_data["type"]]["patterns"].append(pattern_key)

    def _update_performance_metrics(self, interaction_data: Dict[str, Any]):
        """Update performance metrics based on interaction."""
        response = interaction_data.get("response", {})
        feedback = interaction_data.get("feedback", {})
        
        # Track confidence scores
        if "confidence_score" in response:
            self.performance_metrics["confidence_scores"].append(response["confidence_score"])
        
        # Track user satisfaction
        if "satisfaction" in feedback:
            self.performance_metrics["user_satisfaction"].append(feedback["satisfaction"])
        
        # Track processing time
        if "processing_time" in response:
            self.performance_metrics["processing_times"].append(response["processing_time"])
        
        # Track accuracy
        if "accuracy" in feedback:
            self.performance_metrics["accuracy_scores"].append(feedback["accuracy"])

    def _generate_insights(self):
        """Generate insights from learned patterns and metrics."""
        logger.info("Generating learning insights")
        
        # Analyze pattern effectiveness
        self._analyze_pattern_effectiveness()
        
        # Analyze user behavior trends
        self._analyze_user_behavior_trends()
        
        # Analyze performance trends
        self._analyze_performance_trends()
        
        # Generate actionable insights
        self._generate_actionable_insights()

    def _analyze_pattern_effectiveness(self):
        """Analyze the effectiveness of learned patterns."""
        effective_patterns = []
        ineffective_patterns = []
        
        for pattern in self.learned_patterns.values():
            if pattern.usage_count >= 5:  # Minimum usage for reliable analysis
                if pattern.success_rate > 0.7:
                    effective_patterns.append(pattern)
                elif pattern.success_rate < 0.4:
                    ineffective_patterns.append(pattern)
        
        if effective_patterns:
            insight = LearningInsight(
                insight_id=f"pattern_effectiveness_{int(time.time())}",
                insight_type="performance",
                description=f"Identified {len(effective_patterns)} highly effective patterns",
                supporting_evidence=[p.pattern_id for p in effective_patterns[:5]],
                confidence=0.8,
                actionable_recommendations=[
                    "Prioritize these pattern types in future analysis",
                    "Use these patterns as templates for similar situations"
                ],
                impact_assessment="High - can improve recommendation accuracy"
            )
            self.insights.append(insight)
        
        if ineffective_patterns:
            insight = LearningInsight(
                insight_id=f"pattern_ineffectiveness_{int(time.time())}",
                insight_type="quality",
                description=f"Identified {len(ineffective_patterns)} ineffective patterns",
                supporting_evidence=[p.pattern_id for p in ineffective_patterns[:5]],
                confidence=0.7,
                actionable_recommendations=[
                    "Review and refine these pattern detection methods",
                    "Consider removing or modifying these patterns"
                ],
                impact_assessment="Medium - can reduce false positives"
            )
            self.insights.append(insight)

    def _analyze_user_behavior_trends(self):
        """Analyze trends in user behavior and preferences."""
        if len(self.interaction_history) < 10:
            return
        
        recent_interactions = self.interaction_history[-20:]
        
        # Analyze request type preferences
        request_types = [i.get("request", {}).get("request_type") for i in recent_interactions]
        request_type_counts = {}
        for req_type in request_types:
            if req_type:
                request_type_counts[req_type] = request_type_counts.get(req_type, 0) + 1
        
        if request_type_counts:
            most_common = max(request_type_counts, key=request_type_counts.get)
            insight = LearningInsight(
                insight_id=f"user_behavior_{int(time.time())}",
                insight_type="user_behavior",
                description=f"User shows preference for {most_common} requests",
                supporting_evidence=[f"{most_common}: {request_type_counts[most_common]} occurrences"],
                confidence=0.6,
                actionable_recommendations=[
                    f"Optimize {most_common} analysis pipeline",
                    f"Provide more detailed {most_common} recommendations"
                ],
                impact_assessment="Medium - can improve user experience"
            )
            self.insights.append(insight)

    def _analyze_performance_trends(self):
        """Analyze performance trends over time."""
        if "user_satisfaction" in self.performance_metrics:
            satisfaction_scores = self.performance_metrics["user_satisfaction"]
            if len(satisfaction_scores) >= 10:
                recent_avg = np.mean(satisfaction_scores[-10:])
                overall_avg = np.mean(satisfaction_scores)
                
                if recent_avg > overall_avg + 0.1:
                    insight = LearningInsight(
                        insight_id=f"performance_improvement_{int(time.time())}",
                        insight_type="performance",
                        description="User satisfaction is improving over time",
                        supporting_evidence=[f"Recent avg: {recent_avg:.2f}, Overall avg: {overall_avg:.2f}"],
                        confidence=0.8,
                        actionable_recommendations=[
                            "Continue current learning strategies",
                            "Analyze what changes led to improvement"
                        ],
                        impact_assessment="High - validates learning effectiveness"
                    )
                    self.insights.append(insight)

    def _generate_actionable_insights(self):
        """Generate actionable insights for system improvement."""
        # Analyze most common error types
        error_patterns = [p for p in self.learned_patterns.values() if p.pattern_type == "error_resolution"]
        if error_patterns:
            most_common_errors = sorted(error_patterns, key=lambda p: p.usage_count, reverse=True)[:3]
            
            insight = LearningInsight(
                insight_id=f"common_errors_{int(time.time())}",
                insight_type="quality",
                description="Identified most common error patterns",
                supporting_evidence=[p.pattern_id for p in most_common_errors],
                confidence=0.9,
                actionable_recommendations=[
                    "Improve detection accuracy for these error types",
                    "Develop specialized handlers for common errors",
                    "Create preventive suggestions for these patterns"
                ],
                impact_assessment="High - can significantly improve error detection"
            )
            self.insights.append(insight)

    def _decay_patterns(self):
        """Apply decay to old patterns to reduce their influence."""
        current_time = time.time()
        decay_rate = self.learning_config["pattern_decay_rate"]
        
        patterns_to_remove = []
        
        for pattern_id, pattern in self.learned_patterns.items():
            # Apply time-based decay
            time_since_update = current_time - pattern.last_updated
            if time_since_update > 86400 * 30:  # 30 days
                pattern.confidence *= decay_rate
                
                # Remove patterns with very low confidence
                if pattern.confidence < self.learning_config["min_pattern_confidence"]:
                    patterns_to_remove.append(pattern_id)
        
        # Remove low-confidence patterns
        for pattern_id in patterns_to_remove:
            del self.learned_patterns[pattern_id]
            logger.info(f"Removed low-confidence pattern: {pattern_id}")

    def get_learned_recommendations(self, context: Dict[str, Any]) -> List[str]:
        """Get recommendations based on learned patterns.
        
        Args:
            context: Context for generating recommendations
            
        Returns:
            List of learned recommendations
        """
        with self.lock:
            recommendations = []
            
            # Find relevant patterns
            relevant_patterns = self._find_relevant_patterns(context)
            
            # Generate recommendations from patterns
            for pattern in relevant_patterns:
                if pattern.confidence > 0.7 and pattern.success_rate > 0.6:
                    rec = self._generate_recommendation_from_pattern(pattern, context)
                    if rec:
                        recommendations.append(rec)
            
            return recommendations

    def _find_relevant_patterns(self, context: Dict[str, Any]) -> List[LearningPattern]:
        """Find patterns relevant to the given context."""
        relevant_patterns = []
        
        language = context.get("language", "unknown")
        request_type = context.get("request_type", "unknown")
        
        for pattern in self.learned_patterns.values():
            # Check language match
            pattern_language = pattern.context.get("language", "unknown")
            if pattern_language == language or pattern_language == "unknown":
                # Check request type relevance
                if request_type in ["analyze", "debug"] and pattern.pattern_type in ["code_quality", "error_resolution"]:
                    relevant_patterns.append(pattern)
                elif request_type == "optimize" and pattern.pattern_type == "performance":
                    relevant_patterns.append(pattern)
                elif pattern.pattern_type == "user_preferences":
                    relevant_patterns.append(pattern)
        
        # Sort by effectiveness
        relevant_patterns.sort(key=lambda p: p.effectiveness_score, reverse=True)
        
        return relevant_patterns[:10]  # Limit to top 10

    def _generate_recommendation_from_pattern(self, pattern: LearningPattern, 
                                            context: Dict[str, Any]) -> Optional[str]:
        """Generate a recommendation from a learned pattern."""
        if pattern.pattern_type == "performance":
            return f"Based on learned patterns, consider optimizing {pattern.pattern_id.split('_')[1]} (confidence: {pattern.confidence:.2f})"
        elif pattern.pattern_type == "code_quality":
            return f"Learned pattern suggests focusing on {pattern.pattern_id.split('_')[1]} improvements"
        elif pattern.pattern_type == "error_resolution":
            return f"Common error pattern detected: {pattern.pattern_id.split('_')[1]} - apply learned resolution strategies"
        
        return None

    def get_learning_metrics(self) -> LearningMetrics:
        """Get comprehensive learning metrics."""
        with self.lock:
            total_interactions = len(self.interaction_history)
            patterns_learned = len(self.learned_patterns)
            insights_generated = len(self.insights)
            
            # Calculate accuracy improvement
            accuracy_scores = self.performance_metrics.get("accuracy_scores", [])
            accuracy_improvement = 0.0
            if len(accuracy_scores) >= 10:
                recent_accuracy = np.mean(accuracy_scores[-10:])
                initial_accuracy = np.mean(accuracy_scores[:10])
                accuracy_improvement = recent_accuracy - initial_accuracy
            
            # Calculate recommendation effectiveness
            satisfaction_scores = self.performance_metrics.get("user_satisfaction", [])
            recommendation_effectiveness = np.mean(satisfaction_scores) if satisfaction_scores else 0.0
            
            # Calculate satisfaction trend
            satisfaction_trend = 0.0
            if len(satisfaction_scores) >= 10:
                recent_satisfaction = np.mean(satisfaction_scores[-10:])
                overall_satisfaction = np.mean(satisfaction_scores)
                satisfaction_trend = recent_satisfaction - overall_satisfaction
            
            return LearningMetrics(
                total_interactions=total_interactions,
                patterns_learned=patterns_learned,
                insights_generated=insights_generated,
                accuracy_improvement=accuracy_improvement,
                recommendation_effectiveness=recommendation_effectiveness,
                user_satisfaction_trend=satisfaction_trend
            )

    def _load_learned_patterns(self):
        """Load previously learned patterns from storage."""
        patterns_file = self.workspace_dir / "learned_patterns.json"
        if patterns_file.exists():
            try:
                with open(patterns_file, 'r') as f:
                    patterns_data = json.load(f)
                
                for pattern_id, pattern_dict in patterns_data.items():
                    pattern = LearningPattern(**pattern_dict)
                    self.learned_patterns[pattern_id] = pattern
                
                logger.info(f"Loaded {len(self.learned_patterns)} learned patterns")
            except Exception as e:
                logger.error(f"Error loading learned patterns: {e}")

    def save_learned_patterns(self):
        """Save learned patterns to storage."""
        with self.lock:
            patterns_file = self.workspace_dir / "learned_patterns.json"
            try:
                patterns_data = {}
                for pattern_id, pattern in self.learned_patterns.items():
                    patterns_data[pattern_id] = asdict(pattern)
                
                with open(patterns_file, 'w') as f:
                    json.dump(patterns_data, f, indent=2)
                
                logger.info(f"Saved {len(self.learned_patterns)} learned patterns")
            except Exception as e:
                logger.error(f"Error saving learned patterns: {e}")

    def clear_learning_data(self):
        """Clear all learning data."""
        with self.lock:
            self.learned_patterns.clear()
            self.insights.clear()
            self.interaction_history.clear()
            self.performance_metrics.clear()
            self.user_feedback.clear()
            logger.info("Cleared all learning data")

    def analyze_request(self, request: str) -> Dict[str, Any]:
        """Analyze a user request to determine complexity and requirements."""
        analysis = {
            'complexity': 'Standard',
            'estimated_steps': 3,
            'required_tools': [],
            'confidence': 0.7
        }

        # Analyze complexity based on keywords and patterns
        complexity_indicators = {
            'simple': ['list', 'show', 'display', 'get'],
            'standard': ['create', 'write', 'read', 'search', 'find'],
            'complex': ['analyze', 'optimize', 'refactor', 'debug'],
            'advanced': ['integrate', 'deploy', 'architect', 'design']
        }

        request_lower = request.lower()

        for complexity, keywords in complexity_indicators.items():
            if any(keyword in request_lower for keyword in keywords):
                analysis['complexity'] = complexity.title()
                break

        # Determine required tools
        tool_keywords = {
            'file': ['file', 'read', 'write', 'save', 'load'],
            'code': ['code', 'execute', 'run', 'compile'],
            'web': ['web', 'url', 'http', 'website'],
            'search': ['search', 'find', 'look'],
            'codebase': ['codebase', 'project', 'repository'],
            'vision': ['image', 'screenshot', 'visual']
        }

        for tool, keywords in tool_keywords.items():
            if any(keyword in request_lower for keyword in keywords):
                analysis['required_tools'].append(tool)

        # Estimate steps based on complexity
        complexity_steps = {
            'Simple': 2,
            'Standard': 3,
            'Complex': 5,
            'Advanced': 8
        }
        analysis['estimated_steps'] = complexity_steps.get(analysis['complexity'], 3)

        return analysis

    def validate_results(self, original_request: str, results: List[Dict]) -> Dict[str, Any]:
        """Validate execution results against original requirements."""
        validation = {
            'passed': True,
            'missing': [],
            'recommendations': [],
            'confidence': 0.8
        }

        # Check if any steps failed
        failed_steps = [r for r in results if not r.get('success', True)]
        if failed_steps:
            validation['passed'] = False
            validation['missing'].append('Some execution steps failed')
            validation['recommendations'].append('Review and retry failed steps')

        # Basic keyword matching for requirement validation
        request_lower = original_request.lower()

        # Check for file operations
        if any(word in request_lower for word in ['file', 'save', 'write']):
            file_operations = [r for r in results if 'file' in r.get('description', '').lower()]
            if not file_operations:
                validation['missing'].append('File operations not completed')

        # Check for code execution
        if any(word in request_lower for word in ['code', 'execute', 'run']):
            code_operations = [r for r in results if 'code' in r.get('description', '').lower()]
            if not code_operations:
                validation['missing'].append('Code execution not completed')

        # Adjust confidence based on validation
        if validation['missing']:
            validation['confidence'] = max(0.2, validation['confidence'] - 0.2 * len(validation['missing']))

        return validation

    def learn_from_execution(self, request: str, results: List[Dict], validation: Dict) -> None:
        """Learn from an execution to improve future performance."""
        # Record the interaction
        interaction = {
            'timestamp': time.time(),
            'request': request,
            'results': results,
            'validation': validation,
            'success_rate': sum(1 for r in results if r.get('success', True)) / len(results) if results else 0
        }

        self.interaction_history.append(interaction)

        # Update patterns based on success/failure
        if validation.get('passed', False):
            # Learn successful patterns
            self._learn_successful_pattern(request, results)
        else:
            # Learn from failures
            self._learn_failure_pattern(request, results, validation)

        # Update performance metrics
        self._update_performance_metrics(interaction)

        logger.info(f"Learned from execution: {request[:50]}... Success: {validation.get('passed', False)}")

    def _learn_successful_pattern(self, request: str, results: List[Dict]) -> None:
        """Learn from successful execution patterns."""
        pattern_id = f"success_{hash(request) % 10000}"

        pattern = LearningPattern(
            pattern_id=pattern_id,
            pattern_type="success_pattern",
            description=f"Successful execution for: {request[:100]}",
            context={
                'request_type': self._classify_request(request),
                'steps_count': len(results),
                'tools_used': list(set(r.get('tool', 'unknown') for r in results))
            },
            confidence=0.8,
            usage_count=1,
            success_rate=1.0,
            last_updated=time.time(),
            effectiveness_score=0.9
        )

        self.learned_patterns[pattern_id] = pattern

    def _learn_failure_pattern(self, request: str, results: List[Dict], validation: Dict) -> None:
        """Learn from failed execution patterns."""
        pattern_id = f"failure_{hash(request) % 10000}"

        failed_steps = [r for r in results if not r.get('success', True)]

        pattern = LearningPattern(
            pattern_id=pattern_id,
            pattern_type="failure_pattern",
            description=f"Failed execution for: {request[:100]}",
            context={
                'request_type': self._classify_request(request),
                'failure_points': [r.get('description', '') for r in failed_steps],
                'missing_requirements': validation.get('missing', [])
            },
            confidence=0.7,
            usage_count=1,
            success_rate=0.0,
            last_updated=time.time(),
            effectiveness_score=0.3
        )

        self.learned_patterns[pattern_id] = pattern

    def _classify_request(self, request: str) -> str:
        """Classify the type of request."""
        request_lower = request.lower()

        if any(word in request_lower for word in ['file', 'read', 'write']):
            return 'file_operation'
        elif any(word in request_lower for word in ['code', 'execute', 'run']):
            return 'code_execution'
        elif any(word in request_lower for word in ['search', 'find']):
            return 'search_operation'
        elif any(word in request_lower for word in ['web', 'url']):
            return 'web_operation'
        else:
            return 'general_request'

    def _update_performance_metrics(self, interaction: Dict) -> None:
        """Update performance metrics based on interaction."""
        request_type = self._classify_request(interaction['request'])

        if request_type not in self.performance_metrics:
            self.performance_metrics[request_type] = {
                'total_requests': 0,
                'successful_requests': 0,
                'average_steps': 0,
                'average_success_rate': 0
            }

        metrics = self.performance_metrics[request_type]
        metrics['total_requests'] += 1

        if interaction['validation'].get('passed', False):
            metrics['successful_requests'] += 1

        # Update averages
        metrics['average_success_rate'] = metrics['successful_requests'] / metrics['total_requests']

        steps_count = len(interaction['results'])
        metrics['average_steps'] = (metrics['average_steps'] * (metrics['total_requests'] - 1) + steps_count) / metrics['total_requests']
