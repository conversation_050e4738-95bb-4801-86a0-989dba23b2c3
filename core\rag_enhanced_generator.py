"""
RAG-Enhanced Generator for the AI Code Assistant.

This module implements Retrieval-Augmented Generation (RAG) with
Retrieval-Augmented Thinking (RAT) capabilities for context-aware code generation.
"""

import time
import json
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class CodeContext:
    """Represents code context for generation."""
    file_path: str
    surrounding_code: str
    imports: List[str]
    functions: List[str]
    classes: List[str]
    variables: List[str]
    intent: str
    constraints: List[str]

@dataclass
class RetrievalResult:
    """Result from code retrieval."""
    code_snippet: str
    file_path: str
    relevance_score: float
    context_match: str
    usage_pattern: str
    metadata: Dict[str, Any]

@dataclass
class ThinkingStep:
    """Represents a step in the thinking process."""
    step_number: int
    thought: str
    reasoning: str
    retrieved_examples: List[RetrievalResult]
    intermediate_code: Optional[str]
    confidence: float

@dataclass
class GenerationResult:
    """Result of code generation."""
    generated_code: str
    language: str
    confidence_score: float
    thinking_process: List[ThinkingStep]
    retrieved_context: List[RetrievalResult]
    alternatives: List[str]
    explanations: List[str]
    potential_issues: List[str]
    optimization_suggestions: List[str]

class RAGEnhancedGenerator:
    """RAG-enhanced code generator with thinking capabilities."""

    def __init__(self, model_manager, semantic_indexer=None, workspace_dir: Optional[Path] = None):
        """Initialize the RAG-enhanced generator.

        Args:
            model_manager: The model manager for AI interactions
            semantic_indexer: Optional semantic indexer for code retrieval
            workspace_dir: Optional workspace directory
        """
        self.model_manager = model_manager
        self.semantic_indexer = semantic_indexer
        self.workspace_dir = workspace_dir or Path.cwd()
        self.code_knowledge_base: Dict[str, Any] = {}
        self.pattern_library: Dict[str, List[str]] = {}
        self.generation_history: List[GenerationResult] = []
        self.lock = threading.RLock()

        # Initialize pattern library
        self._initialize_pattern_library()

        # RAG configuration
        self.rag_config = {
            "max_retrieved_examples": 5,
            "relevance_threshold": 0.3,
            "thinking_steps": 3,
            "context_window": 1000,
            "diversity_factor": 0.3,
        }

    def _initialize_pattern_library(self):
        """Initialize the code pattern library."""
        self.pattern_library = {
            "data_structures": [
                "list comprehension",
                "dictionary operations",
                "set operations",
                "tree traversal",
                "graph algorithms"
            ],
            "algorithms": [
                "sorting algorithms",
                "search algorithms",
                "dynamic programming",
                "recursion patterns",
                "optimization algorithms"
            ],
            "design_patterns": [
                "singleton pattern",
                "factory pattern",
                "observer pattern",
                "decorator pattern",
                "strategy pattern"
            ],
            "web_development": [
                "API endpoints",
                "database queries",
                "authentication",
                "error handling",
                "middleware"
            ],
            "data_processing": [
                "file I/O",
                "data parsing",
                "data transformation",
                "data validation",
                "batch processing"
            ]
        }

    def generate_code_with_rag(self, prompt: str, context: Optional[CodeContext] = None,
                              language: str = "python", max_thinking_steps: int = 3) -> GenerationResult:
        """Generate code using RAG with thinking process.

        Args:
            prompt: The code generation prompt
            context: Optional code context
            language: Target programming language
            max_thinking_steps: Maximum number of thinking steps

        Returns:
            Generation result with thinking process
        """
        with self.lock:
            logger.info(f"Generating {language} code with RAG: {prompt[:100]}...")

            start_time = time.time()
            thinking_process = []

            # Step 1: Understand the intent and retrieve relevant examples
            step1 = self._thinking_step_1_understand_and_retrieve(prompt, context, language)
            thinking_process.append(step1)

            # Step 2: Analyze patterns and plan the solution
            step2 = self._thinking_step_2_analyze_and_plan(prompt, step1, language)
            thinking_process.append(step2)

            # Step 3: Generate and refine the code
            step3 = self._thinking_step_3_generate_and_refine(prompt, step1, step2, language)
            thinking_process.append(step3)

            # Additional thinking steps if needed
            current_step = step3
            for i in range(3, max_thinking_steps):
                if current_step.confidence >= 0.8:
                    break

                next_step = self._additional_thinking_step(prompt, thinking_process, language, i + 1)
                thinking_process.append(next_step)
                current_step = next_step

            # Generate final code
            final_code = self._generate_final_code(prompt, thinking_process, context, language)

            # Calculate overall confidence
            confidence_score = self._calculate_overall_confidence(thinking_process)

            # Get all retrieved context
            all_retrieved = []
            for step in thinking_process:
                all_retrieved.extend(step.retrieved_examples)

            # Generate alternatives
            alternatives = self._generate_alternatives(prompt, thinking_process, language)

            # Generate explanations
            explanations = self._generate_explanations(final_code, thinking_process, language)

            # Identify potential issues
            potential_issues = self._identify_potential_issues(final_code, language)

            # Generate optimization suggestions
            optimization_suggestions = self._generate_optimization_suggestions(final_code, language)

            result = GenerationResult(
                generated_code=final_code,
                language=language,
                confidence_score=confidence_score,
                thinking_process=thinking_process,
                retrieved_context=all_retrieved,
                alternatives=alternatives,
                explanations=explanations,
                potential_issues=potential_issues,
                optimization_suggestions=optimization_suggestions
            )

            # Store in history
            self.generation_history.append(result)

            execution_time = time.time() - start_time
            logger.info(f"Code generation completed in {execution_time:.2f}s with confidence {confidence_score:.3f}")

            return result

    def _thinking_step_1_understand_and_retrieve(self, prompt: str, context: Optional[CodeContext],
                                               language: str) -> ThinkingStep:
        """First thinking step: understand intent and retrieve examples."""

        # Analyze the prompt to understand intent
        intent_analysis = self._analyze_intent(prompt)

        # Retrieve relevant code examples
        retrieved_examples = self._retrieve_relevant_examples(prompt, intent_analysis, language)

        # Generate initial thoughts
        thought = f"Understanding the request: {intent_analysis['primary_intent']}"
        reasoning = f"Based on the prompt, this appears to be a {intent_analysis['task_type']} task. " \
                   f"Retrieved {len(retrieved_examples)} relevant examples."

        return ThinkingStep(
            step_number=1,
            thought=thought,
            reasoning=reasoning,
            retrieved_examples=retrieved_examples,
            intermediate_code=None,
            confidence=0.6
        )

    def _thinking_step_2_analyze_and_plan(self, prompt: str, step1: ThinkingStep, language: str) -> ThinkingStep:
        """Second thinking step: analyze patterns and plan solution."""

        # Analyze patterns from retrieved examples
        patterns = self._analyze_patterns(step1.retrieved_examples)

        # Plan the solution approach
        solution_plan = self._plan_solution(prompt, patterns, language)

        # Generate intermediate code structure
        intermediate_code = self._generate_code_structure(solution_plan, language)

        thought = f"Planning solution using patterns: {', '.join(patterns[:3])}"
        reasoning = f"Identified common patterns and planned a {solution_plan['approach']} approach. " \
                   f"Created basic code structure."

        return ThinkingStep(
            step_number=2,
            thought=thought,
            reasoning=reasoning,
            retrieved_examples=[],
            intermediate_code=intermediate_code,
            confidence=0.7
        )

    def _thinking_step_3_generate_and_refine(self, prompt: str, step1: ThinkingStep,
                                           step2: ThinkingStep, language: str) -> ThinkingStep:
        """Third thinking step: generate and refine code."""

        # Generate detailed code based on plan
        detailed_code = self._generate_detailed_code(prompt, step1, step2, language)

        # Refine the code
        refined_code = self._refine_code(detailed_code, step1.retrieved_examples, language)

        thought = "Generating detailed implementation and refining based on best practices"
        reasoning = f"Created detailed implementation with {len(refined_code.splitlines())} lines. " \
                   f"Applied refinements based on retrieved examples."

        return ThinkingStep(
            step_number=3,
            thought=thought,
            reasoning=reasoning,
            retrieved_examples=[],
            intermediate_code=refined_code,
            confidence=0.8
        )

    def _additional_thinking_step(self, prompt: str, previous_steps: List[ThinkingStep],
                                language: str, step_number: int) -> ThinkingStep:
        """Additional thinking step for further refinement."""

        # Analyze previous steps for improvement opportunities
        improvement_areas = self._identify_improvement_areas(previous_steps)

        # Apply improvements
        current_code = previous_steps[-1].intermediate_code
        improved_code = self._apply_improvements(current_code, improvement_areas, language)

        thought = f"Refining solution - focusing on {', '.join(improvement_areas[:2])}"
        reasoning = f"Applied improvements in {len(improvement_areas)} areas. " \
                   f"Enhanced code quality and robustness."

        # Calculate confidence based on improvements
        confidence = min(previous_steps[-1].confidence + 0.1, 0.95)

        return ThinkingStep(
            step_number=step_number,
            thought=thought,
            reasoning=reasoning,
            retrieved_examples=[],
            intermediate_code=improved_code,
            confidence=confidence
        )

    def _analyze_intent(self, prompt: str) -> Dict[str, Any]:
        """Analyze the intent of the prompt."""
        prompt_lower = prompt.lower()

        # Determine primary intent
        if any(keyword in prompt_lower for keyword in ["create", "generate", "write", "implement"]):
            primary_intent = "creation"
        elif any(keyword in prompt_lower for keyword in ["fix", "debug", "correct", "repair"]):
            primary_intent = "debugging"
        elif any(keyword in prompt_lower for keyword in ["optimize", "improve", "enhance", "refactor"]):
            primary_intent = "optimization"
        elif any(keyword in prompt_lower for keyword in ["explain", "understand", "analyze"]):
            primary_intent = "explanation"
        else:
            primary_intent = "general"

        # Determine task type
        if any(keyword in prompt_lower for keyword in ["function", "method", "def"]):
            task_type = "function"
        elif any(keyword in prompt_lower for keyword in ["class", "object"]):
            task_type = "class"
        elif any(keyword in prompt_lower for keyword in ["algorithm", "sort", "search"]):
            task_type = "algorithm"
        elif any(keyword in prompt_lower for keyword in ["api", "endpoint", "server"]):
            task_type = "web_api"
        elif any(keyword in prompt_lower for keyword in ["data", "process", "parse"]):
            task_type = "data_processing"
        else:
            task_type = "general"

        return {
            "primary_intent": primary_intent,
            "task_type": task_type,
            "complexity": "medium",  # Could be analyzed further
            "domain": "general"
        }

    def _retrieve_relevant_examples(self, prompt: str, intent_analysis: Dict[str, Any],
                                  language: str) -> List[RetrievalResult]:
        """Retrieve relevant code examples."""
        examples = []

        # Use semantic indexer if available
        if self.semantic_indexer:
            try:
                search_results = self.semantic_indexer.search_semantic(
                    prompt, context=intent_analysis.get("task_type"),
                    max_results=self.rag_config["max_retrieved_examples"]
                )

                for result in search_results:
                    if result.relevance_score >= self.rag_config["relevance_threshold"]:
                        retrieval_result = RetrievalResult(
                            code_snippet=result.block.content if result.block else "",
                            file_path=result.symbol.file_path if result.symbol else "",
                            relevance_score=result.relevance_score,
                            context_match=result.context_match,
                            usage_pattern=intent_analysis["task_type"],
                            metadata={"reasoning": result.reasoning}
                        )
                        examples.append(retrieval_result)
            except Exception as e:
                logger.warning(f"Error retrieving examples: {e}")

        # Fallback to pattern library
        if len(examples) < 2:
            examples.extend(self._get_pattern_examples(intent_analysis, language))

        return examples

    def _get_pattern_examples(self, intent_analysis: Dict[str, Any], language: str) -> List[RetrievalResult]:
        """Get examples from pattern library."""
        examples = []
        task_type = intent_analysis.get("task_type", "general")

        # Simple pattern-based examples
        if task_type == "function":
            code_snippet = self._get_function_template(language)
        elif task_type == "class":
            code_snippet = self._get_class_template(language)
        elif task_type == "algorithm":
            code_snippet = self._get_algorithm_template(language)
        else:
            code_snippet = self._get_general_template(language)

        example = RetrievalResult(
            code_snippet=code_snippet,
            file_path="pattern_library",
            relevance_score=0.5,
            context_match=task_type,
            usage_pattern=task_type,
            metadata={"source": "pattern_library"}
        )
        examples.append(example)

        return examples

    def _get_function_template(self, language: str) -> str:
        """Get function template for the language."""
        templates = {
            "python": """def function_name(param1, param2):
    \"\"\"
    Function description.

    Args:
        param1: Description of param1
        param2: Description of param2

    Returns:
        Description of return value
    \"\"\"
    # Implementation here
    return result""",

            "javascript": """function functionName(param1, param2) {
    /**
     * Function description.
     * @param {type} param1 - Description of param1
     * @param {type} param2 - Description of param2
     * @returns {type} Description of return value
     */
    // Implementation here
    return result;
}""",

            "java": """public returnType functionName(Type param1, Type param2) {
    /**
     * Function description.
     * @param param1 Description of param1
     * @param param2 Description of param2
     * @return Description of return value
     */
    // Implementation here
    return result;
}"""
        }

        return templates.get(language, templates["python"])

    def _get_class_template(self, language: str) -> str:
        """Get class template for the language."""
        templates = {
            "python": """class ClassName:
    \"\"\"
    Class description.
    \"\"\"

    def __init__(self, param1, param2):
        \"\"\"Initialize the class.\"\"\"
        self.param1 = param1
        self.param2 = param2

    def method_name(self, param):
        \"\"\"Method description.\"\"\"
        # Implementation here
        return result""",

            "javascript": """class ClassName {
    /**
     * Class description.
     */
    constructor(param1, param2) {
        this.param1 = param1;
        this.param2 = param2;
    }

    methodName(param) {
        /**
         * Method description.
         */
        // Implementation here
        return result;
    }
}""",

            "java": """public class ClassName {
    /**
     * Class description.
     */
    private Type param1;
    private Type param2;

    public ClassName(Type param1, Type param2) {
        this.param1 = param1;
        this.param2 = param2;
    }

    public ReturnType methodName(Type param) {
        /**
         * Method description.
         */
        // Implementation here
        return result;
    }
}"""
        }

        return templates.get(language, templates["python"])

    def _get_algorithm_template(self, language: str) -> str:
        """Get algorithm template for the language."""
        if language == "python":
            return """def algorithm_name(data):
    \"\"\"
    Algorithm description.

    Time Complexity: O(n)
    Space Complexity: O(1)
    \"\"\"
    # Initialize variables
    result = []

    # Main algorithm logic
    for item in data:
        # Process item
        processed = process_item(item)
        result.append(processed)

    return result"""
        else:
            return self._get_function_template(language)

    def _get_general_template(self, language: str) -> str:
        """Get general template for the language."""
        return self._get_function_template(language)

    def _analyze_patterns(self, examples: List[RetrievalResult]) -> List[str]:
        """Analyze patterns from retrieved examples."""
        patterns = []

        for example in examples:
            code = example.code_snippet

            # Simple pattern detection
            if "class " in code:
                patterns.append("object_oriented")
            if "def " in code or "function " in code:
                patterns.append("functional")
            if "for " in code or "while " in code:
                patterns.append("iterative")
            if "try:" in code or "catch" in code:
                patterns.append("error_handling")
            if "import " in code or "require(" in code:
                patterns.append("modular")

        return list(set(patterns))

    def _plan_solution(self, prompt: str, patterns: List[str], language: str) -> Dict[str, Any]:
        """Plan the solution approach."""
        return {
            "approach": "structured" if "object_oriented" in patterns else "functional",
            "complexity": "medium",
            "patterns_to_use": patterns[:3],
            "estimated_lines": 20,
        }

    def _generate_code_structure(self, plan: Dict[str, Any], language: str) -> str:
        """Generate basic code structure."""
        if plan["approach"] == "structured":
            return self._get_class_template(language)
        else:
            return self._get_function_template(language)

    def _generate_detailed_code(self, prompt: str, step1: ThinkingStep,
                              step2: ThinkingStep, language: str) -> str:
        """Generate detailed code implementation."""
        # This would use the model manager to generate code
        # For now, return the intermediate code with some enhancements
        base_code = step2.intermediate_code or self._get_function_template(language)

        # Add some basic enhancements
        enhanced_code = base_code.replace("# Implementation here",
                                        "# TODO: Implement based on requirements\n    pass")

        return enhanced_code

    def _refine_code(self, code: str, examples: List[RetrievalResult], language: str) -> str:
        """Refine code based on examples."""
        # Simple refinement - in production, this would be more sophisticated
        refined = code

        # Add error handling if examples show it
        if any("try:" in ex.code_snippet for ex in examples):
            if "try:" not in refined:
                refined = refined.replace("pass", "try:\n        # Implementation\n        pass\n    except Exception as e:\n        # Handle error\n        raise")

        return refined

    def _generate_final_code(self, prompt: str, thinking_process: List[ThinkingStep],
                           context: Optional[CodeContext], language: str) -> str:
        """Generate the final code."""
        # Use the last thinking step's code as the base
        if thinking_process and thinking_process[-1].intermediate_code:
            return thinking_process[-1].intermediate_code
        else:
            return self._get_function_template(language)

    def _calculate_overall_confidence(self, thinking_process: List[ThinkingStep]) -> float:
        """Calculate overall confidence score."""
        if not thinking_process:
            return 0.5

        # Average confidence with more weight on later steps
        weights = [0.2, 0.3, 0.5]  # Weights for first 3 steps
        total_weight = 0
        weighted_sum = 0

        for i, step in enumerate(thinking_process):
            weight = weights[i] if i < len(weights) else 0.1
            weighted_sum += step.confidence * weight
            total_weight += weight

        return weighted_sum / total_weight if total_weight > 0 else 0.5

    def _generate_alternatives(self, prompt: str, thinking_process: List[ThinkingStep],
                             language: str) -> List[str]:
        """Generate alternative implementations."""
        # Simple alternatives - in production, generate actual alternatives
        return [
            "Alternative implementation using different approach",
            "Optimized version with better performance",
            "Simplified version with reduced complexity"
        ]

    def _generate_explanations(self, code: str, thinking_process: List[ThinkingStep],
                             language: str) -> List[str]:
        """Generate explanations for the code."""
        explanations = []

        # Add explanation based on thinking process
        if thinking_process:
            explanations.append(f"Solution approach: {thinking_process[0].thought}")
            explanations.append(f"Implementation strategy: {thinking_process[-1].reasoning}")

        # Add code-specific explanations
        if "class " in code:
            explanations.append("Uses object-oriented programming approach")
        if "def " in code:
            explanations.append("Implements functionality using functions")

        return explanations

    def _identify_potential_issues(self, code: str, language: str) -> List[str]:
        """Identify potential issues in the code."""
        issues = []

        # Simple issue detection
        if "pass" in code:
            issues.append("Contains placeholder code that needs implementation")
        if "TODO" in code:
            issues.append("Contains TODO comments that need attention")
        if language == "python" and "except:" in code:
            issues.append("Uses bare except clause - consider specific exception handling")

        return issues

    def _generate_optimization_suggestions(self, code: str, language: str) -> List[str]:
        """Generate optimization suggestions."""
        suggestions = []

        # Simple optimization suggestions
        if "for " in code and "append" in code:
            suggestions.append("Consider using list comprehension for better performance")
        if "while " in code:
            suggestions.append("Ensure loop termination conditions are properly handled")
        if language == "python" and "range(len(" in code:
            suggestions.append("Consider using enumerate() for cleaner iteration")

        return suggestions

    def _identify_improvement_areas(self, steps: List[ThinkingStep]) -> List[str]:
        """Identify areas for improvement in the thinking process."""
        areas = []

        # Check confidence progression
        if len(steps) >= 2 and steps[-1].confidence <= steps[-2].confidence:
            areas.append("confidence_improvement")

        # Check for missing error handling
        if steps and steps[-1].intermediate_code and "try:" not in steps[-1].intermediate_code:
            areas.append("error_handling")

        # Check for documentation
        if steps and steps[-1].intermediate_code and '"""' not in steps[-1].intermediate_code:
            areas.append("documentation")

        return areas

    def _apply_improvements(self, code: str, improvement_areas: List[str], language: str) -> str:
        """Apply improvements to the code."""
        improved_code = code

        for area in improvement_areas:
            if area == "error_handling" and "try:" not in improved_code:
                # Add basic error handling
                improved_code = improved_code.replace(
                    "pass",
                    "try:\n        # Implementation\n        pass\n    except Exception as e:\n        logger.error(f'Error: {e}')\n        raise"
                )
            elif area == "documentation" and '"""' not in improved_code:
                # Add basic documentation
                if "def " in improved_code:
                    improved_code = improved_code.replace(
                        "def ",
                        'def '
                    ).replace(
                        "):",
                        "):\n    \"\"\"Function description.\"\"\""
                    )

        return improved_code

    def update_knowledge_base(self, code: str, metadata: Dict[str, Any]):
        """Update the code knowledge base with new examples.

        Args:
            code: The code to add
            metadata: Metadata about the code
        """
        with self.lock:
            code_id = f"kb_{int(time.time())}_{hash(code) % 10000}"
            self.code_knowledge_base[code_id] = {
                "code": code,
                "metadata": metadata,
                "timestamp": time.time(),
                "usage_count": 0,
                "effectiveness_score": 0.5,
            }

            logger.info(f"Added code example to knowledge base: {code_id}")

    def get_generation_statistics(self) -> Dict[str, Any]:
        """Get statistics about code generation.

        Returns:
            Dictionary with generation statistics
        """
        with self.lock:
            if not self.generation_history:
                return {"total_generations": 0}

            total_generations = len(self.generation_history)
            avg_confidence = sum(r.confidence_score for r in self.generation_history) / total_generations

            languages = {}
            for result in self.generation_history:
                lang = result.language
                languages[lang] = languages.get(lang, 0) + 1

            thinking_steps_avg = sum(len(r.thinking_process) for r in self.generation_history) / total_generations

            return {
                "total_generations": total_generations,
                "average_confidence": avg_confidence,
                "languages_used": languages,
                "average_thinking_steps": thinking_steps_avg,
                "knowledge_base_size": len(self.code_knowledge_base),
            }

    def clear_history(self):
        """Clear generation history."""
        with self.lock:
            self.generation_history.clear()
            logger.info("Cleared generation history")
