"""
Shell tool for the Advanced AI Agent.
Provides robust command execution with comprehensive error handling and improved performance.
"""

import os
import sys
import signal
import subprocess
import platform
import tempfile
import time
import logging
import shlex
import re
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Callable, Set

# Get the logger
logger = logging.getLogger("advanced_ai_agent")

class ShellToolError(Exception):
    """Base exception for ShellTool errors."""
    pass

class CommandExecutionError(ShellToolError):
    """Exception raised when a command fails to execute."""
    def __init__(self, message: str, command: str, return_code: Optional[int] = None,
                 stdout: Optional[str] = None, stderr: Optional[str] = None):
        self.command = command
        self.return_code = return_code
        self.stdout = stdout
        self.stderr = stderr
        super().__init__(message)

class CommandTimeoutError(ShellToolError):
    """Exception raised when a command times out."""
    pass

class ShellTool:
    """Shell tool for executing commands with enhanced error handling and performance."""

    # Default timeout for commands in seconds
    DEFAULT_TIMEOUT = 60

    # Maximum output size to capture (in bytes)
    MAX_OUTPUT_SIZE = 1024 * 1024  # 1 MB

    def __init__(self, workspace_dir: Optional[Path] = None):
        """Initialize the shell tool.

        Args:
            workspace_dir: The workspace directory to use. If None, will use the current directory.
        """
        self.workspace_dir = workspace_dir or Path.cwd()
        self.history: List[Dict[str, Any]] = []

        # Determine the shell to use based on the platform
        self.is_windows = platform.system() == "Windows"
        if self.is_windows:
            # Check if bash is available (Git Bash, WSL, etc.)
            try:
                # Try to execute bash with a simple command
                result = subprocess.run(
                    ["bash", "-c", "echo test"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=1
                )
                if result.returncode == 0:
                    # Bash is available, use it for better compatibility with Unix commands
                    self.shell = "bash"
                    self.shell_args = ["-c"]
                    self.env = os.environ.copy()
                    # Ensure consistent locale settings
                    self.env["LC_ALL"] = "C.UTF-8"
                    self.env["LANG"] = "C.UTF-8"
                else:
                    # Fall back to PowerShell
                    self.shell = "powershell"
                    self.shell_args = ["-Command"]
                    self.env = os.environ.copy()
                    # Set PowerShell to use UTF-8
                    self.env["PYTHONIOENCODING"] = "utf-8"
            except (subprocess.SubprocessError, FileNotFoundError):
                # Fall back to PowerShell
                self.shell = "powershell"
                self.shell_args = ["-Command"]
                self.env = os.environ.copy()
                # Set PowerShell to use UTF-8
                self.env["PYTHONIOENCODING"] = "utf-8"
        else:
            self.shell = "bash"
            self.shell_args = ["-c"]
            self.env = os.environ.copy()
            # Ensure consistent locale settings
            self.env["LC_ALL"] = "C.UTF-8"
            self.env["LANG"] = "C.UTF-8"

        # Set of dangerous commands that should be confirmed
        self.dangerous_commands: Set[str] = {
            "rm", "rmdir", "del", "format", "shutdown", "reboot",
            "drop", "truncate", "delete", "remove"
        }

    def execute(self, command: str,
                timeout: Optional[int] = None,
                check_dangerous: bool = True,
                capture_output: bool = True) -> Tuple[str, str, int]:
        """Execute a shell command with enhanced error handling and timeout support.

        Args:
            command: The command to execute.
            timeout: Timeout in seconds. If None, uses DEFAULT_TIMEOUT.
            check_dangerous: Whether to check for dangerous commands. Default is True.
            capture_output: Whether to capture command output. Default is True.

        Returns:
            A tuple of (stdout, stderr, return_code).

        Raises:
            CommandExecutionError: If the command fails to execute.
            CommandTimeoutError: If the command times out.
        """
        # Use default timeout if not specified
        if timeout is None:
            timeout = self.DEFAULT_TIMEOUT

        # Add to history
        self.history.append({
            "action": "execute",
            "command": command,
            "timeout": timeout,
            "timestamp": time.time()
        })

        # Check for dangerous commands
        if check_dangerous and self._is_dangerous_command(command):
            warning = f"Warning: The command '{command}' contains potentially dangerous operations."
            logger.warning(warning)
            self.history[-1]["warning"] = warning

        # Log the command
        logger.info(f"Executing command: {command}")

        # Special handling for complex commands with pipes, redirects, etc.
        if self.is_windows:
            # Log the command for debugging
            logger.info(f"Executing Windows command: {command}")

            # Try direct execution with shell=True first for complex commands
            if any(char in command for char in "|&><;"):
                try:
                    # Use subprocess.run with shell=True for complex commands
                    logger.info(f"Using direct shell execution for complex command")
                    process = subprocess.run(
                        command,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        shell=True,
                        cwd=self.workspace_dir,
                        env=self.env,
                        universal_newlines=True,
                        timeout=timeout
                    )

                    # Add to history
                    self.history[-1].update({
                        "stdout": process.stdout,
                        "stderr": process.stderr,
                        "return_code": process.returncode,
                        "success": process.returncode == 0,
                        "execution_method": "shell_direct"
                    })

                    return process.stdout, process.stderr, process.returncode
                except Exception as e:
                    logger.warning(f"Failed to execute with shell=True: {e}")
                    # Continue to other methods

            # Try cmd.exe for Windows commands with pipes and redirects
            if any(char in command for char in "|&><;"):
                try:
                    # Use cmd.exe directly for better compatibility with Windows commands
                    logger.info(f"Using cmd.exe for command execution")
                    process = subprocess.run(
                        ["cmd.exe", "/c", command],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        cwd=self.workspace_dir,
                        env=self.env,
                        universal_newlines=True,
                        timeout=timeout
                    )

                    # Add to history
                    self.history[-1].update({
                        "stdout": process.stdout,
                        "stderr": process.stderr,
                        "return_code": process.returncode,
                        "success": process.returncode == 0,
                        "execution_method": "cmd_exe"
                    })

                    return process.stdout, process.stderr, process.returncode
                except Exception as e:
                    logger.warning(f"Failed to execute with cmd.exe: {e}")
                    # Fall back to PowerShell

            # For file redirection specifically
            if ">" in command:
                try:
                    # Split the command and redirection
                    parts = command.split(">", 1)
                    cmd = parts[0].strip()
                    file_path = parts[1].strip()

                    # Remove quotes if present
                    if file_path.startswith('"') and file_path.endswith('"'):
                        file_path = file_path[1:-1]
                    elif file_path.startswith("'") and file_path.endswith("'"):
                        file_path = file_path[1:-1]

                    # Execute the command and capture output
                    logger.info(f"Handling redirection manually: {cmd} > {file_path}")
                    process = subprocess.run(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        shell=True,
                        cwd=self.workspace_dir,
                        env=self.env,
                        universal_newlines=True,
                        timeout=timeout
                    )

                    # Write the output to the file
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(process.stdout)

                    # Add to history
                    self.history[-1].update({
                        "stdout": f"Output written to {file_path}",
                        "stderr": process.stderr,
                        "return_code": process.returncode,
                        "success": process.returncode == 0,
                        "execution_method": "manual_redirection"
                    })

                    return f"Output written to {file_path}", process.stderr, process.returncode
                except Exception as e:
                    logger.warning(f"Failed to handle redirection manually: {e}")
                    # Continue to other methods

            # For PowerShell, handle && operator by splitting commands
            if "&&" in command:
                # Split by && and execute each command separately
                commands = command.split("&&")
                stdout_all = ""
                stderr_all = ""
                return_code = 0

                logger.info(f"Handling && operator by splitting commands")
                for cmd in commands:
                    cmd = cmd.strip()
                    if not cmd:
                        continue

                    # Execute each command
                    stdout, stderr, rc = self._execute_single_command(cmd, timeout, capture_output)

                    # Append the output
                    stdout_all += stdout + "\n"
                    stderr_all += stderr + "\n"

                    # If any command fails, stop execution
                    if rc != 0:
                        return_code = rc
                        break

                # Add to history
                self.history[-1].update({
                    "stdout": stdout_all,
                    "stderr": stderr_all,
                    "return_code": return_code,
                    "success": return_code == 0,
                    "execution_method": "split_commands"
                })

                return stdout_all, stderr_all, return_code

            # For PowerShell with pipes, use a different approach
            elif any(char in command for char in "|><;"):
                # For PowerShell, we need to make sure it properly handles these
                logger.info(f"Using PowerShell with special handling for complex command")
                if self.shell == "powershell":
                    # Try different PowerShell approaches
                    try:
                        # First approach: Use -Command with the command as is
                        process = subprocess.run(
                            ["powershell", "-Command", command],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            cwd=self.workspace_dir,
                            env=self.env,
                            universal_newlines=True,
                            timeout=timeout
                        )

                        # If successful, return the results
                        if process.returncode == 0:
                            self.history[-1].update({
                                "stdout": process.stdout,
                                "stderr": process.stderr,
                                "return_code": process.returncode,
                                "success": True,
                                "execution_method": "powershell_direct"
                            })
                            return process.stdout, process.stderr, process.returncode
                    except Exception as e:
                        logger.warning(f"First PowerShell approach failed: {e}")

                    # Second approach: Escape quotes and wrap in quotes
                    command = command.replace('"', '`"')  # Escape double quotes
                    command = f'"{command}"'  # Wrap the entire command in quotes

        try:
            if capture_output:
                # Create temporary files for stdout and stderr
                with tempfile.NamedTemporaryFile(mode="w+", delete=False) as stdout_file, \
                     tempfile.NamedTemporaryFile(mode="w+", delete=False) as stderr_file:

                    # Start the process
                    start_time = time.time()
                    process = subprocess.Popen(
                        [self.shell] + self.shell_args + [command],
                        stdout=stdout_file,
                        stderr=stderr_file,
                        cwd=self.workspace_dir,
                        env=self.env,
                        shell=False,
                        universal_newlines=True,
                    )

                    # Wait for the process with timeout
                    try:
                        return_code = self._wait_with_timeout(process, timeout)
                    except CommandTimeoutError as e:
                        # Kill the process if it times out
                        self._kill_process(process)

                        # Add to history
                        self.history[-1]["error"] = str(e)
                        self.history[-1]["timeout"] = True
                        self.history[-1]["duration"] = time.time() - start_time

                        # Clean up temporary files
                        stdout_file.close()
                        stderr_file.close()
                        try:
                            os.unlink(stdout_file.name)
                            os.unlink(stderr_file.name)
                        except Exception:
                            pass

                        raise

                    # Calculate duration
                    duration = time.time() - start_time

                    # Read the output
                    stdout_file.flush()
                    stderr_file.flush()

                    # Close the files
                    stdout_file.close()
                    stderr_file.close()

                    # Read the output from the files
                    try:
                        with open(stdout_file.name, "r", encoding="utf-8") as f:
                            stdout = f.read(self.MAX_OUTPUT_SIZE)
                            if f.read(1):  # Check if there's more content
                                stdout += "\n... [output truncated due to size]"
                    except UnicodeDecodeError:
                        # Try with a different encoding
                        with open(stdout_file.name, "r", encoding="latin-1") as f:
                            stdout = f.read(self.MAX_OUTPUT_SIZE)
                            if f.read(1):  # Check if there's more content
                                stdout += "\n... [output truncated due to size]"

                    try:
                        with open(stderr_file.name, "r", encoding="utf-8") as f:
                            stderr = f.read(self.MAX_OUTPUT_SIZE)
                            if f.read(1):  # Check if there's more content
                                stderr += "\n... [output truncated due to size]"
                    except UnicodeDecodeError:
                        # Try with a different encoding
                        with open(stderr_file.name, "r", encoding="latin-1") as f:
                            stderr = f.read(self.MAX_OUTPUT_SIZE)
                            if f.read(1):  # Check if there's more content
                                stderr += "\n... [output truncated due to size]"

                    # Delete the temporary files
                    try:
                        os.unlink(stdout_file.name)
                        os.unlink(stderr_file.name)
                    except Exception as e:
                        logger.warning(f"Failed to delete temporary files: {e}")

                    # Add to history
                    self.history[-1].update({
                        "stdout": stdout,
                        "stderr": stderr,
                        "return_code": return_code,
                        "duration": duration,
                        "success": return_code == 0
                    })

                    # Log completion
                    logger.info(f"Command completed with return code {return_code} in {duration:.2f}s")

                    return stdout, stderr, return_code
            else:
                # Execute without capturing output (faster for some commands)
                start_time = time.time()

                # Execute the command
                try:
                    process = subprocess.run(
                        [self.shell] + self.shell_args + [command],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL,
                        cwd=self.workspace_dir,
                        env=self.env,
                        shell=False,
                        timeout=timeout
                    )

                    return_code = process.returncode
                    duration = time.time() - start_time

                    # Add to history
                    self.history[-1].update({
                        "return_code": return_code,
                        "duration": duration,
                        "success": return_code == 0,
                        "output_captured": False
                    })

                    # Log completion
                    logger.info(f"Command completed with return code {return_code} in {duration:.2f}s")

                    return "", "", return_code

                except subprocess.TimeoutExpired:
                    error = CommandTimeoutError(f"Command timed out after {timeout} seconds: {command}")

                    # Add to history
                    self.history[-1].update({
                        "error": str(error),
                        "timeout": True,
                        "duration": time.time() - start_time,
                        "success": False
                    })

                    raise error

        except CommandTimeoutError:
            # Re-raise timeout errors
            raise

        except Exception as e:
            error_message = f"Error executing command: {e}"

            # Add to history
            self.history[-1].update({
                "error": error_message,
                "success": False
            })

            logger.error(f"Command execution failed: {e}")

            # Raise a more specific exception
            raise CommandExecutionError(
                message=error_message,
                command=command,
                return_code=1
            )

    def execute_interactive(self, command: str, check_dangerous: bool = True) -> subprocess.Popen:
        """Execute a shell command interactively with enhanced error handling.

        Args:
            command: The command to execute.
            check_dangerous: Whether to check for dangerous commands. Default is True.

        Returns:
            The subprocess.Popen object.

        Raises:
            CommandExecutionError: If the command fails to execute.
        """
        # Add to history
        self.history.append({
            "action": "execute_interactive",
            "command": command,
            "timestamp": time.time(),
            "interactive": True
        })

        # Check for dangerous commands
        if check_dangerous and self._is_dangerous_command(command):
            warning = f"Warning: The command '{command}' contains potentially dangerous operations."
            logger.warning(warning)
            self.history[-1]["warning"] = warning

        # Log the command
        logger.info(f"Executing interactive command: {command}")

        try:
            # Execute the command
            process = subprocess.Popen(
                [self.shell] + self.shell_args + [command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                cwd=self.workspace_dir,
                env=self.env,
                shell=False,
                universal_newlines=True,
                bufsize=1,
            )

            # Store the process ID in history
            self.history[-1]["pid"] = process.pid

            return process

        except Exception as e:
            error_message = f"Error executing interactive command: {e}"

            # Add to history
            self.history[-1].update({
                "error": error_message,
                "success": False
            })

            logger.error(f"Interactive command execution failed: {e}")

            # Raise a more specific exception
            raise CommandExecutionError(
                message=error_message,
                command=command
            )

    def execute_with_input(self, command: str, input_data: str,
                          timeout: Optional[int] = None,
                          check_dangerous: bool = True) -> Tuple[str, str, int]:
        """Execute a command with input data.

        Args:
            command: The command to execute.
            input_data: The input data to provide to the command.
            timeout: Timeout in seconds. If None, uses DEFAULT_TIMEOUT.
            check_dangerous: Whether to check for dangerous commands. Default is True.

        Returns:
            A tuple of (stdout, stderr, return_code).

        Raises:
            CommandExecutionError: If the command fails to execute.
            CommandTimeoutError: If the command times out.
        """
        # Use default timeout if not specified
        if timeout is None:
            timeout = self.DEFAULT_TIMEOUT

        # Add to history
        self.history.append({
            "action": "execute_with_input",
            "command": command,
            "input_length": len(input_data),
            "timeout": timeout,
            "timestamp": time.time()
        })

        # Check for dangerous commands
        if check_dangerous and self._is_dangerous_command(command):
            warning = f"Warning: The command '{command}' contains potentially dangerous operations."
            logger.warning(warning)
            self.history[-1]["warning"] = warning

        # Log the command
        logger.info(f"Executing command with input: {command}")

        try:
            # Start the process
            start_time = time.time()
            process = subprocess.Popen(
                [self.shell] + self.shell_args + [command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                cwd=self.workspace_dir,
                env=self.env,
                shell=False,
                universal_newlines=True,
                bufsize=1,
            )

            # Send input to the process
            try:
                stdout, stderr = process.communicate(input=input_data, timeout=timeout)
            except subprocess.TimeoutExpired:
                # Kill the process if it times out
                self._kill_process(process)

                error = CommandTimeoutError(f"Command timed out after {timeout} seconds: {command}")

                # Add to history
                self.history[-1].update({
                    "error": str(error),
                    "timeout": True,
                    "duration": time.time() - start_time,
                    "success": False
                })

                raise error

            # Get the return code
            return_code = process.returncode
            duration = time.time() - start_time

            # Truncate output if it's too large
            if len(stdout) > self.MAX_OUTPUT_SIZE:
                stdout = stdout[:self.MAX_OUTPUT_SIZE] + "\n... [output truncated due to size]"

            if len(stderr) > self.MAX_OUTPUT_SIZE:
                stderr = stderr[:self.MAX_OUTPUT_SIZE] + "\n... [output truncated due to size]"

            # Add to history
            self.history[-1].update({
                "stdout": stdout,
                "stderr": stderr,
                "return_code": return_code,
                "duration": duration,
                "success": return_code == 0
            })

            # Log completion
            logger.info(f"Command with input completed with return code {return_code} in {duration:.2f}s")

            return stdout, stderr, return_code

        except CommandTimeoutError:
            # Re-raise timeout errors
            raise

        except Exception as e:
            error_message = f"Error executing command with input: {e}"

            # Add to history
            self.history[-1].update({
                "error": error_message,
                "success": False
            })

            logger.error(f"Command with input execution failed: {e}")

            # Raise a more specific exception
            raise CommandExecutionError(
                message=error_message,
                command=command,
                return_code=1
            )

    def _execute_single_command(self, command: str, timeout: int, capture_output: bool) -> Tuple[str, str, int]:
        """Execute a single command.

        Args:
            command: The command to execute.
            timeout: Timeout in seconds.
            capture_output: Whether to capture command output.

        Returns:
            A tuple of (stdout, stderr, return_code).
        """
        # Log the command for debugging
        logger.info(f"Executing single command: {command}")

        # First try direct execution with shell=True for better compatibility
        if self.is_windows and any(char in command for char in "|&><;"):
            try:
                logger.info(f"Trying direct shell execution for single command")
                process = subprocess.run(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    shell=True,
                    cwd=self.workspace_dir,
                    env=self.env,
                    universal_newlines=True,
                    timeout=timeout
                )
                return process.stdout, process.stderr, process.returncode
            except Exception as e:
                logger.warning(f"Direct shell execution failed: {e}")
                # Continue with other methods

        # Try using subprocess.run with capture_output for simplicity and reliability
        try:
            if capture_output:
                logger.info(f"Using subprocess.run with capture_output")
                process = subprocess.run(
                    [self.shell] + self.shell_args + [command],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=self.workspace_dir,
                    env=self.env,
                    universal_newlines=True,
                    timeout=timeout
                )

                # Truncate output if it's too large
                stdout = process.stdout
                stderr = process.stderr

                if len(stdout) > self.MAX_OUTPUT_SIZE:
                    stdout = stdout[:self.MAX_OUTPUT_SIZE] + "\n... [output truncated due to size]"

                if len(stderr) > self.MAX_OUTPUT_SIZE:
                    stderr = stderr[:self.MAX_OUTPUT_SIZE] + "\n... [output truncated due to size]"

                return stdout, stderr, process.returncode
            else:
                # Execute without capturing output
                logger.info(f"Using subprocess.run without capture_output")
                process = subprocess.run(
                    [self.shell] + self.shell_args + [command],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    cwd=self.workspace_dir,
                    env=self.env,
                    timeout=timeout
                )
                return "", "", process.returncode

        except subprocess.TimeoutExpired:
            error = CommandTimeoutError(f"Command timed out after {timeout} seconds: {command}")
            logger.error(f"Command timed out: {command}")
            raise error
        except Exception as e:
            logger.error(f"subprocess.run failed: {e}")
            # Fall back to the original implementation with temporary files

        # If all else fails, use the original implementation with temporary files
        try:
            if capture_output:
                logger.info(f"Falling back to temporary file method")
                # Create temporary files for stdout and stderr
                with tempfile.NamedTemporaryFile(mode="w+", delete=False) as stdout_file, \
                     tempfile.NamedTemporaryFile(mode="w+", delete=False) as stderr_file:

                    # Start the process
                    start_time = time.time()
                    process = subprocess.Popen(
                        [self.shell] + self.shell_args + [command],
                        stdout=stdout_file,
                        stderr=stderr_file,
                        cwd=self.workspace_dir,
                        env=self.env,
                        shell=False,
                        universal_newlines=True,
                    )

                    # Wait for the process with timeout
                    try:
                        return_code = self._wait_with_timeout(process, timeout)
                    except CommandTimeoutError as e:
                        # Kill the process if it times out
                        self._kill_process(process)
                        raise

                    # Calculate duration
                    duration = time.time() - start_time

                    # Read the output
                    stdout_file.flush()
                    stderr_file.flush()

                    # Close the files
                    stdout_file.close()
                    stderr_file.close()

                    # Read the output from the files
                    stdout = ""
                    stderr = ""

                    # Try multiple encodings for reading the output
                    for encoding in ["utf-8", "latin-1", "cp1252"]:
                        try:
                            with open(stdout_file.name, "r", encoding=encoding) as f:
                                stdout = f.read(self.MAX_OUTPUT_SIZE)
                                if f.read(1):  # Check if there's more content
                                    stdout += "\n... [output truncated due to size]"
                            break  # If successful, break the loop
                        except UnicodeDecodeError:
                            continue
                        except Exception as e:
                            logger.warning(f"Failed to read stdout with {encoding}: {e}")

                    # Try multiple encodings for reading stderr
                    for encoding in ["utf-8", "latin-1", "cp1252"]:
                        try:
                            with open(stderr_file.name, "r", encoding=encoding) as f:
                                stderr = f.read(self.MAX_OUTPUT_SIZE)
                                if f.read(1):  # Check if there's more content
                                    stderr += "\n... [output truncated due to size]"
                            break  # If successful, break the loop
                        except UnicodeDecodeError:
                            continue
                        except Exception as e:
                            logger.warning(f"Failed to read stderr with {encoding}: {e}")

                    # Delete the temporary files
                    try:
                        os.unlink(stdout_file.name)
                        os.unlink(stderr_file.name)
                    except Exception as e:
                        logger.warning(f"Failed to delete temporary files: {e}")

                    return stdout, stderr, return_code
            else:
                # Execute without capturing output
                logger.info(f"Using subprocess.Popen without capture_output")
                start_time = time.time()
                process = subprocess.Popen(
                    [self.shell] + self.shell_args + [command],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    cwd=self.workspace_dir,
                    env=self.env,
                    shell=False,
                )

                # Wait for the process with timeout
                try:
                    return_code = self._wait_with_timeout(process, timeout)
                    return "", "", return_code
                except CommandTimeoutError:
                    # Kill the process if it times out
                    self._kill_process(process)
                    raise

        except CommandTimeoutError:
            # Re-raise timeout errors
            raise

        except Exception as e:
            error_message = f"Error executing command: {e}"
            logger.error(f"Command execution failed: {e}")

            # Last resort: try with shell=True
            try:
                logger.info(f"Last resort: trying with shell=True")
                process = subprocess.run(
                    command,
                    stdout=subprocess.PIPE if capture_output else subprocess.DEVNULL,
                    stderr=subprocess.PIPE if capture_output else subprocess.DEVNULL,
                    shell=True,
                    cwd=self.workspace_dir,
                    env=self.env,
                    timeout=timeout
                )
                if capture_output:
                    return process.stdout, process.stderr, process.returncode
                else:
                    return "", "", process.returncode
            except Exception as last_e:
                logger.error(f"Last resort also failed: {last_e}")
                return "", str(e), 1

    def _wait_with_timeout(self, process: subprocess.Popen, timeout: int) -> int:
        """Wait for a process to complete with timeout.

        Args:
            process: The process to wait for.
            timeout: Timeout in seconds.

        Returns:
            The return code of the process.

        Raises:
            CommandTimeoutError: If the process times out.
        """
        start_time = time.time()

        # Poll the process until it completes or times out
        while process.poll() is None:
            # Check if we've exceeded the timeout
            if time.time() - start_time > timeout:
                raise CommandTimeoutError(f"Command timed out after {timeout} seconds")

            # Sleep briefly to avoid high CPU usage
            time.sleep(0.1)

        return process.returncode

    def _kill_process(self, process: subprocess.Popen) -> None:
        """Kill a process and its children.

        Args:
            process: The process to kill.
        """
        if self.is_windows:
            # On Windows, use taskkill to kill the process tree
            try:
                subprocess.run(
                    ["taskkill", "/F", "/T", "/PID", str(process.pid)],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    timeout=5
                )
            except Exception as e:
                logger.warning(f"Failed to kill process tree: {e}")

                # Fallback to terminate
                process.terminate()
        else:
            # On Unix, send SIGTERM to the process group
            try:
                os.killpg(os.getpgid(process.pid), signal.SIGTERM)
            except Exception as e:
                logger.warning(f"Failed to kill process group: {e}")

                # Fallback to terminate
                process.terminate()

        # Wait briefly for the process to terminate
        try:
            process.wait(timeout=2)
        except subprocess.TimeoutExpired:
            # If it's still running, force kill
            process.kill()

    def _is_dangerous_command(self, command: str) -> bool:
        """Check if a command contains potentially dangerous operations.

        Args:
            command: The command to check.

        Returns:
            True if the command is potentially dangerous, False otherwise.
        """
        # Normalize the command for checking
        normalized = command.lower()

        # Check for dangerous command patterns
        for dangerous in self.dangerous_commands:
            # Check if it's a standalone command or has word boundaries
            pattern = r'\b' + re.escape(dangerous) + r'\b'
            if re.search(pattern, normalized):
                return True

        # Check for specific dangerous patterns
        dangerous_patterns = [
            r'\brm\s+(-[rf]\s+)*/', # rm with root path
            r'\bformat\s+[a-zA-Z]:', # format drive
            r'\bdrop\s+database\b', # drop database
            r'\btruncate\s+table\b', # truncate table
            r'\bshutdown\b', # shutdown
            r'\breboot\b', # reboot
        ]

        for pattern in dangerous_patterns:
            if re.search(pattern, normalized):
                return True

        return False

    def get_history(self) -> List[Dict[str, Any]]:
        """Get the command execution history.

        Returns:
            The command execution history.
        """
        return self.history

    def clear_history(self) -> None:
        """Clear the command execution history."""
        self.history.clear()
        logger.info("Command history cleared")
