"""
Dependency Analyzer for the AI Code Assistant.

This module creates advanced dependency and relationship mapping capabilities,
analyzing code dependencies, call graphs, and impact analysis.
"""

import os
import json
import time
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Tuple, Set
from dataclasses import dataclass, asdict
from pathlib import Path
import ast
import re
from collections import defaultdict, deque
import networkx as nx

logger = logging.getLogger(__name__)

@dataclass
class Dependency:
    """Represents a dependency relationship."""
    source: str
    target: str
    dependency_type: str  # import, call, inheritance, composition
    file_path: str
    line_number: int
    strength: float  # 0-1, how strong the dependency is
    is_circular: bool
    context: str

@dataclass
class DependencyCluster:
    """Represents a cluster of related dependencies."""
    id: str
    members: List[str]
    cluster_type: str  # module, package, feature
    cohesion_score: float
    coupling_score: float
    stability_score: float

@dataclass
class ImpactAnalysis:
    """Represents impact analysis results."""
    target_symbol: str
    directly_affected: List[str]
    indirectly_affected: List[str]
    impact_score: float
    risk_level: str  # low, medium, high, critical
    recommendations: List[str]

class DependencyAnalyzer:
    """Advanced dependency and relationship mapping system."""

    def __init__(self, workspace_dir: Path, semantic_indexer=None):
        """Initialize the dependency analyzer.
        
        Args:
            workspace_dir: The workspace directory
            semantic_indexer: Optional semantic indexer for symbol information
        """
        self.workspace_dir = workspace_dir
        self.semantic_indexer = semantic_indexer
        self.dependencies: Dict[str, Dependency] = {}
        self.dependency_graph = nx.DiGraph()
        self.call_graph = nx.DiGraph()
        self.inheritance_graph = nx.DiGraph()
        self.clusters: Dict[str, DependencyCluster] = {}
        self.lock = threading.RLock()
        
        # Language-specific analyzers
        self.language_analyzers = {
            "python": self._analyze_python_dependencies,
            "javascript": self._analyze_javascript_dependencies,
            "typescript": self._analyze_typescript_dependencies,
            "java": self._analyze_java_dependencies,
            "cpp": self._analyze_cpp_dependencies,
            "go": self._analyze_go_dependencies,
            "rust": self._analyze_rust_dependencies,
        }

    def analyze_dependencies(self, file_paths: Optional[List[str]] = None) -> Dict[str, Any]:
        """Analyze dependencies in the codebase.
        
        Args:
            file_paths: Optional list of specific files to analyze
            
        Returns:
            Analysis results
        """
        with self.lock:
            logger.info("Starting dependency analysis")
            start_time = time.time()
            
            stats = {
                "files_analyzed": 0,
                "dependencies_found": 0,
                "circular_dependencies": 0,
                "clusters_created": 0,
                "errors": [],
            }
            
            # Get files to analyze
            if file_paths is None:
                file_paths = self._find_code_files()
            
            # Clear existing data
            self.dependencies.clear()
            self.dependency_graph.clear()
            self.call_graph.clear()
            self.inheritance_graph.clear()
            
            # Analyze each file
            for file_path in file_paths:
                try:
                    self._analyze_file_dependencies(file_path, stats)
                    stats["files_analyzed"] += 1
                except Exception as e:
                    error_msg = f"Error analyzing {file_path}: {e}"
                    logger.error(error_msg)
                    stats["errors"].append(error_msg)
            
            # Build graphs
            self._build_dependency_graphs()
            
            # Detect circular dependencies
            circular_deps = self._detect_circular_dependencies()
            stats["circular_dependencies"] = len(circular_deps)
            
            # Create clusters
            self._create_dependency_clusters()
            stats["clusters_created"] = len(self.clusters)
            
            execution_time = time.time() - start_time
            logger.info(f"Dependency analysis completed in {execution_time:.2f}s: {stats}")
            
            return stats

    def get_dependency_graph(self, graph_type: str = "all") -> nx.DiGraph:
        """Get the dependency graph.
        
        Args:
            graph_type: Type of graph (all, call, inheritance)
            
        Returns:
            NetworkX directed graph
        """
        with self.lock:
            if graph_type == "call":
                return self.call_graph.copy()
            elif graph_type == "inheritance":
                return self.inheritance_graph.copy()
            else:
                return self.dependency_graph.copy()

    def analyze_impact(self, symbol_name: str, change_type: str = "modification") -> ImpactAnalysis:
        """Analyze the impact of changing a symbol.
        
        Args:
            symbol_name: The symbol to analyze
            change_type: Type of change (modification, deletion, signature_change)
            
        Returns:
            Impact analysis results
        """
        with self.lock:
            logger.info(f"Analyzing impact of {change_type} to {symbol_name}")
            
            # Find directly affected symbols
            directly_affected = list(self.dependency_graph.successors(symbol_name))
            
            # Find indirectly affected symbols
            indirectly_affected = []
            visited = set([symbol_name] + directly_affected)
            
            for direct_dep in directly_affected:
                indirect = self._find_transitive_dependencies(direct_dep, visited)
                indirectly_affected.extend(indirect)
            
            # Calculate impact score
            impact_score = self._calculate_impact_score(
                symbol_name, directly_affected, indirectly_affected, change_type
            )
            
            # Determine risk level
            risk_level = self._determine_risk_level(impact_score, len(directly_affected), len(indirectly_affected))
            
            # Generate recommendations
            recommendations = self._generate_impact_recommendations(
                symbol_name, directly_affected, indirectly_affected, risk_level, change_type
            )
            
            return ImpactAnalysis(
                target_symbol=symbol_name,
                directly_affected=directly_affected,
                indirectly_affected=indirectly_affected,
                impact_score=impact_score,
                risk_level=risk_level,
                recommendations=recommendations
            )

    def find_dependency_path(self, source: str, target: str) -> List[List[str]]:
        """Find dependency paths between two symbols.
        
        Args:
            source: Source symbol
            target: Target symbol
            
        Returns:
            List of dependency paths
        """
        with self.lock:
            try:
                # Find all simple paths
                paths = list(nx.all_simple_paths(self.dependency_graph, source, target, cutoff=10))
                return paths
            except nx.NetworkXNoPath:
                return []

    def get_dependency_metrics(self) -> Dict[str, Any]:
        """Get dependency metrics for the codebase.
        
        Returns:
            Dictionary of metrics
        """
        with self.lock:
            metrics = {
                "total_dependencies": len(self.dependencies),
                "total_symbols": self.dependency_graph.number_of_nodes(),
                "average_dependencies_per_symbol": 0.0,
                "max_dependencies": 0,
                "circular_dependency_count": 0,
                "strongly_connected_components": 0,
                "dependency_depth": 0,
                "coupling_metrics": {},
                "cohesion_metrics": {},
            }
            
            if self.dependency_graph.number_of_nodes() > 0:
                # Calculate basic metrics
                in_degrees = dict(self.dependency_graph.in_degree())
                out_degrees = dict(self.dependency_graph.out_degree())
                
                metrics["average_dependencies_per_symbol"] = sum(out_degrees.values()) / len(out_degrees)
                metrics["max_dependencies"] = max(out_degrees.values()) if out_degrees else 0
                
                # Find strongly connected components (circular dependencies)
                scc = list(nx.strongly_connected_components(self.dependency_graph))
                metrics["strongly_connected_components"] = len([c for c in scc if len(c) > 1])
                
                # Calculate dependency depth
                try:
                    metrics["dependency_depth"] = nx.dag_longest_path_length(self.dependency_graph)
                except nx.NetworkXError:
                    # Graph has cycles
                    metrics["dependency_depth"] = -1
                
                # Calculate coupling and cohesion
                metrics["coupling_metrics"] = self._calculate_coupling_metrics()
                metrics["cohesion_metrics"] = self._calculate_cohesion_metrics()
            
            return metrics

    def _find_code_files(self) -> List[str]:
        """Find all code files in the workspace."""
        code_files = []
        extensions = {".py", ".js", ".ts", ".jsx", ".tsx", ".java", ".cpp", ".c", ".go", ".rs"}
        
        for root, dirs, files in os.walk(self.workspace_dir):
            # Skip common ignore directories
            dirs[:] = [d for d in dirs if d not in {
                'node_modules', '.git', '__pycache__', '.venv', 'venv',
                'build', 'dist', 'target', '.idea', '.vscode'
            }]
            
            for file in files:
                if Path(file).suffix in extensions:
                    code_files.append(os.path.join(root, file))
        
        return code_files

    def _analyze_file_dependencies(self, file_path: str, stats: Dict[str, Any]):
        """Analyze dependencies in a single file."""
        # Determine language
        extension = Path(file_path).suffix
        language_map = {
            ".py": "python", ".js": "javascript", ".ts": "typescript",
            ".jsx": "javascript", ".tsx": "typescript", ".java": "java",
            ".cpp": "cpp", ".c": "cpp", ".go": "go", ".rs": "rust"
        }
        
        language = language_map.get(extension)
        if not language or language not in self.language_analyzers:
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Analyze dependencies
            analyzer = self.language_analyzers[language]
            dependencies = analyzer(content, file_path)
            
            # Store dependencies
            for dep in dependencies:
                dep_id = f"{dep.source}->{dep.target}:{dep.line_number}"
                self.dependencies[dep_id] = dep
                stats["dependencies_found"] += 1
        
        except Exception as e:
            logger.error(f"Error analyzing dependencies in {file_path}: {e}")

    def _analyze_python_dependencies(self, content: str, file_path: str) -> List[Dependency]:
        """Analyze Python dependencies."""
        dependencies = []
        
        try:
            tree = ast.parse(content)
            
            # Analyze imports
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        dep = Dependency(
                            source=file_path,
                            target=alias.name,
                            dependency_type="import",
                            file_path=file_path,
                            line_number=node.lineno,
                            strength=0.8,
                            is_circular=False,
                            context=f"import {alias.name}"
                        )
                        dependencies.append(dep)
                
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        dep = Dependency(
                            source=file_path,
                            target=f"{module}.{alias.name}" if module else alias.name,
                            dependency_type="import",
                            file_path=file_path,
                            line_number=node.lineno,
                            strength=0.8,
                            is_circular=False,
                            context=f"from {module} import {alias.name}"
                        )
                        dependencies.append(dep)
                
                elif isinstance(node, ast.Call):
                    # Analyze function calls
                    if isinstance(node.func, ast.Name):
                        dep = Dependency(
                            source=file_path,
                            target=node.func.id,
                            dependency_type="call",
                            file_path=file_path,
                            line_number=node.lineno,
                            strength=0.6,
                            is_circular=False,
                            context=f"call to {node.func.id}"
                        )
                        dependencies.append(dep)
                
                elif isinstance(node, ast.ClassDef):
                    # Analyze inheritance
                    for base in node.bases:
                        if isinstance(base, ast.Name):
                            dep = Dependency(
                                source=node.name,
                                target=base.id,
                                dependency_type="inheritance",
                                file_path=file_path,
                                line_number=node.lineno,
                                strength=0.9,
                                is_circular=False,
                                context=f"{node.name} inherits from {base.id}"
                            )
                            dependencies.append(dep)
        
        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")
        
        return dependencies

    def _analyze_javascript_dependencies(self, content: str, file_path: str) -> List[Dependency]:
        """Analyze JavaScript dependencies."""
        dependencies = []
        
        # Simple regex-based analysis for JavaScript
        # In production, you'd use a proper JavaScript parser
        
        # Find import statements
        import_patterns = [
            r'import\s+.*?\s+from\s+[\'"]([^\'"]+)[\'"]',
            r'require\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',
            r'import\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',
        ]
        
        for pattern in import_patterns:
            for match in re.finditer(pattern, content):
                line_number = content[:match.start()].count('\n') + 1
                dep = Dependency(
                    source=file_path,
                    target=match.group(1),
                    dependency_type="import",
                    file_path=file_path,
                    line_number=line_number,
                    strength=0.8,
                    is_circular=False,
                    context=match.group(0)
                )
                dependencies.append(dep)
        
        # Find function calls
        call_pattern = r'(\w+)\s*\('
        for match in re.finditer(call_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            dep = Dependency(
                source=file_path,
                target=match.group(1),
                dependency_type="call",
                file_path=file_path,
                line_number=line_number,
                strength=0.4,
                is_circular=False,
                context=f"call to {match.group(1)}"
            )
            dependencies.append(dep)
        
        return dependencies

    def _analyze_typescript_dependencies(self, content: str, file_path: str) -> List[Dependency]:
        """Analyze TypeScript dependencies."""
        # Use JavaScript analyzer as base
        return self._analyze_javascript_dependencies(content, file_path)

    def _analyze_java_dependencies(self, content: str, file_path: str) -> List[Dependency]:
        """Analyze Java dependencies."""
        dependencies = []
        
        # Find import statements
        import_pattern = r'import\s+([^;]+);'
        for match in re.finditer(import_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            dep = Dependency(
                source=file_path,
                target=match.group(1),
                dependency_type="import",
                file_path=file_path,
                line_number=line_number,
                strength=0.8,
                is_circular=False,
                context=match.group(0)
            )
            dependencies.append(dep)
        
        # Find class inheritance
        extends_pattern = r'class\s+(\w+)\s+extends\s+(\w+)'
        for match in re.finditer(extends_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            dep = Dependency(
                source=match.group(1),
                target=match.group(2),
                dependency_type="inheritance",
                file_path=file_path,
                line_number=line_number,
                strength=0.9,
                is_circular=False,
                context=f"{match.group(1)} extends {match.group(2)}"
            )
            dependencies.append(dep)
        
        return dependencies

    def _analyze_cpp_dependencies(self, content: str, file_path: str) -> List[Dependency]:
        """Analyze C++ dependencies."""
        dependencies = []
        
        # Find include statements
        include_pattern = r'#include\s*[<"]([^>"]+)[>"]'
        for match in re.finditer(include_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            dep = Dependency(
                source=file_path,
                target=match.group(1),
                dependency_type="include",
                file_path=file_path,
                line_number=line_number,
                strength=0.8,
                is_circular=False,
                context=match.group(0)
            )
            dependencies.append(dep)
        
        # Find class inheritance
        inheritance_pattern = r'class\s+(\w+)\s*:\s*public\s+(\w+)'
        for match in re.finditer(inheritance_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            dep = Dependency(
                source=match.group(1),
                target=match.group(2),
                dependency_type="inheritance",
                file_path=file_path,
                line_number=line_number,
                strength=0.9,
                is_circular=False,
                context=f"{match.group(1)} inherits from {match.group(2)}"
            )
            dependencies.append(dep)
        
        return dependencies

    def _analyze_go_dependencies(self, content: str, file_path: str) -> List[Dependency]:
        """Analyze Go dependencies."""
        dependencies = []
        
        # Find import statements
        import_pattern = r'import\s+["]([^"]+)["]'
        for match in re.finditer(import_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            dep = Dependency(
                source=file_path,
                target=match.group(1),
                dependency_type="import",
                file_path=file_path,
                line_number=line_number,
                strength=0.8,
                is_circular=False,
                context=match.group(0)
            )
            dependencies.append(dep)
        
        return dependencies

    def _analyze_rust_dependencies(self, content: str, file_path: str) -> List[Dependency]:
        """Analyze Rust dependencies."""
        dependencies = []
        
        # Find use statements
        use_pattern = r'use\s+([^;]+);'
        for match in re.finditer(use_pattern, content):
            line_number = content[:match.start()].count('\n') + 1
            dep = Dependency(
                source=file_path,
                target=match.group(1),
                dependency_type="use",
                file_path=file_path,
                line_number=line_number,
                strength=0.8,
                is_circular=False,
                context=match.group(0)
            )
            dependencies.append(dep)
        
        return dependencies

    def _build_dependency_graphs(self):
        """Build the dependency graphs."""
        for dep in self.dependencies.values():
            # Add to main dependency graph
            self.dependency_graph.add_edge(dep.source, dep.target, 
                                         weight=dep.strength, 
                                         type=dep.dependency_type)
            
            # Add to specific graphs
            if dep.dependency_type == "call":
                self.call_graph.add_edge(dep.source, dep.target, weight=dep.strength)
            elif dep.dependency_type == "inheritance":
                self.inheritance_graph.add_edge(dep.source, dep.target, weight=dep.strength)

    def _detect_circular_dependencies(self) -> List[List[str]]:
        """Detect circular dependencies."""
        try:
            cycles = list(nx.simple_cycles(self.dependency_graph))
            
            # Mark dependencies as circular
            for cycle in cycles:
                for i in range(len(cycle)):
                    source = cycle[i]
                    target = cycle[(i + 1) % len(cycle)]
                    
                    # Find and mark the dependency
                    for dep in self.dependencies.values():
                        if dep.source == source and dep.target == target:
                            dep.is_circular = True
            
            return cycles
        except Exception as e:
            logger.error(f"Error detecting circular dependencies: {e}")
            return []

    def _create_dependency_clusters(self):
        """Create dependency clusters."""
        # Use community detection algorithms
        try:
            # Convert to undirected graph for community detection
            undirected = self.dependency_graph.to_undirected()
            
            # Simple clustering based on connected components
            components = list(nx.connected_components(undirected))
            
            for i, component in enumerate(components):
                if len(component) > 1:  # Only create clusters with multiple members
                    cluster = DependencyCluster(
                        id=f"cluster_{i}",
                        members=list(component),
                        cluster_type="component",
                        cohesion_score=self._calculate_cluster_cohesion(component),
                        coupling_score=self._calculate_cluster_coupling(component),
                        stability_score=self._calculate_cluster_stability(component)
                    )
                    self.clusters[cluster.id] = cluster
        
        except Exception as e:
            logger.error(f"Error creating dependency clusters: {e}")

    def _find_transitive_dependencies(self, symbol: str, visited: Set[str]) -> List[str]:
        """Find transitive dependencies."""
        transitive = []
        
        for successor in self.dependency_graph.successors(symbol):
            if successor not in visited:
                visited.add(successor)
                transitive.append(successor)
                transitive.extend(self._find_transitive_dependencies(successor, visited))
        
        return transitive

    def _calculate_impact_score(self, symbol: str, direct: List[str], 
                               indirect: List[str], change_type: str) -> float:
        """Calculate impact score."""
        base_score = len(direct) * 0.1 + len(indirect) * 0.05
        
        # Adjust based on change type
        multipliers = {
            "modification": 1.0,
            "deletion": 1.5,
            "signature_change": 1.3,
        }
        
        multiplier = multipliers.get(change_type, 1.0)
        return min(base_score * multiplier, 1.0)

    def _determine_risk_level(self, impact_score: float, direct_count: int, indirect_count: int) -> str:
        """Determine risk level."""
        if impact_score > 0.8 or direct_count > 20:
            return "critical"
        elif impact_score > 0.6 or direct_count > 10:
            return "high"
        elif impact_score > 0.3 or direct_count > 5:
            return "medium"
        else:
            return "low"

    def _generate_impact_recommendations(self, symbol: str, direct: List[str], 
                                       indirect: List[str], risk_level: str, 
                                       change_type: str) -> List[str]:
        """Generate impact recommendations."""
        recommendations = []
        
        if risk_level in ["high", "critical"]:
            recommendations.append("Consider creating a deprecation plan")
            recommendations.append("Add comprehensive tests before making changes")
            recommendations.append("Notify all affected teams")
        
        if len(direct) > 10:
            recommendations.append("Consider breaking the change into smaller parts")
        
        if change_type == "signature_change":
            recommendations.append("Consider maintaining backward compatibility")
        
        return recommendations

    def _calculate_coupling_metrics(self) -> Dict[str, float]:
        """Calculate coupling metrics."""
        # Simple coupling metrics
        return {
            "average_coupling": 0.5,
            "max_coupling": 0.8,
            "coupling_distribution": [0.1, 0.3, 0.4, 0.2],
        }

    def _calculate_cohesion_metrics(self) -> Dict[str, float]:
        """Calculate cohesion metrics."""
        # Simple cohesion metrics
        return {
            "average_cohesion": 0.6,
            "min_cohesion": 0.2,
            "cohesion_distribution": [0.2, 0.3, 0.3, 0.2],
        }

    def _calculate_cluster_cohesion(self, component: Set[str]) -> float:
        """Calculate cluster cohesion."""
        # Simple cohesion calculation
        return 0.7

    def _calculate_cluster_coupling(self, component: Set[str]) -> float:
        """Calculate cluster coupling."""
        # Simple coupling calculation
        return 0.3

    def _calculate_cluster_stability(self, component: Set[str]) -> float:
        """Calculate cluster stability."""
        # Simple stability calculation
        return 0.8
