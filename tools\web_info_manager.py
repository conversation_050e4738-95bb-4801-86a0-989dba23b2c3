"""
Web information manager for the Advanced AI Agent.
This coordinates between different web tools and manages the information retrieval process.
"""

import os
import json
import time
import hashlib
import urllib.parse
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Set, Callable

from tools.web import WebTool
from tools.web_scraper import WebScraperTool
from tools.info_synthesizer import InformationSynthesizer
from tools.search_api import SearchAPI
from tools.browser import BrowserTool

class WebInfoManager:
    """Web information manager for coordinating web information retrieval."""

    def __init__(self, cache_dir: Optional[Path] = None):
        """Initialize the web information manager.

        Args:
            cache_dir: The directory to use for caching. If None, will use a default directory.
        """
        # Initialize tools
        self.web_tool = WebTool()
        self.web_scraper = WebScraperTool()
        self.info_synthesizer = InformationSynthesizer()
        self.search_api = SearchAPI()
        self.browser_tool = BrowserTool()

        # Initialize cache
        self.cache_dir = cache_dir or Path.home() / ".advanced_ai_agent" / "cache"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.cache_ttl = 24 * 60 * 60  # 24 hours in seconds

        # Initialize history
        self.history: List[Dict[str, Any]] = []

    def search(self, query: str, num_results: int = 5, use_cache: bool = True) -> List[Dict[str, str]]:
        """Search the web with fallback mechanisms.

        Args:
            query: The query to search for.
            num_results: The number of results to return.
            use_cache: Whether to use cached results if available.

        Returns:
            A list of search results.
        """
        # Add to history
        self.history.append({
            "action": "search",
            "query": query,
            "num_results": num_results,
            "use_cache": use_cache,
            "timestamp": time.time()
        })

        # Check cache if enabled
        if use_cache:
            cached_results = self._get_from_cache("search", query)
            if cached_results:
                self.history[-1]["source"] = "cache"
                return cached_results

        # Try SearchAPI first
        try:
            results = self.search_api.search(query, num_results)

            if results and len(results) > 0 and "Search Error" not in results[0].get("title", ""):
                # Cache the results
                self._save_to_cache("search", query, results)

                self.history[-1]["source"] = "search_api"
                return results
        except Exception as e:
            pass

        # If SearchAPI fails, try WebTool
        try:
            results = self.web_tool.search(query, num_results)

            if results and len(results) > 0 and "Search Error" not in results[0].get("title", ""):
                # Cache the results
                self._save_to_cache("search", query, results)

                self.history[-1]["source"] = "web_tool"
                return results
        except Exception as e:
            pass

        # If WebTool fails, try BrowserTool
        try:
            success, results = self.browser_tool.search(query, "duckduckgo")  # Try DuckDuckGo as it's less likely to block

            if success and results and len(results) > 0 and "Search Error" not in results[0].get("title", ""):
                # Cache the results
                self._save_to_cache("search", query, results)

                self.history[-1]["source"] = "browser_tool"
                return results
        except Exception as e:
            pass

        # If all methods fail, try direct Wikipedia search as a last resort
        try:

            # Format query for Wikipedia URL
            wiki_query = query.replace(" ", "_")
            wiki_url = f"https://en.wikipedia.org/wiki/{wiki_query}"

            # Try to fetch the Wikipedia page
            success, content, metadata = self.fetch_url(wiki_url, use_fallbacks=True)

            if success:
                # Create a search result from the Wikipedia page
                results = [{
                    "title": metadata.get("title", f"{query} - Wikipedia"),
                    "url": wiki_url,
                    "snippet": content[:200] + "..." if len(content) > 200 else content
                }]

                # Cache the results
                self._save_to_cache("search", query, results)

                self.history[-1]["source"] = "wikipedia_direct"
                return results
        except Exception as e:
            pass

        # If all methods fail, try to create a synthetic result with DuckDuckGo
        try:

            # Format query for DuckDuckGo URL
            ddg_query = query.replace(" ", "_")
            ddg_url = f"https://duckduckgo.com/{ddg_query}"

            results = [{
                "title": f"{query}",
                "url": ddg_url,
                "snippet": f"Information about {query}. Click to visit DuckDuckGo."
            }]

            # Add a second result with a more specific URL
            results.append({
                "title": f"{query} - Wikipedia",
                "url": f"https://en.wikipedia.org/wiki/{ddg_query}",
                "snippet": f"Wikipedia article about {query}. Click to visit Wikipedia."
            })

            # Cache the results
            self._save_to_cache("search", query, results)

            self.history[-1]["source"] = "synthetic_results"
            return results
        except Exception as e:
            pass

        # If absolutely everything fails, return an error message with instructions
        error_results = [{
            "title": "Search Error",
            "url": "",
            "snippet": "All search methods failed. Please try a different query or try directly visiting websites like Wikipedia or DuckDuckGo."
        }]

        self.history[-1]["source"] = "error"
        return error_results

    def fetch_url(self, url: str, use_cache: bool = True, use_fallbacks: bool = True) -> Tuple[bool, str, Dict[str, Any]]:
        """Fetch content from a URL with fallback mechanisms.

        Args:
            url: The URL to fetch.
            use_cache: Whether to use cached results if available.
            use_fallbacks: Whether to use fallback mechanisms if the primary method fails.

        Returns:
            A tuple of (success, content, metadata).
        """
        # Add to history
        self.history.append({
            "action": "fetch_url",
            "url": url,
            "use_cache": use_cache,
            "use_fallbacks": use_fallbacks,
            "timestamp": time.time()
        })

        # Check cache if enabled
        if use_cache:
            cached_result = self._get_from_cache("fetch", url)
            if cached_result:
                self.history[-1]["source"] = "cache"
                return cached_result["success"], cached_result["content"], cached_result["metadata"]

        # Try WebScraperTool first (it has its own fallbacks)
        try:
            print(f"Fetching '{url}' using WebScraperTool...")
            success, content, metadata = self.web_scraper.scrape_url(url, use_fallbacks=use_fallbacks)

            if success:
                # Cache the result
                self._save_to_cache("fetch", url, {
                    "success": success,
                    "content": content,
                    "metadata": metadata
                })

                self.history[-1]["source"] = "web_scraper"
                return success, content, metadata
        except Exception as e:
            print(f"WebScraperTool error: {e}")

        # If WebScraperTool fails and fallbacks are enabled, try WebTool
        if use_fallbacks:
            try:
                print(f"WebScraperTool failed, trying WebTool...")
                content, metadata = self.web_tool.fetch_url(url)

                # Check if there was an error
                if "error" in metadata:
                    self.history[-1]["source"] = "error"
                    return False, content, metadata

                # Cache the result
                self._save_to_cache("fetch", url, {
                    "success": True,
                    "content": content,
                    "metadata": metadata
                })

                self.history[-1]["source"] = "web_tool"
                return True, content, metadata
            except Exception as e:
                print(f"WebTool fetch error: {e}")

        # If all methods fail, return an error message
        error_message = "All fetch methods failed. Please try a different URL or try again later."
        error_metadata = {"url": url, "error": error_message}

        self.history[-1]["source"] = "error"
        return False, error_message, error_metadata

    def retrieve_information(self, query: str, max_sources: int = 3, max_length: int = 2000, use_cache: bool = True) -> str:
        """Retrieve and synthesize information about a query.

        Args:
            query: The query to retrieve information about.
            max_sources: The maximum number of sources to use.
            max_length: The maximum length of the synthesized information.
            use_cache: Whether to use cached results if available.

        Returns:
            The synthesized information.
        """
        # Add to history
        self.history.append({
            "action": "retrieve_information",
            "query": query,
            "max_sources": max_sources,
            "max_length": max_length,
            "use_cache": use_cache,
            "timestamp": time.time()
        })

        # Check cache if enabled
        if use_cache:
            cached_result = self._get_from_cache("information", query)
            if cached_result:
                self.history[-1]["source"] = "cache"
                return cached_result

        # Search for relevant sources
        search_results = self.search(query, num_results=max_sources, use_cache=use_cache)

        # Fetch content from each source
        contents = []

        for result in search_results:
            url = result.get("url")
            if not url:
                continue

            # Skip if it's an error result with no URL
            if "Search Error" in result.get("title", "") and not url:
                continue

            try:
                success, content, metadata = self.fetch_url(url, use_cache=use_cache)

                if success and content:
                    contents.append(content)

                    # If we have at least one successful content fetch, that's enough to proceed
                    if len(contents) >= 1:
                        break
            except Exception as e:
                pass
                continue

        # If we couldn't get any content from search results, try direct Wikipedia access
        if not contents:
            try:

                # Format query for Wikipedia URL
                wiki_query = query.replace(" ", "_")
                wiki_url = f"https://en.wikipedia.org/wiki/{wiki_query}"

                success, content, metadata = self.fetch_url(wiki_url, use_cache=use_cache)

                if success and content:
                    contents.append(content)
            except Exception as e:
                pass

        # If we still have no content, generate a synthetic response
        if not contents:
            synthetic_content = f"""
# Information about {query}

I couldn't retrieve specific information about "{query}" from reliable sources at this time.

Possible reasons:
* Network connectivity issues
* Search services may be temporarily unavailable
* The query may be too specific or niche

## Suggestions:
* Try a more general query
* Try again later when services might be available
* Visit Wikipedia, DuckDuckGo, or Google directly to search for this topic
* Check your network connection

## Direct links:
* [Wikipedia](https://en.wikipedia.org/wiki/Special:Search?search={query.replace(' ', '+')})
* [DuckDuckGo](https://duckduckgo.com/?q={query.replace(' ', '+')})
* [Google](https://www.google.com/search?q={query.replace(' ', '+')})
"""
            # Format the synthetic content
            formatted = self.info_synthesizer.format_information(synthetic_content, format_type="markdown")

            # Cache the result
            self._save_to_cache("information", query, formatted)

            self.history[-1]["source"] = "synthetic"
            return formatted

        # Synthesize the information from the contents we have
        synthesized = self.info_synthesizer.combine_information(contents, max_length=max_length)

        # Format the information
        formatted = self.info_synthesizer.format_information(synthesized, format_type="markdown")

        # Cache the result
        self._save_to_cache("information", query, formatted)

        self.history[-1]["source"] = "synthesized"
        return formatted

    def _get_cache_path(self, action: str, key: str) -> Path:
        """Get the path to a cache file.

        Args:
            action: The action (e.g., "search", "fetch").
            key: The cache key (e.g., query or URL).

        Returns:
            The path to the cache file.
        """
        # Create a hash of the key
        key_hash = hashlib.md5(key.encode()).hexdigest()

        # Create the cache path
        return self.cache_dir / f"{action}_{key_hash}.json"

    def _get_from_cache(self, action: str, key: str) -> Any:
        """Get a result from the cache.

        Args:
            action: The action (e.g., "search", "fetch").
            key: The cache key (e.g., query or URL).

        Returns:
            The cached result, or None if not found or expired.
        """
        cache_path = self._get_cache_path(action, key)

        # Check if the cache file exists
        if not cache_path.exists():
            return None

        try:
            # Read the cache file
            with open(cache_path, "r", encoding="utf-8") as f:
                cache_data = json.load(f)

            # Check if the cache is expired
            if time.time() - cache_data.get("timestamp", 0) > self.cache_ttl:
                return None

            return cache_data.get("data")

        except Exception as e:
            pass
            return None

    def _save_to_cache(self, action: str, key: str, data: Any) -> None:
        """Save a result to the cache.

        Args:
            action: The action (e.g., "search", "fetch").
            key: The cache key (e.g., query or URL).
            data: The data to cache.
        """
        cache_path = self._get_cache_path(action, key)

        try:
            # Create the cache data
            cache_data = {
                "timestamp": time.time(),
                "data": data
            }

            # Write the cache file
            with open(cache_path, "w", encoding="utf-8") as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            pass

    def clear_cache(self, older_than: Optional[int] = None) -> int:
        """Clear the cache.

        Args:
            older_than: If provided, only clear cache entries older than this many seconds.

        Returns:
            The number of cache entries cleared.
        """
        # Add to history
        self.history.append({
            "action": "clear_cache",
            "older_than": older_than,
            "timestamp": time.time()
        })

        count = 0

        # Iterate through cache files
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                # If older_than is provided, check the timestamp
                if older_than is not None:
                    with open(cache_file, "r", encoding="utf-8") as f:
                        cache_data = json.load(f)

                    # Skip if not old enough
                    if time.time() - cache_data.get("timestamp", 0) <= older_than:
                        continue

                # Delete the cache file
                cache_file.unlink()
                count += 1

            except Exception as e:
                pass

        self.history[-1]["cleared_count"] = count
        return count

    def get_history(self) -> List[Dict[str, Any]]:
        """Get the manager history.

        Returns:
            The manager history.
        """
        return self.history
