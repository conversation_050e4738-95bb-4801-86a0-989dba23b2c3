"""
Task scheduler for the Advanced AI Agent.
"""

import time
import logging
import threading
import heapq
from typing import Dict, List, Optional, Any, Union, Tuple, Callable, Set
from dataclasses import dataclass, field
from enum import Enum

from tools.threading.thread_manager import ThreadManager

# Set up logging
logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    """Task priority levels."""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3

@dataclass(order=True)
class ScheduledTask:
    """A task scheduled for execution."""
    priority: int
    scheduled_time: float
    task_id: str = field(compare=False)
    executor_name: str = field(compare=False)
    func: Callable = field(compare=False)
    args: Tuple = field(default_factory=tuple, compare=False)
    kwargs: Dict = field(default_factory=dict, compare=False)
    dependencies: Set[str] = field(default_factory=set, compare=False)

class TaskScheduler:
    """Scheduler for prioritized task execution."""
    
    def __init__(self, thread_manager: ThreadManager):
        """Initialize the task scheduler.
        
        Args:
            thread_manager: Thread manager to use for task execution.
        """
        self.thread_manager = thread_manager
        self.task_queue = []  # Priority queue of scheduled tasks
        self.scheduled_tasks: Dict[str, ScheduledTask] = {}
        self.completed_tasks: Set[str] = set()
        self.lock = threading.RLock()
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.running = False
        
    def start(self):
        """Start the scheduler."""
        with self.lock:
            if not self.running:
                self.running = True
                self.scheduler_thread.start()
                logger.debug("Task scheduler started")
    
    def stop(self):
        """Stop the scheduler."""
        with self.lock:
            self.running = False
            logger.debug("Task scheduler stopped")
    
    def schedule_task(self, 
                     task_id: str,
                     executor_name: str,
                     func: Callable,
                     args: Tuple = (),
                     kwargs: Dict = None,
                     priority: TaskPriority = TaskPriority.NORMAL,
                     delay: float = 0.0,
                     dependencies: Set[str] = None) -> str:
        """Schedule a task for execution.
        
        Args:
            task_id: Unique identifier for the task.
            executor_name: Name of the executor to use.
            func: Function to execute.
            args: Arguments to pass to the function.
            kwargs: Keyword arguments to pass to the function.
            priority: Priority of the task.
            delay: Delay in seconds before executing the task.
            dependencies: Set of task IDs that must complete before this task.
            
        Returns:
            Task ID.
        """
        with self.lock:
            # Check if task already exists
            if task_id in self.scheduled_tasks:
                logger.warning(f"Task '{task_id}' already scheduled, replacing")
                self._remove_task(task_id)
            
            # Create the task
            scheduled_time = time.time() + delay
            task = ScheduledTask(
                priority=priority.value,
                scheduled_time=scheduled_time,
                task_id=task_id,
                executor_name=executor_name,
                func=func,
                args=args,
                kwargs=kwargs or {},
                dependencies=dependencies or set()
            )
            
            # Add to queue and dictionary
            heapq.heappush(self.task_queue, task)
            self.scheduled_tasks[task_id] = task
            logger.debug(f"Scheduled task '{task_id}' with priority {priority.name}, delay {delay}s")
            
            return task_id
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a scheduled task.
        
        Args:
            task_id: Task identifier.
            
        Returns:
            Whether the task was successfully cancelled.
        """
        with self.lock:
            if task_id not in self.scheduled_tasks:
                return False
            
            self._remove_task(task_id)
            logger.debug(f"Cancelled task '{task_id}'")
            return True
    
    def _remove_task(self, task_id: str):
        """Remove a task from the scheduler.
        
        Args:
            task_id: Task identifier.
        """
        # Remove from dictionary
        if task_id in self.scheduled_tasks:
            del self.scheduled_tasks[task_id]
        
        # Rebuild queue without the task
        self.task_queue = [task for task in self.task_queue if task.task_id != task_id]
        heapq.heapify(self.task_queue)
    
    def _scheduler_loop(self):
        """Main scheduler loop."""
        while self.running:
            try:
                self._process_queue()
                time.sleep(0.1)  # Small sleep to prevent CPU hogging
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}", exc_info=True)
    
    def _process_queue(self):
        """Process the task queue."""
        with self.lock:
            # Check if there are tasks to process
            if not self.task_queue:
                return
            
            # Get the next task
            task = self.task_queue[0]
            current_time = time.time()
            
            # Check if it's time to execute
            if task.scheduled_time > current_time:
                return
            
            # Check dependencies
            if task.dependencies and not task.dependencies.issubset(self.completed_tasks):
                # Reschedule for later
                task.scheduled_time = current_time + 0.5
                heapq.heapreplace(self.task_queue, task)
                return
            
            # Remove from queue
            heapq.heappop(self.task_queue)
            
            # Submit to thread manager
            try:
                self.thread_manager.submit_task(
                    task.executor_name,
                    task.task_id,
                    self._task_completion_wrapper,
                    task.func,
                    task.task_id,
                    *task.args,
                    **task.kwargs
                )
                logger.debug(f"Executed scheduled task '{task.task_id}'")
            except Exception as e:
                logger.error(f"Error submitting task '{task.task_id}': {e}", exc_info=True)
            
            # Remove from scheduled tasks
            if task.task_id in self.scheduled_tasks:
                del self.scheduled_tasks[task.task_id]
    
    def _task_completion_wrapper(self, func: Callable, task_id: str, *args, **kwargs) -> Any:
        """Wrapper to track task completion.
        
        Args:
            func: Function to execute.
            task_id: Task identifier.
            *args: Arguments to pass to the function.
            **kwargs: Keyword arguments to pass to the function.
            
        Returns:
            Result of the function.
        """
        try:
            result = func(*args, **kwargs)
            with self.lock:
                self.completed_tasks.add(task_id)
            return result
        except Exception as e:
            logger.error(f"Error in task '{task_id}': {e}", exc_info=True)
            raise
    
    def get_scheduled_tasks(self) -> Dict[str, Dict[str, Any]]:
        """Get information about scheduled tasks.
        
        Returns:
            Dictionary mapping task IDs to task information.
        """
        with self.lock:
            return {
                task_id: {
                    "priority": TaskPriority(task.priority).name,
                    "scheduled_time": task.scheduled_time,
                    "executor_name": task.executor_name,
                    "dependencies": list(task.dependencies)
                }
                for task_id, task in self.scheduled_tasks.items()
            }
