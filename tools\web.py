"""
Web tool for the Advanced AI Agent.
Provides robust web operations with comprehensive error handling and improved performance.
"""

import re
import json
import time
import random
import logging
import urllib.parse
from typing import Dict, List, Optional, Any, Union, Tuple, Set

import requests
from bs4 import BeautifulSoup
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

# Get the logger
logger = logging.getLogger("advanced_ai_agent")

class WebToolError(Exception):
    """Base exception for WebTool errors."""
    pass

class FetchError(WebToolError):
    """Exception raised when a URL cannot be fetched."""
    pass

class SearchError(WebToolError):
    """Exception raised when a search fails."""
    pass

class WebTool:
    """Web tool for web operations with enhanced error handling and performance."""

    # Default timeout for requests in seconds
    DEFAULT_TIMEOUT = 30

    # Maximum content size to fetch (in bytes)
    MAX_CONTENT_SIZE = 5 * 1024 * 1024  # 5 MB

    # Common user agents for rotation
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36"
    ]

    def __init__(self):
        """Initialize the web tool with enhanced session configuration."""
        self.history: List[Dict[str, Any]] = []

        # Create a session with retry mechanism
        self.session = self._create_robust_session()

        # Set of visited URLs to avoid duplicates
        self.visited_urls: Set[str] = set()

        # Cache for URL content
        self.url_cache: Dict[str, Dict[str, Any]] = {}

    def search(self, query: str, num_results: int = 5) -> List[Dict[str, str]]:
        """Search the web.

        Args:
            query: The query to search for.
            num_results: The number of results to return.

        Returns:
            A list of search results.
        """
        # Add to history
        self.history.append({"action": "search", "query": query})

        # Encode the query
        encoded_query = urllib.parse.quote_plus(query)

        # Build the URL
        url = f"https://www.google.com/search?q={encoded_query}&num={num_results}"

        try:
            # Send the request with a more realistic user agent
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept-Language": "en-US,en;q=0.9",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Referer": "https://www.google.com/",
                "DNT": "1"
            }

            response = self.session.get(url, headers=headers)
            response.raise_for_status()

            # Parse the response
            soup = BeautifulSoup(response.text, "html.parser")

            # Extract the search results
            results = []

            # Try multiple selector patterns to handle Google's changing layout (2025 updated)
            search_results = (
                soup.select("div.MjjYud") or  # New Google 2025 container
                soup.select("div[data-content-feature='1']") or  # Primary Google selector
                soup.select("div.g") or  # Classic selector
                soup.select("div.tF2Cxc") or  # Alternative container
                soup.select("div.yuRUbf") or  # Title container
                soup.select("div[data-sokoban-container]") or  # Sokoban container
                soup.select("div[jscontroller]") or  # Dynamic content
                soup.select("div[data-ved]")  # Google tracking selector
            )

            # Process search results silently

            for result in search_results:
                # Extract the title - try multiple possible selectors
                title_elem = (result.select_one("h3") or
                             result.select_one(".LC20lb") or
                             result.select_one("[role='heading']"))

                if not title_elem:
                    continue

                title = title_elem.get_text().strip()

                # Extract the URL - try multiple possible selectors
                url_elem = result.select_one("a")
                if not url_elem:
                    continue

                url = url_elem.get("href")
                if url.startswith("/url?q="):
                    url = url[7:]
                    url = url.split("&sa=")[0]

                # Extract the snippet - try multiple possible selectors
                snippet_elem = (result.select_one("div.VwiC3b") or
                               result.select_one(".lyLwlc") or
                               result.select_one("[data-content-feature='1']") or
                               result.select_one("div.s3v9rd") or
                               result.select_one("span.aCOpRe"))

                snippet = snippet_elem.get_text().strip() if snippet_elem else ""

                # Only add if we have at least a title and URL
                if title and url:
                    results.append({
                        "title": title,
                        "url": url,
                        "snippet": snippet
                    })

                    # Stop if we've reached the requested number of results
                    if len(results) >= num_results:
                        break

            # Add to history
            self.history[-1]["results"] = results

            # If we couldn't find any results with the above selectors, try a fallback approach
            if not results:
                # Look for any links with headings nearby as a fallback
                for heading in soup.select("h3"):
                    if len(results) >= num_results:
                        break

                    # Find the closest link
                    link = heading.find_parent("a") or heading.find_next("a")
                    if link and link.get("href"):
                        url = link.get("href")
                        if url.startswith("/url?q="):
                            url = url[7:]
                            url = url.split("&sa=")[0]

                        # Get the snippet - any nearby paragraph or div with text
                        snippet_elem = heading.find_next("div") or heading.find_next("p") or heading.find_next("span")
                        snippet = ""
                        if snippet_elem and snippet_elem.get_text() != heading.get_text():
                            snippet = snippet_elem.get_text().strip()

                        results.append({
                            "title": heading.get_text().strip(),
                            "url": url,
                            "snippet": snippet
                        })

            # Return empty results if nothing found
            if not results:
                results.append({
                    "title": "No Results Found",
                    "url": "",
                    "snippet": "No search results could be extracted. Try a different search query."
                })

            return results

        except Exception as e:
            error_message = f"Error searching: {e}"
            self.history[-1]["error"] = error_message

            # Return a message about the error
            return [{
                "title": "Search Error",
                "url": "",
                "snippet": f"An error occurred while searching: {e}"
            }]

    def fetch_url(self, url: str,
                timeout: Optional[int] = None,
                use_cache: bool = True,
                extract_main_content: bool = True) -> Tuple[str, Dict[str, Any]]:
        """Fetch a URL with enhanced error handling, caching, and content extraction.

        Args:
            url: The URL to fetch.
            timeout: Timeout in seconds. If None, uses DEFAULT_TIMEOUT.
            use_cache: Whether to use the URL cache. Default is True.
            extract_main_content: Whether to extract the main content from HTML. Default is True.

        Returns:
            A tuple of (content, metadata).

        Raises:
            FetchError: If the URL cannot be fetched.
        """
        # Use default timeout if not specified
        if timeout is None:
            timeout = self.DEFAULT_TIMEOUT

        # Normalize the URL
        normalized_url = self._normalize_url(url)

        # Add to history
        self.history.append({
            "action": "fetch",
            "url": url,
            "normalized_url": normalized_url,
            "timeout": timeout,
            "use_cache": use_cache,
            "extract_main_content": extract_main_content,
            "timestamp": time.time()
        })

        # Check cache if enabled
        if use_cache and normalized_url in self.url_cache:
            cached_data = self.url_cache[normalized_url]

            # Add to history
            self.history[-1].update({
                "cached": True,
                "content": cached_data["content"],
                "metadata": cached_data["metadata"]
            })

            logger.info(f"Using cached content for {normalized_url}")

            return cached_data["content"], cached_data["metadata"]

        # Add to visited URLs
        self.visited_urls.add(normalized_url)

        # Rotate user agent
        self._rotate_user_agent()

        try:
            # Log the request
            logger.info(f"Fetching URL: {normalized_url}")

            # Start timer
            start_time = time.time()

            # Send the request with timeout
            response = self.session.get(
                normalized_url,
                timeout=timeout,
                stream=True  # Use streaming to handle large responses
            )
            response.raise_for_status()

            # Get the content type and other headers
            content_type = response.headers.get("Content-Type", "")
            content_length = response.headers.get("Content-Length")

            # Check content length if available
            if content_length and int(content_length) > self.MAX_CONTENT_SIZE:
                logger.warning(f"Content too large: {normalized_url} ({content_length} bytes)")

                # Add to history
                self.history[-1].update({
                    "error": f"Content too large: {content_length} bytes",
                    "content_type": content_type,
                    "content_length": content_length,
                    "duration": time.time() - start_time,
                    "success": False
                })

                raise FetchError(f"Content too large: {content_length} bytes")

            # Read content with size limit
            content_bytes = b""
            for chunk in response.iter_content(chunk_size=1024 * 1024):  # 1MB chunks
                content_bytes += chunk
                if len(content_bytes) > self.MAX_CONTENT_SIZE:
                    logger.warning(f"Content too large (streaming): {normalized_url}")
                    content_bytes = content_bytes[:self.MAX_CONTENT_SIZE]
                    break

            # Try to decode the content
            try:
                content = content_bytes.decode("utf-8")
            except UnicodeDecodeError:
                try:
                    content = content_bytes.decode("latin-1")
                except Exception:
                    # If all decoding fails, use a safe representation
                    content = str(content_bytes)

            # Calculate duration
            duration = time.time() - start_time

            # Initialize metadata
            metadata = {
                "url": url,
                "normalized_url": normalized_url,
                "content_type": content_type,
                "headers": dict(response.headers),
                "status_code": response.status_code,
                "duration": duration,
                "size": len(content_bytes)
            }

            # Process based on content type
            if "text/html" in content_type:
                # Parse HTML
                soup = BeautifulSoup(content, "html.parser")

                # Extract additional metadata
                metadata.update({
                    "title": soup.title.string.strip() if soup.title and soup.title.string else "",
                    "meta_description": soup.find("meta", attrs={"name": "description"}).get("content", "") if soup.find("meta", attrs={"name": "description"}) else "",
                    "meta_keywords": soup.find("meta", attrs={"name": "keywords"}).get("content", "") if soup.find("meta", attrs={"name": "keywords"}) else ""
                })

                # Extract main content if requested
                if extract_main_content:
                    text = self._extract_main_content(soup)
                else:
                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.extract()

                    # Get the text
                    text = soup.get_text()

                    # Clean up the text
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    text = "\n".join(chunk for chunk in chunks if chunk)

                # Cache the content
                if use_cache:
                    self.url_cache[normalized_url] = {
                        "content": text,
                        "metadata": metadata,
                        "timestamp": time.time()
                    }

                # Add to history
                self.history[-1].update({
                    "content": text,
                    "metadata": metadata,
                    "duration": duration,
                    "success": True
                })

                logger.info(f"Successfully fetched HTML from {normalized_url} in {duration:.2f}s")

                return text, metadata

            elif "application/json" in content_type:
                # Parse JSON
                try:
                    data = json.loads(content)

                    # Convert to formatted string
                    text = json.dumps(data, indent=2)

                    # Cache the content
                    if use_cache:
                        self.url_cache[normalized_url] = {
                            "content": text,
                            "metadata": metadata,
                            "timestamp": time.time()
                        }

                    # Add to history
                    self.history[-1].update({
                        "content": text,
                        "metadata": metadata,
                        "duration": duration,
                        "success": True
                    })

                    logger.info(f"Successfully fetched JSON from {normalized_url} in {duration:.2f}s")

                    return text, metadata

                except json.JSONDecodeError:
                    # If JSON parsing fails, return as text
                    logger.warning(f"Failed to parse JSON from {normalized_url}")

                    # Add to history
                    self.history[-1].update({
                        "content": content,
                        "metadata": metadata,
                        "json_parse_error": True,
                        "duration": duration,
                        "success": True
                    })

                    return content, metadata

            else:
                # Return as raw text
                # Cache the content
                if use_cache:
                    self.url_cache[normalized_url] = {
                        "content": content,
                        "metadata": metadata,
                        "timestamp": time.time()
                    }

                # Add to history
                self.history[-1].update({
                    "content": content,
                    "metadata": metadata,
                    "duration": duration,
                    "success": True
                })

                logger.info(f"Successfully fetched content from {normalized_url} in {duration:.2f}s")

                return content, metadata

        except requests.exceptions.Timeout:
            error_message = f"Timeout fetching URL: {normalized_url} (timeout: {timeout}s)"
            logger.error(error_message)

            # Add to history
            self.history[-1].update({
                "error": error_message,
                "timeout": True,
                "duration": time.time() - start_time if 'start_time' in locals() else None,
                "success": False
            })

            raise FetchError(error_message)

        except requests.exceptions.HTTPError as e:
            error_message = f"HTTP error fetching URL: {normalized_url} (status: {e.response.status_code})"
            logger.error(error_message)

            # Add to history
            self.history[-1].update({
                "error": error_message,
                "status_code": e.response.status_code,
                "duration": time.time() - start_time if 'start_time' in locals() else None,
                "success": False
            })

            raise FetchError(error_message)

        except requests.exceptions.RequestException as e:
            error_message = f"Error fetching URL: {normalized_url} ({str(e)})"
            logger.error(error_message)

            # Add to history
            self.history[-1].update({
                "error": error_message,
                "duration": time.time() - start_time if 'start_time' in locals() else None,
                "success": False
            })

            raise FetchError(error_message)

        except Exception as e:
            error_message = f"Unexpected error fetching URL: {normalized_url} ({str(e)})"
            logger.error(error_message)

            # Add to history
            self.history[-1].update({
                "error": error_message,
                "duration": time.time() - start_time if 'start_time' in locals() else None,
                "success": False
            })

            raise FetchError(error_message)

    def _create_robust_session(self) -> requests.Session:
        """Create a requests session with retry mechanism and other optimizations.

        Returns:
            A configured requests session.
        """
        session = requests.Session()

        # Configure retry strategy
        retry_strategy = Retry(
            total=3,  # Maximum number of retries
            backoff_factor=0.5,  # Backoff factor for retries
            status_forcelist=[429, 500, 502, 503, 504],  # Status codes to retry on
            allowed_methods=["GET", "HEAD", "OPTIONS"]  # Methods to retry
        )

        # Mount the adapter to the session
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # Set default headers
        session.headers.update({
            "User-Agent": random.choice(self.USER_AGENTS),
            "Accept-Language": "en-US,en;q=0.9",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "DNT": "1"  # Do Not Track
        })

        return session

    def _rotate_user_agent(self) -> None:
        """Rotate the user agent to avoid detection."""
        self.session.headers.update({
            "User-Agent": random.choice(self.USER_AGENTS)
        })

    def _normalize_url(self, url: str) -> str:
        """Normalize a URL to avoid duplicates.

        Args:
            url: The URL to normalize.

        Returns:
            The normalized URL.
        """
        # Parse the URL
        parsed = urllib.parse.urlparse(url)

        # Ensure the URL has a scheme
        if not parsed.scheme:
            url = "https://" + url
            parsed = urllib.parse.urlparse(url)

        # Remove trailing slash
        path = parsed.path
        if path.endswith("/") and path != "/":
            path = path[:-1]

        # Rebuild the URL
        normalized = urllib.parse.urlunparse((
            parsed.scheme,
            parsed.netloc,
            path,
            parsed.params,
            parsed.query,
            ""  # Remove fragment
        ))

        return normalized

    def _extract_main_content(self, soup: BeautifulSoup) -> str:
        """Extract the main content from a BeautifulSoup object.

        Args:
            soup: The BeautifulSoup object.

        Returns:
            The extracted main content.
        """
        # Try to find the main content area
        main_content = None

        # Look for common content containers
        for selector in ["main", "article", "#content", ".content", "#main", ".main"]:
            content_area = soup.select_one(selector)
            if content_area:
                main_content = content_area
                break

        # If no main content area was found, use the body
        if not main_content:
            main_content = soup.body

        # If still no content, use the whole soup
        if not main_content:
            main_content = soup

        # Remove unwanted elements
        for unwanted in main_content.select("nav, footer, header, aside, .sidebar, .ad, .advertisement, script, style, [role='banner'], [role='navigation']"):
            unwanted.extract()

        # Get the text
        text = main_content.get_text()

        # Clean up the text
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = "\n".join(chunk for chunk in chunks if chunk)

        return text

    def clear_cache(self) -> None:
        """Clear the URL cache."""
        self.url_cache.clear()
        self.visited_urls.clear()
        logger.info("Web cache cleared")

    def get_history(self) -> List[Dict[str, Any]]:
        """Get the web operation history.

        Returns:
            The web operation history.
        """
        return self.history

    def clear_history(self) -> None:
        """Clear the web operation history."""
        self.history.clear()
        logger.info("Web history cleared")
