const http = require('http');

const data = {
  name: 'Example Name',
  age: 30,
  city: 'Example City'
};

const server = http.createServer((req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Access-Control-Allow-Origin', '*'); // Allow cross-origin requests (for testing purposes)
  res.end(JSON.stringify(data));
});

const port = 3000;

server.listen(port, () => {
  console.log(`Server running at http://localhost:${port}/`);
});