"""
Self-Analyzing Intelligence Loop System for the Advanced AI Agent.
Implements recursive analysis, adaptive decision-making, and iterative improvement cycles.
"""

import time
import logging
import threading
import asyncio
from typing import Dict, List, Optional, Any, Callable, Tuple, Union
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import json
import uuid

logger = logging.getLogger(__name__)

class AnalysisType(Enum):
    """Types of analysis that can be performed."""
    CODE_EXECUTION = "code_execution"
    PERFORMANCE = "performance"
    CORRECTNESS = "correctness"
    OPTIMIZATION = "optimization"
    DEBUGGING = "debugging"
    REFACTORING = "refactoring"
    TESTING = "testing"

class DecisionType(Enum):
    """Types of decisions the system can make."""
    CONTINUE = "continue"
    RETRY = "retry"
    OPTIMIZE = "optimize"
    REFACTOR = "refactor"
    DEBUG = "debug"
    COMPLETE = "complete"
    ESCALATE = "escalate"

@dataclass
class AnalysisResult:
    """Result of an analysis iteration."""
    analysis_id: str
    analysis_type: AnalysisType
    timestamp: float
    success: bool
    confidence_score: float
    findings: List[str]
    metrics: Dict[str, Any]
    recommendations: List[str]
    next_actions: List[str]
    execution_time: float
    context: Dict[str, Any]

@dataclass
class DecisionContext:
    """Context for making adaptive decisions."""
    current_state: Dict[str, Any]
    analysis_history: List[AnalysisResult]
    user_intent: str
    constraints: List[str]
    success_criteria: List[str]
    iteration_count: int
    max_iterations: int
    timeout_seconds: float

@dataclass
class AdaptiveDecision:
    """An adaptive decision made by the system."""
    decision_id: str
    decision_type: DecisionType
    reasoning: str
    confidence: float
    actions: List[str]
    expected_outcome: str
    risk_assessment: str
    fallback_plan: Optional[str]

class SelfAnalyzingIntelligence:
    """Self-analyzing intelligence loop system with recursive analysis and adaptive decision-making."""

    def __init__(self, model_manager, workspace_dir: Path):
        """Initialize the self-analyzing intelligence system.

        Args:
            model_manager: The model manager for AI operations
            workspace_dir: The workspace directory
        """
        self.model_manager = model_manager
        self.workspace_dir = workspace_dir
        self.lock = threading.RLock()

        # Analysis state
        self.analysis_history: List[AnalysisResult] = []
        self.decision_history: List[AdaptiveDecision] = []
        self.current_context: Optional[DecisionContext] = None

        # Configuration
        self.max_iterations = 10
        self.timeout_seconds = 300.0  # 5 minutes
        self.confidence_threshold = 0.8
        self.improvement_threshold = 0.1

        # Metrics tracking
        self.success_rate = 0.0
        self.average_iterations = 0.0
        self.total_analyses = 0

        # Callback system for external integration
        self.analysis_callbacks: List[Callable[[AnalysisResult], None]] = []
        self.decision_callbacks: List[Callable[[AdaptiveDecision], None]] = []

    def analyze_and_improve(self,
                          initial_input: str,
                          analysis_type: AnalysisType,
                          success_criteria: List[str],
                          constraints: List[str] = None,
                          max_iterations: int = None) -> List[AnalysisResult]:
        """Execute the self-analyzing intelligence loop.

        Args:
            initial_input: The initial input to analyze
            analysis_type: Type of analysis to perform
            success_criteria: Criteria for determining success
            constraints: Optional constraints to consider
            max_iterations: Maximum number of iterations

        Returns:
            List of analysis results from all iterations
        """
        with self.lock:
            logger.info(f"Starting self-analyzing loop for {analysis_type.value}")

            # Initialize context
            context = DecisionContext(
                current_state={"input": initial_input, "status": "starting"},
                analysis_history=[],
                user_intent=initial_input,
                constraints=constraints or [],
                success_criteria=success_criteria,
                iteration_count=0,
                max_iterations=max_iterations or self.max_iterations,
                timeout_seconds=self.timeout_seconds
            )

            self.current_context = context
            start_time = time.time()

            try:
                # Execute the analysis loop
                while (context.iteration_count < context.max_iterations and
                       time.time() - start_time < context.timeout_seconds):

                    # Perform analysis iteration
                    analysis_result = self._perform_analysis_iteration(
                        context, analysis_type
                    )

                    # Add to history
                    context.analysis_history.append(analysis_result)
                    self.analysis_history.append(analysis_result)

                    # Trigger callbacks
                    for callback in self.analysis_callbacks:
                        try:
                            callback(analysis_result)
                        except Exception as e:
                            logger.error(f"Error in analysis callback: {e}")

                    # Make adaptive decision
                    decision = self._make_adaptive_decision(context)
                    self.decision_history.append(decision)

                    # Trigger decision callbacks
                    for callback in self.decision_callbacks:
                        try:
                            callback(decision)
                        except Exception as e:
                            logger.error(f"Error in decision callback: {e}")

                    # Check if we should continue
                    if decision.decision_type == DecisionType.COMPLETE:
                        logger.info("Analysis completed successfully")
                        break
                    elif decision.decision_type == DecisionType.ESCALATE:
                        logger.warning("Analysis escalated due to complexity")
                        break

                    # Update context for next iteration
                    context.iteration_count += 1
                    context.current_state.update({
                        "last_analysis": asdict(analysis_result),
                        "last_decision": asdict(decision),
                        "iteration": context.iteration_count
                    })

                # Update metrics
                self._update_metrics(context.analysis_history)

                return context.analysis_history

            except Exception as e:
                logger.error(f"Error in self-analyzing loop: {e}", exc_info=True)
                return context.analysis_history

            finally:
                self.current_context = None

    def _perform_analysis_iteration(self,
                                  context: DecisionContext,
                                  analysis_type: AnalysisType) -> AnalysisResult:
        """Perform a single analysis iteration.

        Args:
            context: The decision context
            analysis_type: Type of analysis to perform

        Returns:
            Analysis result
        """
        start_time = time.time()
        analysis_id = str(uuid.uuid4())

        try:
            # Prepare analysis prompt
            analysis_prompt = self._create_analysis_prompt(context, analysis_type)

            # Execute analysis using AI model
            response = self.model_manager.generate(
                prompt=analysis_prompt,
                system_prompt=self._get_analysis_system_prompt(analysis_type)
            )

            # Parse analysis response
            findings, metrics, recommendations, next_actions = self._parse_analysis_response(response)

            # Calculate confidence score
            confidence = self._calculate_confidence_score(findings, metrics, context)

            # Determine success
            success = self._evaluate_success(findings, context.success_criteria, confidence)

            execution_time = time.time() - start_time

            return AnalysisResult(
                analysis_id=analysis_id,
                analysis_type=analysis_type,
                timestamp=time.time(),
                success=success,
                confidence_score=confidence,
                findings=findings,
                metrics=metrics,
                recommendations=recommendations,
                next_actions=next_actions,
                execution_time=execution_time,
                context={"iteration": context.iteration_count, "input": context.user_intent}
            )

        except Exception as e:
            logger.error(f"Error in analysis iteration: {e}", exc_info=True)
            return AnalysisResult(
                analysis_id=analysis_id,
                analysis_type=analysis_type,
                timestamp=time.time(),
                success=False,
                confidence_score=0.0,
                findings=[f"Analysis failed: {str(e)}"],
                metrics={"error": str(e)},
                recommendations=["Review input and try again"],
                next_actions=["debug", "retry"],
                execution_time=time.time() - start_time,
                context={"error": str(e)}
            )

    def _make_adaptive_decision(self, context: DecisionContext) -> AdaptiveDecision:
        """Make an adaptive decision based on analysis results.

        Args:
            context: The decision context

        Returns:
            Adaptive decision
        """
        decision_id = str(uuid.uuid4())

        if not context.analysis_history:
            return AdaptiveDecision(
                decision_id=decision_id,
                decision_type=DecisionType.CONTINUE,
                reasoning="No analysis history available",
                confidence=0.5,
                actions=["perform_initial_analysis"],
                expected_outcome="Initial analysis completed",
                risk_assessment="Low risk",
                fallback_plan="Retry with different approach"
            )

        latest_analysis = context.analysis_history[-1]

        # Decision logic based on analysis results
        if latest_analysis.success and latest_analysis.confidence_score >= self.confidence_threshold:
            decision_type = DecisionType.COMPLETE
            reasoning = f"Analysis successful with confidence {latest_analysis.confidence_score:.2f}"
            actions = ["finalize_results"]

        elif context.iteration_count >= context.max_iterations:
            decision_type = DecisionType.ESCALATE
            reasoning = "Maximum iterations reached without satisfactory results"
            actions = ["escalate_to_human", "provide_partial_results"]

        elif latest_analysis.confidence_score < 0.3:
            decision_type = DecisionType.DEBUG
            reasoning = "Low confidence indicates potential issues"
            actions = ["debug_analysis", "review_input", "adjust_approach"]

        elif self._should_optimize(context):
            decision_type = DecisionType.OPTIMIZE
            reasoning = "Optimization opportunities identified"
            actions = ["apply_optimizations", "measure_improvements"]

        elif self._should_refactor(context):
            decision_type = DecisionType.REFACTOR
            reasoning = "Refactoring needed for better results"
            actions = ["refactor_approach", "simplify_logic"]

        else:
            decision_type = DecisionType.RETRY
            reasoning = "Results not satisfactory, retrying with improvements"
            actions = ["adjust_parameters", "retry_analysis"]

        # Calculate confidence in decision
        decision_confidence = self._calculate_decision_confidence(context, decision_type)

        return AdaptiveDecision(
            decision_id=decision_id,
            decision_type=decision_type,
            reasoning=reasoning,
            confidence=decision_confidence,
            actions=actions,
            expected_outcome=self._predict_outcome(decision_type, context),
            risk_assessment=self._assess_risk(decision_type, context),
            fallback_plan=self._create_fallback_plan(decision_type)
        )

    def _create_analysis_prompt(self, context: DecisionContext, analysis_type: AnalysisType) -> str:
        """Create analysis prompt based on context and type."""
        base_prompt = f"""
Analyze the following based on {analysis_type.value}:

Input: {context.user_intent}
Current State: {json.dumps(context.current_state, indent=2)}
Iteration: {context.iteration_count + 1}/{context.max_iterations}
Success Criteria: {', '.join(context.success_criteria)}
Constraints: {', '.join(context.constraints)}

Previous Analysis Results:
"""

        for i, analysis in enumerate(context.analysis_history[-3:], 1):  # Last 3 analyses
            base_prompt += f"""
Analysis {i}:
- Success: {analysis.success}
- Confidence: {analysis.confidence_score:.2f}
- Findings: {', '.join(analysis.findings[:3])}
- Recommendations: {', '.join(analysis.recommendations[:2])}
"""

        base_prompt += """
Please provide:
1. Detailed findings about the current state
2. Metrics and measurements
3. Specific recommendations for improvement
4. Next actions to take
5. Confidence assessment

Format your response as JSON with keys: findings, metrics, recommendations, next_actions, confidence_notes
"""
        return base_prompt

    def _get_analysis_system_prompt(self, analysis_type: AnalysisType) -> str:
        """Get system prompt for specific analysis type."""
        prompts = {
            AnalysisType.CODE_EXECUTION: """You are an expert code execution analyzer. Focus on:
- Code correctness and functionality
- Runtime behavior and output
- Error detection and handling
- Performance characteristics
- Edge cases and boundary conditions""",

            AnalysisType.PERFORMANCE: """You are a performance optimization expert. Focus on:
- Execution time and efficiency
- Memory usage and optimization
- Resource utilization
- Bottleneck identification
- Scalability considerations""",

            AnalysisType.CORRECTNESS: """You are a code correctness validator. Focus on:
- Logic accuracy and completeness
- Algorithm implementation
- Data structure usage
- Input/output validation
- Compliance with requirements""",

            AnalysisType.OPTIMIZATION: """You are an optimization specialist. Focus on:
- Code efficiency improvements
- Algorithm optimization
- Resource usage reduction
- Performance enhancement opportunities
- Best practice implementation""",

            AnalysisType.DEBUGGING: """You are a debugging expert. Focus on:
- Error identification and root cause analysis
- Bug pattern recognition
- Fix recommendations
- Prevention strategies
- Testing approaches""",

            AnalysisType.REFACTORING: """You are a refactoring specialist. Focus on:
- Code structure improvement
- Maintainability enhancement
- Design pattern application
- Code duplication removal
- Readability optimization""",

            AnalysisType.TESTING: """You are a testing expert. Focus on:
- Test case design and coverage
- Test strategy development
- Quality assurance
- Validation approaches
- Automated testing opportunities"""
        }

        return prompts.get(analysis_type, "You are an expert code analyst.")

    def _parse_analysis_response(self, response: str) -> Tuple[List[str], Dict[str, Any], List[str], List[str]]:
        """Parse AI analysis response."""
        try:
            # Try to parse as JSON first
            if response.strip().startswith('{'):
                data = json.loads(response)
                return (
                    data.get('findings', []),
                    data.get('metrics', {}),
                    data.get('recommendations', []),
                    data.get('next_actions', [])
                )
        except json.JSONDecodeError:
            pass

        # Fallback to text parsing
        findings = []
        metrics = {}
        recommendations = []
        next_actions = []

        lines = response.split('\n')
        current_section = None

        for line in lines:
            line = line.strip()
            if 'finding' in line.lower():
                current_section = 'findings'
            elif 'metric' in line.lower():
                current_section = 'metrics'
            elif 'recommendation' in line.lower():
                current_section = 'recommendations'
            elif 'action' in line.lower():
                current_section = 'next_actions'
            elif line and current_section:
                if current_section == 'findings':
                    findings.append(line)
                elif current_section == 'recommendations':
                    recommendations.append(line)
                elif current_section == 'next_actions':
                    next_actions.append(line)

        return findings, metrics, recommendations, next_actions

    def _calculate_confidence_score(self, findings: List[str], metrics: Dict[str, Any], context: DecisionContext) -> float:
        """Calculate confidence score for analysis results."""
        base_confidence = 0.5

        # Boost confidence based on findings quality
        if len(findings) >= 3:
            base_confidence += 0.2

        # Boost confidence based on metrics availability
        if metrics:
            base_confidence += 0.1

        # Boost confidence based on iteration success
        if context.analysis_history:
            recent_successes = sum(1 for a in context.analysis_history[-3:] if a.success)
            base_confidence += (recent_successes / 3) * 0.2

        return min(base_confidence, 1.0)

    def _evaluate_success(self, findings: List[str], success_criteria: List[str], confidence: float) -> bool:
        """Evaluate if analysis meets success criteria."""
        if confidence < 0.5:
            return False

        if not findings:
            return False

        # Check if findings address success criteria
        criteria_met = 0
        for criterion in success_criteria:
            for finding in findings:
                if any(word in finding.lower() for word in criterion.lower().split()):
                    criteria_met += 1
                    break

        return criteria_met >= len(success_criteria) * 0.7  # 70% of criteria met

    def _should_optimize(self, context: DecisionContext) -> bool:
        """Determine if optimization should be performed."""
        if not context.analysis_history:
            return False

        latest = context.analysis_history[-1]
        return (latest.success and
                'optimization' in ' '.join(latest.recommendations).lower() and
                latest.confidence_score > 0.6)

    def _should_refactor(self, context: DecisionContext) -> bool:
        """Determine if refactoring should be performed."""
        if len(context.analysis_history) < 2:
            return False

        # Check if multiple iterations suggest structural issues
        structural_issues = 0
        for analysis in context.analysis_history[-3:]:
            if any('structure' in finding.lower() or 'refactor' in finding.lower()
                   for finding in analysis.findings):
                structural_issues += 1

        return structural_issues >= 2

    def _calculate_decision_confidence(self, context: DecisionContext, decision_type: DecisionType) -> float:
        """Calculate confidence in a decision."""
        base_confidence = 0.6

        if context.analysis_history:
            latest_confidence = context.analysis_history[-1].confidence_score
            base_confidence = (base_confidence + latest_confidence) / 2

        # Adjust based on decision type
        adjustments = {
            DecisionType.COMPLETE: 0.1,
            DecisionType.CONTINUE: 0.0,
            DecisionType.RETRY: -0.1,
            DecisionType.DEBUG: -0.2,
            DecisionType.ESCALATE: -0.3
        }

        return max(0.1, min(1.0, base_confidence + adjustments.get(decision_type, 0.0)))

    def _predict_outcome(self, decision_type: DecisionType, context: DecisionContext) -> str:
        """Predict the outcome of a decision."""
        predictions = {
            DecisionType.COMPLETE: "Analysis completed with satisfactory results",
            DecisionType.CONTINUE: "Analysis will proceed to next iteration",
            DecisionType.RETRY: "Improved results expected with adjusted approach",
            DecisionType.OPTIMIZE: "Performance and efficiency improvements expected",
            DecisionType.REFACTOR: "Better code structure and maintainability expected",
            DecisionType.DEBUG: "Issues will be identified and resolved",
            DecisionType.ESCALATE: "Human intervention required for complex issues"
        }
        return predictions.get(decision_type, "Outcome uncertain")

    def _assess_risk(self, decision_type: DecisionType, context: DecisionContext) -> str:
        """Assess risk of a decision."""
        risk_levels = {
            DecisionType.COMPLETE: "Low - results are satisfactory",
            DecisionType.CONTINUE: "Low - standard iteration",
            DecisionType.RETRY: "Medium - may not improve results",
            DecisionType.OPTIMIZE: "Medium - changes may introduce issues",
            DecisionType.REFACTOR: "High - significant structural changes",
            DecisionType.DEBUG: "Medium - debugging may take time",
            DecisionType.ESCALATE: "Low - human oversight reduces risk"
        }
        return risk_levels.get(decision_type, "Unknown risk level")

    def _create_fallback_plan(self, decision_type: DecisionType) -> str:
        """Create fallback plan for a decision."""
        fallbacks = {
            DecisionType.COMPLETE: "Review results if issues arise",
            DecisionType.CONTINUE: "Switch to debugging if problems persist",
            DecisionType.RETRY: "Escalate if retry fails",
            DecisionType.OPTIMIZE: "Revert changes if optimization fails",
            DecisionType.REFACTOR: "Maintain original structure if refactoring fails",
            DecisionType.DEBUG: "Escalate if debugging doesn't resolve issues",
            DecisionType.ESCALATE: "Provide partial results and recommendations"
        }
        return fallbacks.get(decision_type, "Escalate to human intervention")

    def _update_metrics(self, analysis_history: List[AnalysisResult]):
        """Update system metrics based on analysis history."""
        if not analysis_history:
            return

        self.total_analyses += len(analysis_history)
        successful_analyses = sum(1 for a in analysis_history if a.success)
        self.success_rate = successful_analyses / len(analysis_history)
        self.average_iterations = len(analysis_history)

        logger.info(f"Updated metrics - Success rate: {self.success_rate:.2f}, "
                   f"Average iterations: {self.average_iterations:.1f}")

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status and metrics."""
        return {
            "total_analyses": self.total_analyses,
            "success_rate": self.success_rate,
            "average_iterations": self.average_iterations,
            "current_context": asdict(self.current_context) if self.current_context else None,
            "recent_analyses": len(self.analysis_history[-10:]),
            "recent_decisions": len(self.decision_history[-10:])
        }

    def add_analysis_callback(self, callback: Callable[[AnalysisResult], None]):
        """Add callback for analysis results."""
        self.analysis_callbacks.append(callback)

    def add_decision_callback(self, callback: Callable[[AdaptiveDecision], None]):
        """Add callback for decisions."""
        self.decision_callbacks.append(callback)
