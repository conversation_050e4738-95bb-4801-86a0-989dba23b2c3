"""
Context-Aware Completion for the AI Code Assistant.

This module provides intelligent code completion that understands context,
analyzes surrounding code, and suggests relevant completions based on patterns.
"""

import time
import logging
import threading
import ast
import re
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class CompletionContext:
    """Represents the context for code completion."""
    file_path: str
    line_number: int
    column_number: int
    current_line: str
    surrounding_lines: List[str]
    cursor_position: int
    partial_code: str
    language: str
    scope_info: Dict[str, Any]
    imports: List[str]
    available_symbols: List[str]

@dataclass
class CompletionSuggestion:
    """Represents a code completion suggestion."""
    text: str
    type: str  # function, variable, class, keyword, snippet
    description: str
    confidence: float
    priority: int
    documentation: Optional[str]
    insert_text: str
    detail: str
    source: str  # local, imported, builtin, inferred

@dataclass
class CompletionResult:
    """Result of code completion analysis."""
    suggestions: List[CompletionSuggestion]
    context_analysis: Dict[str, Any]
    completion_time: float
    total_suggestions: int
    filtered_suggestions: int

class ContextAwareCompletion:
    """Context-aware code completion system."""

    def __init__(self, workspace_dir: Path, semantic_indexer=None, model_manager=None):
        """Initialize the context-aware completion system.
        
        Args:
            workspace_dir: The workspace directory
            semantic_indexer: Optional semantic indexer for symbol lookup
            model_manager: Optional model manager for AI-assisted completion
        """
        self.workspace_dir = workspace_dir
        self.semantic_indexer = semantic_indexer
        self.model_manager = model_manager
        self.completion_cache: Dict[str, CompletionResult] = {}
        self.symbol_database: Dict[str, Dict[str, Any]] = {}
        self.completion_history: List[CompletionResult] = []
        self.lock = threading.RLock()
        
        # Language-specific completers
        self.language_completers = {
            "python": self._complete_python,
            "javascript": self._complete_javascript,
            "typescript": self._complete_typescript,
            "java": self._complete_java,
        }
        
        # Built-in symbols for different languages
        self.builtin_symbols = {
            "python": self._get_python_builtins(),
            "javascript": self._get_javascript_builtins(),
            "typescript": self._get_typescript_builtins(),
            "java": self._get_java_builtins(),
        }

    def get_completions(self, context: CompletionContext) -> CompletionResult:
        """Get code completions for the given context.
        
        Args:
            context: The completion context
            
        Returns:
            Completion result with suggestions
        """
        with self.lock:
            start_time = time.time()
            
            # Check cache first
            cache_key = self._generate_cache_key(context)
            if cache_key in self.completion_cache:
                cached_result = self.completion_cache[cache_key]
                logger.debug(f"Returning cached completion for {context.file_path}:{context.line_number}")
                return cached_result
            
            logger.info(f"Generating completions for {context.language} at {context.file_path}:{context.line_number}")
            
            # Analyze context
            context_analysis = self._analyze_completion_context(context)
            
            # Get language-specific completions
            suggestions = []
            if context.language in self.language_completers:
                suggestions = self.language_completers[context.language](context, context_analysis)
            else:
                suggestions = self._complete_generic(context, context_analysis)
            
            # Add semantic completions if available
            if self.semantic_indexer:
                semantic_suggestions = self._get_semantic_completions(context, context_analysis)
                suggestions.extend(semantic_suggestions)
            
            # Filter and rank suggestions
            filtered_suggestions = self._filter_and_rank_suggestions(suggestions, context)
            
            completion_time = time.time() - start_time
            
            result = CompletionResult(
                suggestions=filtered_suggestions,
                context_analysis=context_analysis,
                completion_time=completion_time,
                total_suggestions=len(suggestions),
                filtered_suggestions=len(filtered_suggestions)
            )
            
            # Cache result
            self.completion_cache[cache_key] = result
            
            # Store in history
            self.completion_history.append(result)
            
            logger.info(f"Generated {len(filtered_suggestions)} completions in {completion_time:.3f}s")
            return result

    def _analyze_completion_context(self, context: CompletionContext) -> Dict[str, Any]:
        """Analyze the completion context."""
        analysis = {
            "completion_type": "unknown",
            "in_string": False,
            "in_comment": False,
            "after_dot": False,
            "in_function_call": False,
            "in_import": False,
            "expected_type": None,
            "scope_depth": 0,
            "indentation_level": 0,
        }
        
        current_line = context.current_line
        cursor_pos = context.cursor_position
        
        # Check if in string or comment
        before_cursor = current_line[:cursor_pos]
        analysis["in_string"] = self._is_in_string(before_cursor)
        analysis["in_comment"] = self._is_in_comment(before_cursor, context.language)
        
        # Check if after dot (member access)
        analysis["after_dot"] = before_cursor.rstrip().endswith(".")
        
        # Check if in function call
        analysis["in_function_call"] = "(" in before_cursor and ")" not in before_cursor[before_cursor.rfind("("):]
        
        # Check if in import statement
        analysis["in_import"] = self._is_in_import(current_line, context.language)
        
        # Calculate indentation level
        analysis["indentation_level"] = len(current_line) - len(current_line.lstrip())
        
        # Determine completion type
        analysis["completion_type"] = self._determine_completion_type(context, analysis)
        
        return analysis

    def _complete_python(self, context: CompletionContext, analysis: Dict[str, Any]) -> List[CompletionSuggestion]:
        """Generate Python-specific completions."""
        suggestions = []
        
        # Skip completion in strings and comments
        if analysis["in_string"] or analysis["in_comment"]:
            return suggestions
        
        # Import completions
        if analysis["in_import"]:
            suggestions.extend(self._get_python_import_completions(context))
        
        # Member access completions
        elif analysis["after_dot"]:
            suggestions.extend(self._get_python_member_completions(context))
        
        # Keyword completions
        else:
            suggestions.extend(self._get_python_keyword_completions(context))
            suggestions.extend(self._get_python_builtin_completions(context))
            suggestions.extend(self._get_local_symbol_completions(context))
        
        return suggestions

    def _complete_javascript(self, context: CompletionContext, analysis: Dict[str, Any]) -> List[CompletionSuggestion]:
        """Generate JavaScript-specific completions."""
        suggestions = []
        
        if analysis["in_string"] or analysis["in_comment"]:
            return suggestions
        
        # Add JavaScript keywords
        js_keywords = ["function", "var", "let", "const", "if", "else", "for", "while", "return", "class", "extends"]
        for keyword in js_keywords:
            if keyword.startswith(context.partial_code.lower()):
                suggestions.append(CompletionSuggestion(
                    text=keyword,
                    type="keyword",
                    description=f"JavaScript keyword: {keyword}",
                    confidence=0.8,
                    priority=1,
                    documentation=None,
                    insert_text=keyword,
                    detail="keyword",
                    source="builtin"
                ))
        
        # Add built-in objects
        suggestions.extend(self._get_javascript_builtin_completions(context))
        
        return suggestions

    def _complete_typescript(self, context: CompletionContext, analysis: Dict[str, Any]) -> List[CompletionSuggestion]:
        """Generate TypeScript-specific completions."""
        # Use JavaScript completer as base and add TypeScript-specific features
        suggestions = self._complete_javascript(context, analysis)
        
        # Add TypeScript keywords
        ts_keywords = ["interface", "type", "enum", "namespace", "module", "declare", "abstract"]
        for keyword in ts_keywords:
            if keyword.startswith(context.partial_code.lower()):
                suggestions.append(CompletionSuggestion(
                    text=keyword,
                    type="keyword",
                    description=f"TypeScript keyword: {keyword}",
                    confidence=0.8,
                    priority=1,
                    documentation=None,
                    insert_text=keyword,
                    detail="keyword",
                    source="builtin"
                ))
        
        return suggestions

    def _complete_java(self, context: CompletionContext, analysis: Dict[str, Any]) -> List[CompletionSuggestion]:
        """Generate Java-specific completions."""
        suggestions = []
        
        if analysis["in_string"] or analysis["in_comment"]:
            return suggestions
        
        # Add Java keywords
        java_keywords = ["public", "private", "protected", "static", "final", "class", "interface", "extends", "implements"]
        for keyword in java_keywords:
            if keyword.startswith(context.partial_code.lower()):
                suggestions.append(CompletionSuggestion(
                    text=keyword,
                    type="keyword",
                    description=f"Java keyword: {keyword}",
                    confidence=0.8,
                    priority=1,
                    documentation=None,
                    insert_text=keyword,
                    detail="keyword",
                    source="builtin"
                ))
        
        return suggestions

    def _complete_generic(self, context: CompletionContext, analysis: Dict[str, Any]) -> List[CompletionSuggestion]:
        """Generate generic completions for unsupported languages."""
        suggestions = []
        
        # Add basic programming keywords
        generic_keywords = ["if", "else", "for", "while", "function", "return", "class", "import"]
        for keyword in generic_keywords:
            if keyword.startswith(context.partial_code.lower()):
                suggestions.append(CompletionSuggestion(
                    text=keyword,
                    type="keyword",
                    description=f"Keyword: {keyword}",
                    confidence=0.5,
                    priority=1,
                    documentation=None,
                    insert_text=keyword,
                    detail="keyword",
                    source="builtin"
                ))
        
        return suggestions

    def _get_python_import_completions(self, context: CompletionContext) -> List[CompletionSuggestion]:
        """Get Python import completions."""
        suggestions = []
        
        # Common Python modules
        common_modules = ["os", "sys", "json", "re", "time", "datetime", "collections", "itertools", "functools"]
        
        for module in common_modules:
            if module.startswith(context.partial_code):
                suggestions.append(CompletionSuggestion(
                    text=module,
                    type="module",
                    description=f"Python module: {module}",
                    confidence=0.7,
                    priority=2,
                    documentation=f"Standard library module: {module}",
                    insert_text=module,
                    detail="module",
                    source="builtin"
                ))
        
        return suggestions

    def _get_python_member_completions(self, context: CompletionContext) -> List[CompletionSuggestion]:
        """Get Python member access completions."""
        suggestions = []
        
        # Extract the object before the dot
        before_cursor = context.current_line[:context.cursor_position]
        dot_pos = before_cursor.rfind(".")
        if dot_pos > 0:
            object_name = before_cursor[:dot_pos].split()[-1]
            
            # Common methods for different types
            if object_name in ["str", "string"] or object_name.startswith('"') or object_name.startswith("'"):
                string_methods = ["split", "join", "replace", "strip", "lower", "upper", "format"]
                for method in string_methods:
                    suggestions.append(CompletionSuggestion(
                        text=method,
                        type="method",
                        description=f"String method: {method}",
                        confidence=0.8,
                        priority=1,
                        documentation=f"String method: {method}()",
                        insert_text=f"{method}()",
                        detail="method",
                        source="builtin"
                    ))
            
            elif object_name in ["list", "lst"] or object_name.startswith("["):
                list_methods = ["append", "extend", "insert", "remove", "pop", "index", "count", "sort"]
                for method in list_methods:
                    suggestions.append(CompletionSuggestion(
                        text=method,
                        type="method",
                        description=f"List method: {method}",
                        confidence=0.8,
                        priority=1,
                        documentation=f"List method: {method}()",
                        insert_text=f"{method}()",
                        detail="method",
                        source="builtin"
                    ))
        
        return suggestions

    def _get_python_keyword_completions(self, context: CompletionContext) -> List[CompletionSuggestion]:
        """Get Python keyword completions."""
        suggestions = []
        
        python_keywords = [
            "and", "as", "assert", "break", "class", "continue", "def", "del", "elif", "else",
            "except", "finally", "for", "from", "global", "if", "import", "in", "is", "lambda",
            "nonlocal", "not", "or", "pass", "raise", "return", "try", "while", "with", "yield"
        ]
        
        for keyword in python_keywords:
            if keyword.startswith(context.partial_code.lower()):
                suggestions.append(CompletionSuggestion(
                    text=keyword,
                    type="keyword",
                    description=f"Python keyword: {keyword}",
                    confidence=0.9,
                    priority=1,
                    documentation=None,
                    insert_text=keyword,
                    detail="keyword",
                    source="builtin"
                ))
        
        return suggestions

    def _get_python_builtin_completions(self, context: CompletionContext) -> List[CompletionSuggestion]:
        """Get Python built-in function completions."""
        suggestions = []
        
        for builtin_name, builtin_info in self.builtin_symbols["python"].items():
            if builtin_name.startswith(context.partial_code.lower()):
                suggestions.append(CompletionSuggestion(
                    text=builtin_name,
                    type=builtin_info["type"],
                    description=builtin_info["description"],
                    confidence=0.7,
                    priority=2,
                    documentation=builtin_info.get("documentation"),
                    insert_text=builtin_info.get("insert_text", builtin_name),
                    detail="builtin",
                    source="builtin"
                ))
        
        return suggestions

    def _get_javascript_builtin_completions(self, context: CompletionContext) -> List[CompletionSuggestion]:
        """Get JavaScript built-in completions."""
        suggestions = []
        
        for builtin_name, builtin_info in self.builtin_symbols["javascript"].items():
            if builtin_name.startswith(context.partial_code.lower()):
                suggestions.append(CompletionSuggestion(
                    text=builtin_name,
                    type=builtin_info["type"],
                    description=builtin_info["description"],
                    confidence=0.7,
                    priority=2,
                    documentation=builtin_info.get("documentation"),
                    insert_text=builtin_info.get("insert_text", builtin_name),
                    detail="builtin",
                    source="builtin"
                ))
        
        return suggestions

    def _get_local_symbol_completions(self, context: CompletionContext) -> List[CompletionSuggestion]:
        """Get completions for local symbols."""
        suggestions = []
        
        # Extract symbols from available_symbols in context
        for symbol in context.available_symbols:
            if symbol.startswith(context.partial_code):
                suggestions.append(CompletionSuggestion(
                    text=symbol,
                    type="variable",
                    description=f"Local symbol: {symbol}",
                    confidence=0.8,
                    priority=1,
                    documentation=None,
                    insert_text=symbol,
                    detail="local",
                    source="local"
                ))
        
        return suggestions

    def _get_semantic_completions(self, context: CompletionContext, analysis: Dict[str, Any]) -> List[CompletionSuggestion]:
        """Get semantic completions using the semantic indexer."""
        suggestions = []
        
        try:
            # Search for similar symbols
            search_results = self.semantic_indexer.search_semantic(
                context.partial_code, max_results=10
            )
            
            for result in search_results:
                if result.symbol:
                    suggestions.append(CompletionSuggestion(
                        text=result.symbol.name,
                        type=result.symbol.type,
                        description=f"From {result.symbol.file_path}: {result.symbol.signature or result.symbol.name}",
                        confidence=result.relevance_score,
                        priority=3,
                        documentation=result.symbol.docstring,
                        insert_text=result.symbol.name,
                        detail="semantic",
                        source="indexed"
                    ))
        
        except Exception as e:
            logger.warning(f"Error getting semantic completions: {e}")
        
        return suggestions

    def _filter_and_rank_suggestions(self, suggestions: List[CompletionSuggestion], 
                                   context: CompletionContext) -> List[CompletionSuggestion]:
        """Filter and rank completion suggestions."""
        # Remove duplicates
        seen = set()
        unique_suggestions = []
        for suggestion in suggestions:
            if suggestion.text not in seen:
                seen.add(suggestion.text)
                unique_suggestions.append(suggestion)
        
        # Filter by partial code match
        filtered = []
        for suggestion in unique_suggestions:
            if suggestion.text.lower().startswith(context.partial_code.lower()):
                filtered.append(suggestion)
        
        # Sort by priority, confidence, and alphabetically
        filtered.sort(key=lambda s: (-s.priority, -s.confidence, s.text))
        
        # Limit to top 50 suggestions
        return filtered[:50]

    def _generate_cache_key(self, context: CompletionContext) -> str:
        """Generate cache key for completion context."""
        return f"{context.file_path}:{context.line_number}:{context.column_number}:{context.partial_code}"

    def _is_in_string(self, text: str) -> bool:
        """Check if cursor is inside a string."""
        single_quotes = text.count("'") - text.count("\\'")
        double_quotes = text.count('"') - text.count('\\"')
        return (single_quotes % 2 == 1) or (double_quotes % 2 == 1)

    def _is_in_comment(self, text: str, language: str) -> bool:
        """Check if cursor is inside a comment."""
        if language == "python":
            return "#" in text and text.rfind("#") > text.rfind('"') and text.rfind("#") > text.rfind("'")
        elif language in ["javascript", "typescript", "java"]:
            return "//" in text
        return False

    def _is_in_import(self, line: str, language: str) -> bool:
        """Check if cursor is in an import statement."""
        line_stripped = line.strip()
        if language == "python":
            return line_stripped.startswith("import ") or line_stripped.startswith("from ")
        elif language in ["javascript", "typescript"]:
            return "import " in line or "require(" in line
        elif language == "java":
            return line_stripped.startswith("import ")
        return False

    def _determine_completion_type(self, context: CompletionContext, analysis: Dict[str, Any]) -> str:
        """Determine the type of completion needed."""
        if analysis["in_import"]:
            return "import"
        elif analysis["after_dot"]:
            return "member"
        elif analysis["in_function_call"]:
            return "parameter"
        else:
            return "general"

    def _get_python_builtins(self) -> Dict[str, Dict[str, Any]]:
        """Get Python built-in symbols."""
        return {
            "print": {"type": "function", "description": "Print objects to the text stream", "insert_text": "print()"},
            "len": {"type": "function", "description": "Return the length of an object", "insert_text": "len()"},
            "range": {"type": "function", "description": "Return a sequence of numbers", "insert_text": "range()"},
            "str": {"type": "class", "description": "String class", "insert_text": "str()"},
            "int": {"type": "class", "description": "Integer class", "insert_text": "int()"},
            "list": {"type": "class", "description": "List class", "insert_text": "list()"},
            "dict": {"type": "class", "description": "Dictionary class", "insert_text": "dict()"},
            "open": {"type": "function", "description": "Open a file", "insert_text": "open()"},
        }

    def _get_javascript_builtins(self) -> Dict[str, Dict[str, Any]]:
        """Get JavaScript built-in symbols."""
        return {
            "console": {"type": "object", "description": "Console object for logging", "insert_text": "console"},
            "document": {"type": "object", "description": "Document object", "insert_text": "document"},
            "window": {"type": "object", "description": "Window object", "insert_text": "window"},
            "Array": {"type": "class", "description": "Array constructor", "insert_text": "Array()"},
            "Object": {"type": "class", "description": "Object constructor", "insert_text": "Object()"},
            "String": {"type": "class", "description": "String constructor", "insert_text": "String()"},
            "Number": {"type": "class", "description": "Number constructor", "insert_text": "Number()"},
        }

    def _get_typescript_builtins(self) -> Dict[str, Dict[str, Any]]:
        """Get TypeScript built-in symbols."""
        # Use JavaScript builtins as base
        builtins = self._get_javascript_builtins()
        # Add TypeScript-specific builtins
        builtins.update({
            "Promise": {"type": "class", "description": "Promise class", "insert_text": "Promise"},
            "Map": {"type": "class", "description": "Map class", "insert_text": "Map()"},
            "Set": {"type": "class", "description": "Set class", "insert_text": "Set()"},
        })
        return builtins

    def _get_java_builtins(self) -> Dict[str, Dict[str, Any]]:
        """Get Java built-in symbols."""
        return {
            "System": {"type": "class", "description": "System class", "insert_text": "System"},
            "String": {"type": "class", "description": "String class", "insert_text": "String"},
            "Integer": {"type": "class", "description": "Integer class", "insert_text": "Integer"},
            "ArrayList": {"type": "class", "description": "ArrayList class", "insert_text": "ArrayList"},
            "HashMap": {"type": "class", "description": "HashMap class", "insert_text": "HashMap"},
        }

    def clear_cache(self):
        """Clear the completion cache."""
        with self.lock:
            self.completion_cache.clear()
            logger.info("Cleared completion cache")

    def get_completion_statistics(self) -> Dict[str, Any]:
        """Get completion statistics."""
        with self.lock:
            if not self.completion_history:
                return {"total_completions": 0}
            
            total_completions = len(self.completion_history)
            avg_suggestions = sum(r.filtered_suggestions for r in self.completion_history) / total_completions
            avg_time = sum(r.completion_time for r in self.completion_history) / total_completions
            
            return {
                "total_completions": total_completions,
                "average_suggestions": avg_suggestions,
                "average_completion_time": avg_time,
                "cache_size": len(self.completion_cache),
            }
