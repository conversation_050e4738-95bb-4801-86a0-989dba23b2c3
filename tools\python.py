"""
Python tool for the Advanced AI Agent.
"""

import sys
import io
import traceback
from typing import Dict, List, Optional, Any, Union, Tuple

class PythonTool:
    """Python tool for executing Python code."""
    
    def __init__(self):
        """Initialize the Python tool."""
        self.globals = {}
        self.locals = {}
        self.history: List[Dict[str, str]] = []
    
    def execute(self, code: str) -> Tuple[str, str, Any]:
        """Execute Python code.
        
        Args:
            code: The Python code to execute.
            
        Returns:
            A tuple of (stdout, stderr, result).
        """
        # Add to history
        self.history.append({"code": code})
        
        # Capture stdout and stderr
        old_stdout = sys.stdout
        old_stderr = sys.stderr
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()
        sys.stdout = stdout_capture
        sys.stderr = stderr_capture
        
        result = None
        
        try:
            # Execute the code
            compiled_code = compile(code, "<string>", "exec")
            exec(compiled_code, self.globals, self.locals)
            
            # Try to get the result of the last expression
            last_line = code.strip().split("\n")[-1]
            if not last_line.startswith(" ") and not last_line.startswith("\t") and not last_line.strip().endswith(":"):
                try:
                    result_code = compile(last_line, "<string>", "eval")
                    result = eval(result_code, self.globals, self.locals)
                except SyntaxError:
                    # Not a valid expression, ignore
                    pass
            
            # Get stdout and stderr
            stdout = stdout_capture.getvalue()
            stderr = stderr_capture.getvalue()
            
            # Add to history
            self.history[-1]["stdout"] = stdout
            self.history[-1]["stderr"] = stderr
            self.history[-1]["result"] = repr(result) if result is not None else None
            
            return stdout, stderr, result
        
        except Exception as e:
            # Get the traceback
            stderr = stderr_capture.getvalue()
            stderr += traceback.format_exc()
            
            # Add to history
            self.history[-1]["stderr"] = stderr
            self.history[-1]["error"] = str(e)
            
            return stdout_capture.getvalue(), stderr, None
        
        finally:
            # Restore stdout and stderr
            sys.stdout = old_stdout
            sys.stderr = old_stderr
    
    def get_history(self) -> List[Dict[str, str]]:
        """Get the execution history.
        
        Returns:
            The execution history.
        """
        return self.history
    
    def get_variables(self) -> Dict[str, Any]:
        """Get the current variables.
        
        Returns:
            The current variables.
        """
        return {**self.globals, **self.locals}
    
    def reset(self) -> None:
        """Reset the Python tool."""
        self.globals = {}
        self.locals = {}
        self.history = []
