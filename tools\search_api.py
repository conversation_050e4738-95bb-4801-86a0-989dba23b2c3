"""
Search API tool for the Advanced AI Agent.
This provides a more reliable search mechanism using free search APIs.
"""

import os
import json
import urllib.parse
import requests
from typing import Dict, List, Optional, Any, Union, Tuple

class SearchAPI:
    """Search API tool for reliable web search."""

    def __init__(self):
        """Initialize the search API tool."""
        self.history: List[Dict[str, str]] = []
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        })

        # List of available search APIs
        self.apis = [
            self._search_serpapi,
            self._search_serper,
            self._search_serpstack,
            self._search_duckduckgo_api
        ]

    def search(self, query: str, num_results: int = 5) -> List[Dict[str, str]]:
        """Search the web using available APIs.

        Args:
            query: The query to search for.
            num_results: The number of results to return.

        Returns:
            A list of search results.
        """
        # Add to history
        self.history.append({"action": "search", "query": query})

        # Try each API in order until one works
        for api_func in self.apis:
            try:
                results = api_func(query, num_results)
                if results:
                    # Add to history
                    self.history[-1]["results"] = results
                    return results
            except Exception as e:
                continue

        # If all APIs fail, return a message silently
        return [{
            "title": "Search Error",
            "url": "",
            "snippet": "All search APIs failed. Please try again later or use a different search method."
        }]

    def _search_serpapi(self, query: str, num_results: int = 5) -> List[Dict[str, str]]:
        """Search using SerpAPI.

        Args:
            query: The query to search for.
            num_results: The number of results to return.

        Returns:
            A list of search results.
        """
        # SerpAPI requires an API key
        api_key = os.environ.get("SERPAPI_KEY")
        if not api_key:
            return []

        # Encode the query
        encoded_query = urllib.parse.quote_plus(query)

        # Build the URL
        url = f"https://serpapi.com/search.json?q={encoded_query}&num={num_results}&api_key={api_key}"

        # Send the request
        response = self.session.get(url)
        response.raise_for_status()

        # Parse the response
        data = response.json()

        # Extract the search results
        results = []
        for result in data.get("organic_results", []):
            results.append({
                "title": result.get("title", ""),
                "url": result.get("link", ""),
                "snippet": result.get("snippet", "")
            })

        return results[:num_results]

    def _search_serper(self, query: str, num_results: int = 5) -> List[Dict[str, str]]:
        """Search using Serper.dev.

        Args:
            query: The query to search for.
            num_results: The number of results to return.

        Returns:
            A list of search results.
        """
        # Serper requires an API key
        api_key = os.environ.get("SERPER_API_KEY")
        if not api_key:
            return []

        # Build the URL
        url = "https://serpapi.com/search"

        # Build the payload
        payload = {
            "q": query,
            "num": num_results
        }

        # Build the headers
        headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }

        # Send the request
        response = self.session.post(url, json=payload, headers=headers)
        response.raise_for_status()

        # Parse the response
        data = response.json()

        # Extract the search results
        results = []
        for result in data.get("organic", []):
            results.append({
                "title": result.get("title", ""),
                "url": result.get("link", ""),
                "snippet": result.get("snippet", "")
            })

        return results[:num_results]

    def _search_serpstack(self, query: str, num_results: int = 5) -> List[Dict[str, str]]:
        """Search using SerpStack.

        Args:
            query: The query to search for.
            num_results: The number of results to return.

        Returns:
            A list of search results.
        """
        # SerpStack requires an API key
        api_key = os.environ.get("SERPSTACK_API_KEY")
        if not api_key:
            return []

        # Encode the query
        encoded_query = urllib.parse.quote_plus(query)

        # Build the URL
        url = f"http://api.serpstack.com/search?access_key={api_key}&query={encoded_query}&num={num_results}"

        # Send the request
        response = self.session.get(url)
        response.raise_for_status()

        # Parse the response
        data = response.json()

        # Extract the search results
        results = []
        for result in data.get("organic_results", []):
            results.append({
                "title": result.get("title", ""),
                "url": result.get("url", ""),
                "snippet": result.get("snippet", "")
            })

        return results[:num_results]

    def _search_duckduckgo_api(self, query: str, num_results: int = 5) -> List[Dict[str, str]]:
        """Search using DuckDuckGo API.

        Args:
            query: The query to search for.
            num_results: The number of results to return.

        Returns:
            A list of search results.
        """
        # Encode the query
        encoded_query = urllib.parse.quote_plus(query)

        # Build the URL (DuckDuckGo doesn't have an official API, but this endpoint works)
        url = f"https://api.duckduckgo.com/?q={encoded_query}&format=json"

        # Send the request
        response = self.session.get(url)
        response.raise_for_status()

        # Parse the response
        data = response.json()

        # Extract the search results
        results = []

        # Add the abstract if available
        if data.get("Abstract"):
            results.append({
                "title": data.get("Heading", ""),
                "url": data.get("AbstractURL", ""),
                "snippet": data.get("Abstract", "")
            })

        # Add the related topics
        for topic in data.get("RelatedTopics", [])[:num_results]:
            if "Topics" in topic:
                # This is a category
                continue

            results.append({
                "title": topic.get("Text", "").split(" - ")[0],
                "url": topic.get("FirstURL", ""),
                "snippet": topic.get("Text", "")
            })

        return results[:num_results]

    def get_history(self) -> List[Dict[str, str]]:
        """Get the search history.

        Returns:
            The search history.
        """
        return self.history
