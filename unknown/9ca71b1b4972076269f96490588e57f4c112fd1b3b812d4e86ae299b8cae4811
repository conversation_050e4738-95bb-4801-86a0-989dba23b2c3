"""
Iterative Controller for enforcing step-by-step execution methodology.
This module ensures that all operations follow the iterative approach:
1. Execute one step at a time
2. Analyze results thoroughly  
3. Plan next step based on analysis
4. Continue iterative process
5. Validate against user requirements
6. Provide clear status updates
"""

import time
import json
import logging
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StepStatus(Enum):
    """Status of execution steps."""
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    ANALYZING = "analyzing"
    PLANNING = "planning"

@dataclass
class ExecutionStep:
    """Represents a single execution step."""
    step_id: str
    description: str
    operation: str
    parameters: Dict[str, Any]
    status: StepStatus = StepStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    result: Optional[Any] = None
    error: Optional[str] = None
    analysis: Optional[str] = None
    next_step_plan: Optional[str] = None
    validation_notes: Optional[str] = None

@dataclass
class IterativeSession:
    """Represents an iterative execution session."""
    session_id: str
    user_requirements: str
    steps: List[ExecutionStep] = field(default_factory=list)
    current_step_index: int = 0
    session_start_time: float = field(default_factory=time.time)
    session_status: str = "active"
    final_validation: Optional[str] = None

class IterativeController:
    """
    Controller that enforces iterative step-by-step execution methodology.
    """
    
    def __init__(self, workspace_dir: Optional[Path] = None):
        """Initialize the iterative controller."""
        self.workspace_dir = workspace_dir or Path.cwd()
        self.current_session: Optional[IterativeSession] = None
        self.sessions_history: List[IterativeSession] = []
        self.step_executors: Dict[str, Callable] = {}
        
        # Initialize session storage
        self.sessions_dir = self.workspace_dir / "iterative_sessions"
        self.sessions_dir.mkdir(exist_ok=True)
        
        logger.info("IterativeController initialized with step-by-step methodology")
    
    def start_session(self, user_requirements: str, session_id: Optional[str] = None) -> str:
        """
        Start a new iterative session.
        
        Args:
            user_requirements: The user's requirements and goals
            session_id: Optional custom session ID
            
        Returns:
            The session ID
        """
        if session_id is None:
            session_id = f"session_{int(time.time())}"
        
        self.current_session = IterativeSession(
            session_id=session_id,
            user_requirements=user_requirements
        )
        
        logger.info(f"🚀 Started iterative session: {session_id}")
        logger.info(f"📋 User Requirements: {user_requirements}")
        
        return session_id
    
    def add_step(self, description: str, operation: str, parameters: Dict[str, Any]) -> str:
        """
        Add a step to the current session.
        
        Args:
            description: Human-readable description of the step
            operation: The operation to execute
            parameters: Parameters for the operation
            
        Returns:
            The step ID
        """
        if not self.current_session:
            raise ValueError("No active session. Start a session first.")
        
        step_id = f"step_{len(self.current_session.steps) + 1}"
        step = ExecutionStep(
            step_id=step_id,
            description=description,
            operation=operation,
            parameters=parameters
        )
        
        self.current_session.steps.append(step)
        logger.info(f"➕ Added step {step_id}: {description}")
        
        return step_id
    
    def execute_next_step(self) -> Tuple[bool, str, Any]:
        """
        Execute the next step in the current session following iterative methodology.
        
        Returns:
            Tuple of (success, status_message, result)
        """
        if not self.current_session:
            return False, "No active session", None
        
        if self.current_step_index >= len(self.current_session.steps):
            return False, "No more steps to execute", None
        
        current_step = self.current_session.steps[self.current_step_index]
        
        try:
            # Step 1: Execute one step at a time
            logger.info(f"🔄 STEP {self.current_step_index + 1}: EXECUTING")
            logger.info(f"📝 Description: {current_step.description}")
            
            current_step.status = StepStatus.EXECUTING
            current_step.start_time = time.time()
            
            # Execute the operation
            if current_step.operation in self.step_executors:
                result = self.step_executors[current_step.operation](current_step.parameters)
            else:
                result = self._default_executor(current_step.operation, current_step.parameters)
            
            current_step.end_time = time.time()
            current_step.result = result
            current_step.status = StepStatus.COMPLETED
            
            execution_time = current_step.end_time - current_step.start_time
            logger.info(f"✅ Step completed in {execution_time:.2f}s")
            
            # Step 2: Analyze results thoroughly
            logger.info(f"🔍 ANALYZING RESULTS...")
            current_step.status = StepStatus.ANALYZING
            analysis = self._analyze_step_results(current_step)
            current_step.analysis = analysis
            logger.info(f"📊 Analysis: {analysis}")
            
            # Step 3: Plan next step based on analysis
            logger.info(f"🎯 PLANNING NEXT STEP...")
            current_step.status = StepStatus.PLANNING
            next_plan = self._plan_next_step(current_step)
            current_step.next_step_plan = next_plan
            logger.info(f"📋 Next Step Plan: {next_plan}")
            
            # Step 4: Continue iterative process (increment step index)
            self.current_step_index += 1
            
            # Step 5: Validate against user requirements
            validation = self._validate_against_requirements(current_step)
            current_step.validation_notes = validation
            logger.info(f"✔️ Validation: {validation}")
            
            # Step 6: Provide clear status updates
            status_update = self._generate_status_update(current_step)
            logger.info(f"📢 STATUS UPDATE: {status_update}")
            
            # Save session state
            self._save_session()
            
            return True, status_update, result
            
        except Exception as e:
            current_step.status = StepStatus.FAILED
            current_step.error = str(e)
            current_step.end_time = time.time()
            
            error_msg = f"❌ Step {current_step.step_id} failed: {str(e)}"
            logger.error(error_msg)
            
            return False, error_msg, None
    
    def _analyze_step_results(self, step: ExecutionStep) -> str:
        """Analyze the results of a completed step."""
        analysis_points = []
        
        # Analyze execution time
        if step.start_time and step.end_time:
            execution_time = step.end_time - step.start_time
            if execution_time > 10:
                analysis_points.append(f"Long execution time ({execution_time:.2f}s) - consider optimization")
            else:
                analysis_points.append(f"Normal execution time ({execution_time:.2f}s)")
        
        # Analyze result type and content
        if step.result is not None:
            result_type = type(step.result).__name__
            analysis_points.append(f"Result type: {result_type}")
            
            if isinstance(step.result, str):
                if "error" in step.result.lower():
                    analysis_points.append("Result contains error indicators")
                elif "success" in step.result.lower():
                    analysis_points.append("Result indicates successful operation")
                    
                result_length = len(step.result)
                analysis_points.append(f"Result length: {result_length} characters")
        else:
            analysis_points.append("No result returned - may indicate void operation")
        
        # Analyze operation type
        analysis_points.append(f"Operation '{step.operation}' completed")
        
        return " | ".join(analysis_points)
    
    def _plan_next_step(self, current_step: ExecutionStep) -> str:
        """Plan the next step based on current step analysis."""
        if not self.current_session:
            return "No active session for planning"
        
        remaining_steps = len(self.current_session.steps) - self.current_step_index
        
        if remaining_steps == 0:
            return "No more steps planned - session completion imminent"
        
        next_step = self.current_session.steps[self.current_step_index]
        
        # Analyze if current step affects next step
        plan_notes = []
        plan_notes.append(f"Next: {next_step.description}")
        plan_notes.append(f"Remaining steps: {remaining_steps}")
        
        # Check if current step result should influence next step
        if current_step.result and isinstance(current_step.result, str):
            if "error" in current_step.result.lower():
                plan_notes.append("⚠️ Previous step had errors - may need error handling")
            elif "file" in current_step.result.lower():
                plan_notes.append("📁 File operation detected - next step may use file")
        
        return " | ".join(plan_notes)
    
    def _validate_against_requirements(self, step: ExecutionStep) -> str:
        """Validate step results against user requirements."""
        if not self.current_session:
            return "No session requirements to validate against"
        
        requirements = self.current_session.user_requirements.lower()
        validation_notes = []
        
        # Check if step aligns with requirements
        step_desc = step.description.lower()
        operation = step.operation.lower()
        
        # Look for requirement keywords in step
        requirement_keywords = ["create", "build", "implement", "develop", "generate", "setup", "install"]
        step_keywords = [kw for kw in requirement_keywords if kw in step_desc or kw in operation]
        
        if step_keywords:
            validation_notes.append(f"Aligns with requirements: {', '.join(step_keywords)}")
        
        # Check progress towards requirements
        progress_pct = ((self.current_step_index) / len(self.current_session.steps)) * 100
        validation_notes.append(f"Progress: {progress_pct:.1f}% complete")
        
        # Check if step result indicates progress
        if step.result and isinstance(step.result, str):
            if "success" in step.result.lower():
                validation_notes.append("✅ Step contributes to requirement fulfillment")
            elif "error" in step.result.lower():
                validation_notes.append("⚠️ Step may hinder requirement fulfillment")
        
        return " | ".join(validation_notes)
    
    def _generate_status_update(self, step: ExecutionStep) -> str:
        """Generate a comprehensive status update."""
        if not self.current_session:
            return "No active session"
        
        update_parts = []
        update_parts.append(f"STEP {self.current_step_index}/{len(self.current_session.steps)} COMPLETED")
        update_parts.append(f"Operation: {step.operation}")
        update_parts.append(f"Status: {step.status.value}")
        
        if step.start_time and step.end_time:
            execution_time = step.end_time - step.start_time
            update_parts.append(f"Time: {execution_time:.2f}s")
        
        # Add next step info if available
        if self.current_step_index < len(self.current_session.steps):
            next_step = self.current_session.steps[self.current_step_index]
            update_parts.append(f"Next: {next_step.description}")
        else:
            update_parts.append("Next: Session completion")
        
        return " | ".join(update_parts)
    
    def _default_executor(self, operation: str, parameters: Dict[str, Any]) -> Any:
        """Default executor for operations without specific handlers."""
        logger.info(f"Executing operation '{operation}' with parameters: {parameters}")
        return f"Operation '{operation}' executed with parameters: {json.dumps(parameters)}"
    
    def register_executor(self, operation: str, executor: Callable) -> None:
        """Register a custom executor for an operation type."""
        self.step_executors[operation] = executor
        logger.info(f"Registered executor for operation: {operation}")
    
    def _save_session(self) -> None:
        """Save the current session to disk."""
        if not self.current_session:
            return
        
        session_file = self.sessions_dir / f"{self.current_session.session_id}.json"
        
        # Convert session to serializable format
        session_data = {
            "session_id": self.current_session.session_id,
            "user_requirements": self.current_session.user_requirements,
            "session_start_time": self.current_session.session_start_time,
            "session_status": self.current_session.session_status,
            "current_step_index": self.current_step_index,
            "steps": []
        }
        
        for step in self.current_session.steps:
            step_data = {
                "step_id": step.step_id,
                "description": step.description,
                "operation": step.operation,
                "parameters": step.parameters,
                "status": step.status.value,
                "start_time": step.start_time,
                "end_time": step.end_time,
                "result": str(step.result) if step.result else None,
                "error": step.error,
                "analysis": step.analysis,
                "next_step_plan": step.next_step_plan,
                "validation_notes": step.validation_notes
            }
            session_data["steps"].append(step_data)
        
        with open(session_file, 'w') as f:
            json.dump(session_data, f, indent=2)
    
    def get_session_summary(self) -> str:
        """Get a summary of the current session."""
        if not self.current_session:
            return "No active session"
        
        summary_parts = []
        summary_parts.append(f"Session: {self.current_session.session_id}")
        summary_parts.append(f"Requirements: {self.current_session.user_requirements}")
        summary_parts.append(f"Steps: {self.current_step_index}/{len(self.current_session.steps)} completed")
        
        if self.current_session.steps:
            completed_steps = [s for s in self.current_session.steps if s.status == StepStatus.COMPLETED]
            failed_steps = [s for s in self.current_session.steps if s.status == StepStatus.FAILED]
            
            summary_parts.append(f"Success rate: {len(completed_steps)}/{len(self.current_session.steps)}")
            
            if failed_steps:
                summary_parts.append(f"Failed steps: {len(failed_steps)}")
        
        return " | ".join(summary_parts)
