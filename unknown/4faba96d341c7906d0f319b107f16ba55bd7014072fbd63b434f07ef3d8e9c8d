"""
Conversation management for the Advanced AI Agent.
"""

import os
import json
import time
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any, Union

from pydantic import BaseModel, Field

class Message(BaseModel):
    """A message in a conversation."""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    role: str
    content: str
    timestamp: float = Field(default_factory=time.time)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the message to a dictionary.

        Returns:
            The message as a dictionary.
        """
        return {
            "id": self.id,
            "role": self.role,
            "content": self.content,
            "timestamp": self.timestamp
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Message":
        """Create a message from a dictionary.

        Args:
            data: The dictionary to create the message from.

        Returns:
            The created message.
        """
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            role=data["role"],
            content=data["content"],
            timestamp=data.get("timestamp", time.time())
        )

class Conversation(BaseModel):
    """A conversation with the AI agent."""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    messages: List[Message] = Field(default_factory=list)
    created_at: float = Field(default_factory=time.time)
    updated_at: float = Field(default_factory=time.time)
    summary: Optional[str] = None

    def add_message(self, role: str, content: str) -> Message:
        """Add a message to the conversation.

        Args:
            role: The role of the message sender.
            content: The content of the message.

        Returns:
            The added message.
        """
        message = Message(role=role, content=content)
        self.messages.append(message)
        self.updated_at = time.time()
        return message

    def get_messages_by_role(self, role: str) -> List[Message]:
        """Get all messages with a specific role.

        Args:
            role: The role to filter by.

        Returns:
            A list of messages with the specified role.
        """
        return [msg for msg in self.messages if msg.role == role]

    def get_last_n_messages(self, n: int) -> List[Message]:
        """Get the last N messages in the conversation.

        Args:
            n: The number of messages to get.

        Returns:
            The last N messages.
        """
        return self.messages[-n:] if n < len(self.messages) else self.messages[:]

    def get_message_count(self) -> int:
        """Get the number of messages in the conversation.

        Returns:
            The number of messages.
        """
        return len(self.messages)

    def set_summary(self, summary: str) -> None:
        """Set a summary for the conversation.

        Args:
            summary: The summary to set.
        """
        self.summary = summary

    def to_dict(self) -> Dict[str, Any]:
        """Convert the conversation to a dictionary.

        Returns:
            The conversation as a dictionary.
        """
        result = {
            "id": self.id,
            "name": self.name,
            "messages": [message.to_dict() for message in self.messages],
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

        # Add summary if it exists
        if self.summary:
            result["summary"] = self.summary

        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Conversation":
        """Create a conversation from a dictionary.

        Args:
            data: The dictionary to create the conversation from.

        Returns:
            The created conversation.
        """
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            name=data["name"],
            messages=[Message.from_dict(message) for message in data.get("messages", [])],
            created_at=data.get("created_at", time.time()),
            updated_at=data.get("updated_at", time.time()),
            summary=data.get("summary")
        )

    def save(self, directory: Path) -> Path:
        """Save the conversation to a file.

        Args:
            directory: The directory to save the conversation to.

        Returns:
            The path to the saved conversation.
        """
        # Create the directory if it doesn't exist
        directory.mkdir(parents=True, exist_ok=True)

        # Create the file path
        file_path = directory / f"{self.id}.json"

        # Save the conversation
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(self.to_dict(), f, indent=2)

        return file_path

    @classmethod
    def load(cls, file_path: Path) -> "Conversation":
        """Load a conversation from a file.

        Args:
            file_path: The path to the file to load the conversation from.

        Returns:
            The loaded conversation.
        """
        # Check if the file exists
        if not file_path.exists():
            raise FileNotFoundError(f"Conversation file not found: {file_path}")

        # Load the conversation
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        return cls.from_dict(data)

class ConversationManager:
    """Manager for conversations."""

    def __init__(self, history_dir: Path):
        """Initialize the conversation manager.

        Args:
            history_dir: The directory to store conversation history in.
        """
        self.history_dir = history_dir
        self.history_dir.mkdir(parents=True, exist_ok=True)
        self.current_conversation: Optional[Conversation] = None
        self.auto_summarize_threshold: int = 20  # Number of messages before auto-summarizing
        self.conversation_cache: Dict[str, Conversation] = {}  # Cache for loaded conversations
        self.max_cache_size: int = 10  # Maximum number of conversations to keep in memory

        # Load the most recent conversation if available
        self._load_most_recent_conversation()

    def new_conversation(self, name: Optional[str] = None) -> Conversation:
        """Create a new conversation.

        Args:
            name: The name of the conversation. If None, will generate a name.

        Returns:
            The created conversation.
        """
        # Generate a name if not provided
        if name is None:
            name = f"Conversation {time.strftime('%Y-%m-%d %H:%M:%S')}"

        # Create the conversation
        self.current_conversation = Conversation(name=name)

        # Save the conversation
        self.save_conversation()

        return self.current_conversation

    def _load_most_recent_conversation(self) -> None:
        """Load the most recent conversation if available."""
        conversations = self.list_conversations()
        if conversations:
            # Get the most recent conversation
            most_recent = conversations[0]
            # Load it
            self.load_conversation(most_recent["id"])

    def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Get a conversation by ID.

        Args:
            conversation_id: The ID of the conversation to get.

        Returns:
            The conversation, or None if not found.
        """
        # Check if the conversation is the current one
        if self.current_conversation and self.current_conversation.id == conversation_id:
            return self.current_conversation

        # Check if the conversation is in the cache
        if conversation_id in self.conversation_cache:
            return self.conversation_cache[conversation_id]

        # Check if the conversation file exists
        file_path = self.history_dir / f"{conversation_id}.json"
        if not file_path.exists():
            return None

        # Load the conversation
        conversation = Conversation.load(file_path)

        # Add to cache
        self._add_to_cache(conversation)

        return conversation

    def _add_to_cache(self, conversation: Conversation) -> None:
        """Add a conversation to the cache.

        Args:
            conversation: The conversation to add to the cache.
        """
        # Add to cache
        self.conversation_cache[conversation.id] = conversation

        # If cache is too large, remove the oldest conversations
        if len(self.conversation_cache) > self.max_cache_size:
            # Sort by updated_at (oldest first)
            sorted_ids = sorted(
                self.conversation_cache.keys(),
                key=lambda cid: self.conversation_cache[cid].updated_at
            )

            # Remove the oldest conversation
            oldest_id = sorted_ids[0]
            del self.conversation_cache[oldest_id]

    def list_conversations(self) -> List[Dict[str, Any]]:
        """List all conversations.

        Returns:
            A list of conversation metadata.
        """
        # Get all conversation files
        conversation_files = list(self.history_dir.glob("*.json"))

        # Load the conversations
        conversations = []
        for file_path in conversation_files:
            try:
                conversation = Conversation.load(file_path)
                conversations.append({
                    "id": conversation.id,
                    "name": conversation.name,
                    "created_at": conversation.created_at,
                    "updated_at": conversation.updated_at,
                    "message_count": len(conversation.messages)
                })
            except Exception:
                # Skip invalid conversation files
                pass

        # Sort by updated_at (newest first)
        conversations.sort(key=lambda c: c["updated_at"], reverse=True)

        return conversations

    def summarize_conversation(self, conversation: Conversation, model_manager=None) -> Optional[str]:
        """Summarize a conversation.

        Args:
            conversation: The conversation to summarize.
            model_manager: The model manager to use for summarization.

        Returns:
            The summary, or None if summarization failed.
        """
        # If no model manager is provided, we can't summarize
        if model_manager is None:
            return None

        # If the conversation has fewer than 5 messages, don't summarize
        if len(conversation.messages) < 5:
            return None

        try:
            # Extract user and assistant messages for summarization
            messages = [msg for msg in conversation.messages if msg.role in ["user", "assistant"]]

            # Format the messages for the summarization prompt
            conversation_text = "\n".join([
                f"{msg.role.upper()}: {msg.content}"
                for msg in messages
            ])

            # Create the summarization prompt
            prompt = f"""Please summarize the following conversation in a concise paragraph.
Focus on the main topics discussed and key information exchanged.

{conversation_text}

SUMMARY:"""

            # Generate the summary
            summary = model_manager.generate(prompt=prompt)

            # Set the summary on the conversation
            conversation.set_summary(summary)

            return summary
        except Exception as e:
            print(f"Error summarizing conversation: {e}")
            return None

    def save_conversation(self) -> None:
        """Save the current conversation."""
        if self.current_conversation:
            # Check if we should auto-summarize
            if (self.current_conversation.get_message_count() >= self.auto_summarize_threshold and
                not self.current_conversation.summary):
                # We'll try to summarize in the future when we have a model manager
                pass

            # Save to disk
            self.current_conversation.save(self.history_dir)

            # Update cache
            self._add_to_cache(self.current_conversation)

    def load_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Load a conversation and set it as the current conversation.

        Args:
            conversation_id: The ID of the conversation to load.

        Returns:
            The loaded conversation, or None if not found.
        """
        conversation = self.get_conversation(conversation_id)
        if conversation:
            self.current_conversation = conversation
            # Make sure it's in the cache
            self._add_to_cache(conversation)
        return conversation

    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete a conversation.

        Args:
            conversation_id: The ID of the conversation to delete.

        Returns:
            Whether the conversation was deleted.
        """
        # Check if the conversation file exists
        file_path = self.history_dir / f"{conversation_id}.json"
        if not file_path.exists():
            return False

        # Delete the file
        file_path.unlink()

        # Remove from cache if present
        if conversation_id in self.conversation_cache:
            del self.conversation_cache[conversation_id]

        # Clear the current conversation if it was deleted
        if self.current_conversation and self.current_conversation.id == conversation_id:
            self.current_conversation = None
            # Load the most recent conversation if available
            self._load_most_recent_conversation()

        return True
