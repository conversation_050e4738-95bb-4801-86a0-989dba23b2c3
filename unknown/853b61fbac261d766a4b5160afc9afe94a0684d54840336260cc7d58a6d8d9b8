"""
Gemini API integration for the Advanced AI Agent.
"""

import os
import random
import logging
from typing import Dict, List, Optional, Any, Generator, Union

import google.generativeai as genai
from PIL import Image

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from config import get_api_key, DEFAULT_GEMINI_API_KEYS

# Set up logging
logger = logging.getLogger(__name__)

class GeminiModel:
    """Gemini API integration for the Advanced AI Agent."""

    def __init__(
        self,
        model_name: str = "gemini-1.5-flash",
        temperature: float = 0.7,
        max_tokens: int = 4096,
        api_key: Optional[str] = None,
        max_context_window: int = 32000  # Approximate token limit for context window
    ):
        """Initialize the Gemini model.

        Args:
            model_name: The name of the Gemini model to use.
            temperature: The temperature to use for generation.
            max_tokens: The maximum number of tokens to generate.
            api_key: The API key to use. If None, will try to get from config.
            max_context_window: Maximum number of tokens to include in context window.
        """
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.max_context_window = max_context_window

        # Get API key
        if api_key is None:
            api_key = get_api_key("gemini")

        # If still no API key, try the default keys
        if not api_key:
            api_key = random.choice(DEFAULT_GEMINI_API_KEYS)

        # Configure the Gemini API
        genai.configure(api_key=api_key)

        # Initialize the model
        self.model = genai.GenerativeModel(
            model_name=model_name,
            generation_config={
                "temperature": temperature,
                "max_output_tokens": max_tokens,
                "top_p": 0.95,
                "top_k": 40,
            }
        )

        # Initialize the chat session
        self.chat = self.model.start_chat(history=[])

    def _format_conversation_history(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format conversation history for Gemini API.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys.

        Returns:
            Formatted conversation history for Gemini API.
        """
        formatted_history = []

        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")

            # Map roles to Gemini API roles
            if role == "assistant":
                gemini_role = "model"
            elif role == "system":
                gemini_role = "user"  # Gemini doesn't have a system role, so we use user
                content = f"[SYSTEM]: {content}"  # Mark as system message
            elif role in ["tool", "tool_result"]:
                # Skip tool messages for now - they're not part of the conversation flow
                continue
            else:
                gemini_role = "user"

            formatted_history.append({
                "role": gemini_role,
                "parts": [content]
            })

        return formatted_history

    def _estimate_token_count(self, text: str) -> int:
        """Estimate token count for a text string.

        This is a very rough estimate based on the assumption that
        1 token ≈ 4 characters for English text.

        Args:
            text: The text to estimate token count for.

        Returns:
            Estimated token count.
        """
        return len(text) // 4

    def _truncate_conversation_history(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Truncate conversation history to fit within context window.

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys.

        Returns:
            Truncated conversation history.
        """
        # If we have few messages, just return them all
        if len(messages) <= 4:
            return messages

        # Always keep the system message if present
        system_message = None
        regular_messages = []

        for message in messages:
            if message.get("role") == "system":
                system_message = message
            else:
                regular_messages.append(message)

        # Estimate total tokens
        total_tokens = sum(self._estimate_token_count(msg.get("content", "")) for msg in regular_messages)

        # If we're within the limit, return all messages
        if total_tokens <= self.max_context_window:
            if system_message:
                return [system_message] + regular_messages
            return regular_messages

        # We need to truncate
        # Always keep the most recent messages
        truncated_messages = []
        current_tokens = 0

        # Start from the most recent message and work backwards
        for message in reversed(regular_messages):
            message_tokens = self._estimate_token_count(message.get("content", ""))

            if current_tokens + message_tokens <= self.max_context_window:
                truncated_messages.insert(0, message)
                current_tokens += message_tokens
            else:
                # We've reached the limit
                break

        # Add system message back if we had one
        if system_message:
            return [system_message] + truncated_messages

        return truncated_messages

    def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Generate a response to a prompt.

        Args:
            prompt: The prompt to generate a response to.
            system_prompt: The system prompt to use.
            images: A list of images to include in the prompt.
            conversation_history: Previous conversation messages.

        Returns:
            The generated response.
        """
        # Prepare the content
        content = []

        # Process conversation history if provided
        if conversation_history:
            # Truncate history to fit in context window
            truncated_history = self._truncate_conversation_history(conversation_history)

            # Format history for Gemini API
            content = self._format_conversation_history(truncated_history)

            # Log how many messages were included
            logger.debug(f"Using {len(truncated_history)} messages from conversation history")
            if len(truncated_history) < len(conversation_history):
                logger.debug(f"Truncated {len(conversation_history) - len(truncated_history)} older messages")

        # Add system prompt if provided and not in history
        if system_prompt and not any(msg.get("role") == "system" for msg in (conversation_history or [])):
            content.append({
                "role": "user",
                "parts": [f"[SYSTEM]: {system_prompt}"]
            })

        # Add images if provided
        if images:
            parts = []
            # Add the prompt text
            parts.append(prompt)

            # Add the images
            for img in images:
                if isinstance(img, str):
                    # Load image from file
                    try:
                        img = Image.open(img)
                    except Exception as e:
                        logger.error(f"Error loading image {img}: {e}")
                        continue

                parts.append(img)

            content.append({
                "role": "user",
                "parts": parts
            })
        else:
            # Just text prompt
            content.append({
                "role": "user",
                "parts": [prompt]
            })

        try:
            # Generate the response
            response = self.model.generate_content(content)
            return response.text
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return f"Error generating response: {e}"

    def chat_generate(
        self,
        message: str,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Generate a response in a chat session.

        Args:
            message: The message to generate a response to.
            images: A list of images to include in the message.
            conversation_history: Previous conversation messages.

        Returns:
            The generated response.
        """
        # If we have conversation history, restart the chat with it
        if conversation_history:
            # Truncate history to fit in context window
            truncated_history = self._truncate_conversation_history(conversation_history)

            # Format history for Gemini API
            formatted_history = self._format_conversation_history(truncated_history)

            # Restart the chat with the history
            self.chat = self.model.start_chat(history=formatted_history)

            # Log how many messages were included
            logger.debug(f"Chat initialized with {len(truncated_history)} messages from conversation history")
            if len(truncated_history) < len(conversation_history):
                logger.debug(f"Truncated {len(conversation_history) - len(truncated_history)} older messages")

        # Prepare the content
        content = []

        # Add images if provided
        if images:
            parts = []
            # Add the message text
            parts.append(message)

            # Add the images
            for img in images:
                if isinstance(img, str):
                    # Load image from file
                    try:
                        img = Image.open(img)
                    except Exception as e:
                        logger.error(f"Error loading image {img}: {e}")
                        continue

                parts.append(img)

            content = parts
        else:
            # Just text message
            content = message

        try:
            # Generate the response
            response = self.chat.send_message(content)
            return response.text
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return f"Error generating response: {e}"

    def stream_generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> Generator[str, None, None]:
        """Stream a response to a prompt.

        Args:
            prompt: The prompt to generate a response to.
            system_prompt: The system prompt to use.
            images: A list of images to include in the prompt.
            conversation_history: Previous conversation messages.

        Yields:
            Chunks of the generated response.
        """
        # Prepare the content
        content = []

        # Process conversation history if provided
        if conversation_history:
            # Truncate history to fit in context window
            truncated_history = self._truncate_conversation_history(conversation_history)

            # Format history for Gemini API
            content = self._format_conversation_history(truncated_history)

            # Log how many messages were included
            logger.debug(f"Using {len(truncated_history)} messages from conversation history")
            if len(truncated_history) < len(conversation_history):
                logger.debug(f"Truncated {len(conversation_history) - len(truncated_history)} older messages")

        # Add system prompt if provided and not in history
        if system_prompt and not any(msg.get("role") == "system" for msg in (conversation_history or [])):
            content.append({
                "role": "user",
                "parts": [f"[SYSTEM]: {system_prompt}"]
            })

        # Add images if provided
        if images:
            parts = []
            # Add the prompt text
            parts.append(prompt)

            # Add the images
            for img in images:
                if isinstance(img, str):
                    # Load image from file
                    try:
                        img = Image.open(img)
                    except Exception as e:
                        logger.error(f"Error loading image {img}: {e}")
                        continue

                parts.append(img)

            content.append({
                "role": "user",
                "parts": parts
            })
        else:
            # Just text prompt
            content.append({
                "role": "user",
                "parts": [prompt]
            })

        try:
            # Generate the response
            response = self.model.generate_content(content, stream=True)

            for chunk in response:
                if chunk.text:
                    yield chunk.text
        except Exception as e:
            logger.error(f"Error streaming response: {e}")
            yield f"Error streaming response: {e}"

    def stream_chat_generate(
        self,
        message: str,
        images: Optional[List[Union[str, Image.Image]]] = None,
        conversation_history: Optional[List[Dict[str, Any]]] = None
    ) -> Generator[str, None, None]:
        """Stream a response in a chat session.

        Args:
            message: The message to generate a response to.
            images: A list of images to include in the message.
            conversation_history: Previous conversation messages.

        Yields:
            Chunks of the generated response.
        """
        # If we have conversation history, restart the chat with it
        if conversation_history:
            # Truncate history to fit in context window
            truncated_history = self._truncate_conversation_history(conversation_history)

            # Format history for Gemini API
            formatted_history = self._format_conversation_history(truncated_history)

            # Restart the chat with the history
            self.chat = self.model.start_chat(history=formatted_history)

            # Log how many messages were included
            logger.debug(f"Chat initialized with {len(truncated_history)} messages from conversation history")
            if len(truncated_history) < len(conversation_history):
                logger.debug(f"Truncated {len(conversation_history) - len(truncated_history)} older messages")

        # Prepare the content
        content = []

        # Add images if provided
        if images:
            parts = []
            # Add the message text
            parts.append(message)

            # Add the images
            for img in images:
                if isinstance(img, str):
                    # Load image from file
                    try:
                        img = Image.open(img)
                    except Exception as e:
                        logger.error(f"Error loading image {img}: {e}")
                        continue

                parts.append(img)

            content = parts
        else:
            # Just text message
            content = message

        try:
            # Generate the response
            response = self.chat.send_message(content, stream=True)

            for chunk in response:
                if chunk.text:
                    yield chunk.text
        except Exception as e:
            logger.error(f"Error streaming response: {e}")
            yield f"Error streaming response: {e}"
