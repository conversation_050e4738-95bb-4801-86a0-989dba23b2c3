"""
Intent recognizer for the Advanced AI Agent.
"""

import re
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Tuple, Set, Pattern

# Set up logging
logger = logging.getLogger(__name__)

class Intent:
    """Represents a recognized intent."""
    
    def __init__(self, name: str, confidence: float, parameters: Dict[str, Any] = None):
        """Initialize an intent.
        
        Args:
            name: Name of the intent.
            confidence: Confidence score (0.0 to 1.0).
            parameters: Parameters extracted from the query.
        """
        self.name = name
        self.confidence = confidence
        self.parameters = parameters or {}
    
    def __str__(self) -> str:
        """String representation of the intent."""
        params_str = ", ".join(f"{k}={v}" for k, v in self.parameters.items())
        return f"Intent(name='{self.name}', confidence={self.confidence:.2f}, parameters={{{params_str}}})"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary.
        
        Returns:
            Dictionary representation of the intent.
        """
        return {
            "name": self.name,
            "confidence": self.confidence,
            "parameters": self.parameters
        }

class IntentRecognizer:
    """Recognizer for natural language intents."""
    
    def __init__(self, model_manager: Any):
        """Initialize the intent recognizer.
        
        Args:
            model_manager: Model manager for AI-powered intent recognition.
        """
        self.model_manager = model_manager
        self.lock = threading.RLock()
        
        # Define intent patterns
        self.patterns: Dict[str, Dict[str, Union[Pattern, List[str]]]] = {
            "code_generation": {
                "patterns": [
                    r"(?:create|generate|write|implement|code)\s+(?:a|an)?\s*(.+)",
                    r"(?:make|build)\s+(?:a|an)?\s*(.+)",
                    r"(?:how\s+to|how\s+do\s+I)\s+(?:create|generate|write|implement|code)\s+(?:a|an)?\s*(.+)",
                ],
                "keywords": ["create", "generate", "write", "implement", "code", "make", "build"],
                "parameter_names": ["code_description"]
            },
            "code_explanation": {
                "patterns": [
                    r"(?:explain|understand|describe|what\s+does)\s+(?:this|the)?\s*(?:code|function|class|method|snippet)",
                    r"(?:how\s+does|what\s+is)\s+(?:this|the)?\s*(?:code|function|class|method|snippet)\s+(?:do|work)",
                ],
                "keywords": ["explain", "understand", "describe", "what does", "how does"],
                "parameter_names": []
            },
            "code_refactoring": {
                "patterns": [
                    r"(?:refactor|improve|optimize|clean\s+up)\s+(?:this|the)?\s*(?:code|function|class|method|snippet)",
                    r"(?:make|can\s+you\s+make)\s+(?:this|the)?\s*(?:code|function|class|method|snippet)\s+(?:better|cleaner|more\s+efficient)",
                ],
                "keywords": ["refactor", "improve", "optimize", "clean up", "better", "cleaner", "more efficient"],
                "parameter_names": []
            },
            "bug_fixing": {
                "patterns": [
                    r"(?:fix|debug|solve|resolve)\s+(?:this|the)?\s*(?:bug|issue|problem|error)",
                    r"(?:what's|what\s+is)\s+(?:wrong|the\s+problem)\s+(?:with|in)\s+(?:this|the)?\s*(?:code|function|class|method|snippet)",
                ],
                "keywords": ["fix", "debug", "solve", "resolve", "bug", "issue", "problem", "error"],
                "parameter_names": []
            },
            "code_completion": {
                "patterns": [
                    r"(?:complete|finish)\s+(?:this|the)?\s*(?:code|function|class|method|snippet)",
                    r"(?:what|how)\s+(?:should|would|could)\s+(?:I|we)\s+(?:add|write|put)\s+(?:next|here)",
                ],
                "keywords": ["complete", "finish", "next", "continue"],
                "parameter_names": []
            },
            "language_translation": {
                "patterns": [
                    r"(?:translate|convert|rewrite)\s+(?:this|the)?\s*(?:code|function|class|method|snippet)\s+(?:from|to)\s+(\w+)(?:\s+(?:to|into)\s+(\w+))?",
                    r"(?:how\s+would|can\s+you)\s+(?:write|implement)\s+(?:this|the)?\s*(?:in|using)\s+(\w+)",
                ],
                "keywords": ["translate", "convert", "rewrite", "from", "to", "into"],
                "parameter_names": ["source_language", "target_language"]
            },
            "test_generation": {
                "patterns": [
                    r"(?:create|generate|write|implement)\s+(?:a|some)?\s*(?:test|tests|unit\s+tests|test\s+cases)\s+(?:for|of)\s+(?:this|the)?\s*(?:code|function|class|method|snippet)",
                    r"(?:how\s+to|how\s+do\s+I|can\s+you)\s+(?:test|verify)\s+(?:this|the)?\s*(?:code|function|class|method|snippet)",
                ],
                "keywords": ["test", "unit test", "test case", "verify"],
                "parameter_names": []
            },
            "documentation_generation": {
                "patterns": [
                    r"(?:create|generate|write|add)\s+(?:documentation|docs|comments)\s+(?:for|to)\s+(?:this|the)?\s*(?:code|function|class|method|snippet)",
                    r"(?:document|comment)\s+(?:this|the)?\s*(?:code|function|class|method|snippet)",
                ],
                "keywords": ["documentation", "docs", "comments", "document", "comment"],
                "parameter_names": []
            },
        }
        
        # Compile patterns
        for intent_name, intent_data in self.patterns.items():
            compiled_patterns = []
            for pattern in intent_data["patterns"]:
                compiled_patterns.append(re.compile(pattern, re.IGNORECASE))
            intent_data["compiled_patterns"] = compiled_patterns
    
    def recognize_intent(self, query: str) -> List[Intent]:
        """Recognize intents in a natural language query.
        
        Args:
            query: Natural language query.
            
        Returns:
            List of recognized intents, sorted by confidence.
        """
        intents = []
        
        # Check pattern-based intents
        for intent_name, intent_data in self.patterns.items():
            confidence = 0.0
            parameters = {}
            
            # Check compiled patterns
            for pattern in intent_data["compiled_patterns"]:
                match = pattern.search(query)
                if match:
                    confidence = 0.8  # High confidence for pattern match
                    
                    # Extract parameters
                    param_names = intent_data.get("parameter_names", [])
                    for i, param_name in enumerate(param_names):
                        if i + 1 < len(match.groups()) + 1:
                            parameters[param_name] = match.group(i + 1)
                    
                    break
            
            # Check keywords if no pattern match
            if confidence == 0.0:
                keywords = intent_data.get("keywords", [])
                matched_keywords = sum(1 for keyword in keywords if keyword.lower() in query.lower())
                if matched_keywords > 0:
                    confidence = 0.5 * (matched_keywords / len(keywords))
            
            if confidence > 0.0:
                intents.append(Intent(intent_name, confidence, parameters))
        
        # If no intents recognized or low confidence, use the model
        if not intents or all(intent.confidence < 0.6 for intent in intents):
            model_intents = self._recognize_with_model(query)
            
            # Merge model intents with pattern intents
            for model_intent in model_intents:
                # Check if intent already exists
                existing = next((i for i in intents if i.name == model_intent.name), None)
                if existing:
                    # Use higher confidence and merge parameters
                    existing.confidence = max(existing.confidence, model_intent.confidence)
                    existing.parameters.update(model_intent.parameters)
                else:
                    intents.append(model_intent)
        
        # Sort by confidence
        intents.sort(key=lambda x: x.confidence, reverse=True)
        
        return intents
    
    def _recognize_with_model(self, query: str) -> List[Intent]:
        """Use the model to recognize intents.
        
        Args:
            query: Natural language query.
            
        Returns:
            List of recognized intents.
        """
        try:
            # Create a prompt for the model
            prompt = f"""
You are an AI assistant that recognizes user intents from natural language queries.
Analyze the following query and identify the most likely intent from these options:
- code_generation: User wants to generate or create code
- code_explanation: User wants an explanation of code
- code_refactoring: User wants to refactor or improve code
- bug_fixing: User wants to fix a bug or error
- code_completion: User wants to complete or continue code
- language_translation: User wants to translate code between languages
- test_generation: User wants to generate tests
- documentation_generation: User wants to generate documentation

Query: "{query}"

Respond in JSON format with the intent name, confidence score (0.0-1.0), and any parameters:
"""
            
            # Generate response
            response = self.model_manager.generate(prompt=prompt)
            
            # Parse the response
            import json
            try:
                # Extract JSON from response
                json_str = response.strip()
                if "```json" in json_str:
                    json_str = json_str.split("```json")[1].split("```")[0].strip()
                elif "```" in json_str:
                    json_str = json_str.split("```")[1].split("```")[0].strip()
                
                data = json.loads(json_str)
                
                # Create intent
                if isinstance(data, dict) and "intent" in data:
                    return [Intent(
                        name=data["intent"],
                        confidence=data.get("confidence", 0.7),
                        parameters=data.get("parameters", {})
                    )]
                elif isinstance(data, list):
                    return [Intent(
                        name=item["intent"],
                        confidence=item.get("confidence", 0.7),
                        parameters=item.get("parameters", {})
                    ) for item in data if "intent" in item]
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse JSON from model response: {response}")
            
            # Fallback: simple parsing
            for intent_name in self.patterns.keys():
                if intent_name.lower() in response.lower():
                    return [Intent(intent_name, 0.6)]
            
            return []
        except Exception as e:
            logger.error(f"Error recognizing intent with model: {e}", exc_info=True)
            return []
