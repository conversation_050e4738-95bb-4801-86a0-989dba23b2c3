"""
Natural language to code converter for the Advanced AI Agent.
"""

import re
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Tuple, Set

# Set up logging
logger = logging.getLogger(__name__)

class NLToCode:
    """Converter for natural language to code."""
    
    def __init__(self, model_manager: Any):
        """Initialize the natural language to code converter.
        
        Args:
            model_manager: Model manager for AI-powered code generation.
        """
        self.model_manager = model_manager
        self.lock = threading.RLock()
        
        # Supported languages
        self.supported_languages = [
            "python", "javascript", "typescript", "java", "c", "cpp", "csharp", 
            "go", "rust", "php", "ruby", "swift", "kotlin", "sql", "html", "css"
        ]
        
        # Language detection patterns
        self.language_patterns = {
            "python": [
                r"python", r"django", r"flask", r"numpy", r"pandas", r"tensorflow", 
                r"pytorch", r"scikit-learn", r"matplotlib", r"def\s+\w+\s*\(", r"import\s+\w+"
            ],
            "javascript": [
                r"javascript", r"js", r"node", r"express", r"react", r"vue", r"angular", 
                r"function\s+\w+\s*\(", r"const\s+\w+\s*=", r"let\s+\w+\s*=", r"var\s+\w+\s*="
            ],
            "typescript": [
                r"typescript", r"ts", r"interface\s+\w+", r"type\s+\w+\s*=", 
                r"class\s+\w+\s*implements", r":\s*\w+\[\]"
            ],
            "java": [
                r"java", r"spring", r"public\s+class", r"private\s+\w+\s+\w+", 
                r"public\s+static\s+void\s+main"
            ],
            "c": [
                r"\bc\b", r"stdio", r"stdlib", r"#include\s+<\w+\.h>", r"void\s+\w+\s*\("
            ],
            "cpp": [
                r"c\+\+", r"cpp", r"vector<", r"iostream", r"namespace\s+\w+"
            ],
            "csharp": [
                r"c#", r"csharp", r"\.net", r"using\s+System", r"namespace\s+\w+"
            ],
            "go": [
                r"golang", r"go\b", r"func\s+\w+\s*\(", r"package\s+main", r"import\s+\("
            ],
            "rust": [
                r"rust", r"fn\s+\w+\s*\(", r"let\s+mut\s+\w+", r"struct\s+\w+"
            ],
            "php": [
                r"php", r"<\?php", r"\$\w+\s*=", r"function\s+\w+\s*\("
            ],
            "ruby": [
                r"ruby", r"rails", r"def\s+\w+", r"end\b", r"require\s+['\"]"
            ],
            "swift": [
                r"swift", r"ios", r"func\s+\w+\s*\(", r"var\s+\w+\s*:", r"let\s+\w+\s*:"
            ],
            "kotlin": [
                r"kotlin", r"android", r"fun\s+\w+\s*\(", r"val\s+\w+\s*:", r"var\s+\w+\s*:"
            ],
            "sql": [
                r"sql", r"mysql", r"postgresql", r"sqlite", r"select\s+", r"from\s+", 
                r"where\s+", r"insert\s+into", r"create\s+table"
            ],
            "html": [
                r"html", r"<html", r"<div", r"<p>", r"<body", r"<head", r"<script"
            ],
            "css": [
                r"css", r"style", r"\.\w+\s*{", r"#\w+\s*{", r"@media", r"margin", r"padding"
            ]
        }
        
        # Compile patterns
        self.compiled_patterns = {}
        for language, patterns in self.language_patterns.items():
            self.compiled_patterns[language] = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
    
    def convert_to_code(self, 
                       nl_description: str, 
                       language: Optional[str] = None,
                       context: Optional[str] = None) -> str:
        """Convert natural language to code.
        
        Args:
            nl_description: Natural language description of the code.
            language: Target programming language. If None, will be detected.
            context: Optional code context.
            
        Returns:
            Generated code.
        """
        # Detect language if not provided
        if language is None:
            language = self._detect_language(nl_description, context)
        
        # Ensure language is supported
        if language not in self.supported_languages:
            language = "python"  # Default to Python
        
        try:
            # Create a prompt for the model
            prompt = f"""
You are an expert {language} programmer. Convert the following natural language description into {language} code.
Only provide the code, no explanations or comments unless they are part of the code.

Description: {nl_description}
"""
            
            # Add context if provided
            if context:
                prompt += f"""
Context:
```{language}
{context}
```
"""
            
            prompt += f"""
{language} code:
```{language}
"""
            
            # Generate code
            response = self.model_manager.generate(prompt=prompt)
            
            # Extract code from response
            code = self._extract_code(response, language)
            
            return code
        except Exception as e:
            logger.error(f"Error converting to code: {e}", exc_info=True)
            return f"# Error generating code: {e}"
    
    def _detect_language(self, nl_description: str, context: Optional[str] = None) -> str:
        """Detect the target programming language from the description and context.
        
        Args:
            nl_description: Natural language description.
            context: Optional code context.
            
        Returns:
            Detected programming language.
        """
        text = nl_description.lower()
        if context:
            text += " " + context.lower()
        
        # Count matches for each language
        scores = {}
        for language, patterns in self.compiled_patterns.items():
            score = 0
            for pattern in patterns:
                matches = pattern.findall(text)
                score += len(matches)
            scores[language] = score
        
        # Find language with highest score
        if scores:
            max_score = max(scores.values())
            if max_score > 0:
                # Get all languages with the max score
                candidates = [lang for lang, score in scores.items() if score == max_score]
                return candidates[0]  # Return the first candidate
        
        # Default to Python if no language detected
        return "python"
    
    def _extract_code(self, response: str, language: str) -> str:
        """Extract code from the model response.
        
        Args:
            response: Model response.
            language: Programming language.
            
        Returns:
            Extracted code.
        """
        # Look for code blocks
        code_block_pattern = f"```(?:{language})?(.*?)```"
        matches = re.findall(code_block_pattern, response, re.DOTALL)
        
        if matches:
            # Return the first code block
            return matches[0].strip()
        
        # If no code blocks found, return the whole response
        return response.strip()
    
    def generate_function(self, 
                         function_description: str, 
                         function_name: Optional[str] = None,
                         parameters: Optional[List[str]] = None,
                         return_type: Optional[str] = None,
                         language: Optional[str] = None) -> str:
        """Generate a function from a description.
        
        Args:
            function_description: Description of what the function should do.
            function_name: Optional function name.
            parameters: Optional list of parameter names.
            return_type: Optional return type.
            language: Target programming language. If None, will be detected.
            
        Returns:
            Generated function code.
        """
        # Detect language if not provided
        if language is None:
            language = self._detect_language(function_description, None)
        
        # Ensure language is supported
        if language not in self.supported_languages:
            language = "python"  # Default to Python
        
        try:
            # Create a prompt for the model
            prompt = f"""
You are an expert {language} programmer. Generate a {language} function based on the following description.
Only provide the function code, no explanations or additional comments.

Function description: {function_description}
"""
            
            # Add function name if provided
            if function_name:
                prompt += f"Function name: {function_name}\n"
            
            # Add parameters if provided
            if parameters:
                prompt += f"Parameters: {', '.join(parameters)}\n"
            
            # Add return type if provided
            if return_type:
                prompt += f"Return type: {return_type}\n"
            
            prompt += f"""
{language} function:
```{language}
"""
            
            # Generate function
            response = self.model_manager.generate(prompt=prompt)
            
            # Extract code from response
            code = self._extract_code(response, language)
            
            return code
        except Exception as e:
            logger.error(f"Error generating function: {e}", exc_info=True)
            return f"# Error generating function: {e}"
    
    def generate_class(self, 
                      class_description: str, 
                      class_name: Optional[str] = None,
                      methods: Optional[List[str]] = None,
                      properties: Optional[List[str]] = None,
                      language: Optional[str] = None) -> str:
        """Generate a class from a description.
        
        Args:
            class_description: Description of what the class should do.
            class_name: Optional class name.
            methods: Optional list of method descriptions.
            properties: Optional list of property names.
            language: Target programming language. If None, will be detected.
            
        Returns:
            Generated class code.
        """
        # Detect language if not provided
        if language is None:
            language = self._detect_language(class_description, None)
        
        # Ensure language is supported
        if language not in self.supported_languages:
            language = "python"  # Default to Python
        
        try:
            # Create a prompt for the model
            prompt = f"""
You are an expert {language} programmer. Generate a {language} class based on the following description.
Only provide the class code, no explanations or additional comments.

Class description: {class_description}
"""
            
            # Add class name if provided
            if class_name:
                prompt += f"Class name: {class_name}\n"
            
            # Add methods if provided
            if methods:
                prompt += f"Methods: {'; '.join(methods)}\n"
            
            # Add properties if provided
            if properties:
                prompt += f"Properties: {', '.join(properties)}\n"
            
            prompt += f"""
{language} class:
```{language}
"""
            
            # Generate class
            response = self.model_manager.generate(prompt=prompt)
            
            # Extract code from response
            code = self._extract_code(response, language)
            
            return code
        except Exception as e:
            logger.error(f"Error generating class: {e}", exc_info=True)
            return f"# Error generating class: {e}"
