"""
Thread manager for the Advanced AI Agent.
"""

import time
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from typing import Dict, List, Optional, Any, Union, Tuple, Callable, Set

# Set up logging
logger = logging.getLogger(__name__)

class ThreadManager:
    """Enhanced thread manager for handling parallel task execution with optimization."""

    def __init__(self, max_workers: int = 4, enable_monitoring: bool = True):
        """Initialize the thread manager.

        Args:
            max_workers: Maximum number of worker threads.
            enable_monitoring: Whether to enable performance monitoring.
        """
        self.max_workers = max_workers
        self.enable_monitoring = enable_monitoring
        self.executors: Dict[str, ThreadPoolExecutor] = {}
        self.futures: Dict[str, Dict[str, Future]] = {}
        self.results: Dict[str, Dict[str, Any]] = {}
        self.lock = threading.RLock()
        self.active_tasks: Set[str] = set()

        # Performance monitoring
        self.task_metrics: Dict[str, Dict[str, Any]] = {}
        self.executor_stats: Dict[str, Dict[str, Any]] = {}

        # Task queue for load balancing
        self.task_queue: Dict[str, List[Tuple[str, Callable, tuple, dict]]] = {}

        # Adaptive worker management
        self.worker_utilization: Dict[str, float] = {}
        self.last_optimization_time = time.time()
        self.optimization_interval = 60.0  # Optimize every minute

    def create_executor(self, name: str, workers: Optional[int] = None) -> ThreadPoolExecutor:
        """Create a new thread pool executor.

        Args:
            name: Name of the executor.
            workers: Number of worker threads. If None, uses max_workers.

        Returns:
            The created executor.
        """
        with self.lock:
            if name in self.executors:
                return self.executors[name]

            executor = ThreadPoolExecutor(max_workers=workers or self.max_workers,
                                         thread_name_prefix=f"{name}_worker")
            self.executors[name] = executor
            self.futures[name] = {}
            self.results[name] = {}
            self.task_queue[name] = []
            self.executor_stats[name] = {
                "tasks_completed": 0,
                "tasks_failed": 0,
                "total_execution_time": 0.0,
                "average_execution_time": 0.0,
                "worker_count": workers or self.max_workers
            }
            self.worker_utilization[name] = 0.0
            logger.debug(f"Created executor '{name}' with {workers or self.max_workers} workers")
            return executor

    def submit_task(self,
                   executor_name: str,
                   task_id: str,
                   func: Callable,
                   *args,
                   **kwargs) -> Future:
        """Submit a task to an executor.

        Args:
            executor_name: Name of the executor to use.
            task_id: Unique identifier for the task.
            func: Function to execute.
            *args: Arguments to pass to the function.
            **kwargs: Keyword arguments to pass to the function.

        Returns:
            Future object representing the task.
        """
        with self.lock:
            # Create executor if it doesn't exist
            if executor_name not in self.executors:
                self.create_executor(executor_name)

            # Submit the task with enhanced wrapper
            future = self.executors[executor_name].submit(
                self._task_wrapper, func, task_id, executor_name, *args, **kwargs
            )
            self.futures[executor_name][task_id] = future
            self.active_tasks.add(task_id)
            logger.debug(f"Submitted task '{task_id}' to executor '{executor_name}'")
            return future

    def _task_wrapper(self, func: Callable, task_id: str, executor_name: str, *args, **kwargs) -> Any:
        """Enhanced wrapper for tasks with performance monitoring.

        Args:
            func: Function to execute.
            task_id: Task identifier.
            executor_name: Name of the executor.
            *args: Arguments to pass to the function.
            **kwargs: Keyword arguments to pass to the function.

        Returns:
            Result of the function.
        """
        start_time = time.time()

        try:
            # Execute the function
            result = func(*args, **kwargs)

            # Record success metrics
            execution_time = time.time() - start_time
            if self.enable_monitoring:
                self._record_task_success(task_id, executor_name, execution_time)

            # Store result
            with self.lock:
                self.results[executor_name][task_id] = result
                self.active_tasks.discard(task_id)

            logger.debug(f"Task '{task_id}' completed successfully in {execution_time:.3f}s")
            return result

        except Exception as e:
            # Record failure metrics
            execution_time = time.time() - start_time
            if self.enable_monitoring:
                self._record_task_failure(task_id, executor_name, execution_time, e)

            # Store error
            with self.lock:
                self.results[executor_name][task_id] = {"error": str(e), "exception": e}
                self.active_tasks.discard(task_id)

            logger.error(f"Task '{task_id}' failed after {execution_time:.3f}s: {e}")
            raise

    def _record_task_success(self, task_id: str, executor_name: str, execution_time: float):
        """Record successful task execution metrics."""
        with self.lock:
            if executor_name in self.executor_stats:
                stats = self.executor_stats[executor_name]
                stats["tasks_completed"] += 1
                stats["total_execution_time"] += execution_time
                stats["average_execution_time"] = (
                    stats["total_execution_time"] / stats["tasks_completed"]
                )

            # Record individual task metrics
            self.task_metrics[task_id] = {
                "executor": executor_name,
                "execution_time": execution_time,
                "status": "success",
                "timestamp": time.time()
            }

    def _record_task_failure(self, task_id: str, executor_name: str, execution_time: float, error: Exception):
        """Record failed task execution metrics."""
        with self.lock:
            if executor_name in self.executor_stats:
                stats = self.executor_stats[executor_name]
                stats["tasks_failed"] += 1

            # Record individual task metrics
            self.task_metrics[task_id] = {
                "executor": executor_name,
                "execution_time": execution_time,
                "status": "failed",
                "error": str(error),
                "timestamp": time.time()
            }

    def optimize_executors(self):
        """Optimize executor configurations based on performance metrics."""
        current_time = time.time()

        if current_time - self.last_optimization_time < self.optimization_interval:
            return

        with self.lock:
            for executor_name, stats in self.executor_stats.items():
                if stats["tasks_completed"] < 10:  # Need sufficient data
                    continue

                # Calculate utilization
                total_tasks = stats["tasks_completed"] + stats["tasks_failed"]
                success_rate = stats["tasks_completed"] / total_tasks
                avg_time = stats["average_execution_time"]

                # Determine if we need more or fewer workers
                current_workers = stats["worker_count"]

                if success_rate > 0.95 and avg_time < 1.0:
                    # High success rate and fast execution - can reduce workers
                    new_workers = max(1, current_workers - 1)
                elif success_rate < 0.8 or avg_time > 5.0:
                    # Low success rate or slow execution - increase workers
                    new_workers = min(self.max_workers * 2, current_workers + 1)
                else:
                    new_workers = current_workers

                if new_workers != current_workers:
                    self._resize_executor(executor_name, new_workers)

            self.last_optimization_time = current_time

    def _resize_executor(self, executor_name: str, new_worker_count: int):
        """Resize an executor's worker pool."""
        if executor_name not in self.executors:
            return

        # Create new executor with different worker count
        old_executor = self.executors[executor_name]
        new_executor = ThreadPoolExecutor(
            max_workers=new_worker_count,
            thread_name_prefix=f"{executor_name}_worker"
        )

        # Update references
        self.executors[executor_name] = new_executor
        self.executor_stats[executor_name]["worker_count"] = new_worker_count

        # Shutdown old executor gracefully
        old_executor.shutdown(wait=False)

        logger.info(f"Resized executor '{executor_name}' to {new_worker_count} workers")

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        with self.lock:
            return {
                "executor_stats": dict(self.executor_stats),
                "active_tasks": len(self.active_tasks),
                "total_executors": len(self.executors),
                "worker_utilization": dict(self.worker_utilization),
                "recent_tasks": len([
                    task for task in self.task_metrics.values()
                    if time.time() - task["timestamp"] < 300  # Last 5 minutes
                ])
            }

    def get_task_metrics(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get metrics for a specific task."""
        with self.lock:
            return self.task_metrics.get(task_id)

    def get_result(self, executor_name: str, task_id: str, timeout: Optional[float] = None) -> Any:
        """Get the result of a task.

        Args:
            executor_name: Name of the executor.
            task_id: Task identifier.
            timeout: Maximum time to wait for the result.

        Returns:
            Result of the task.

        Raises:
            KeyError: If the executor or task doesn't exist.
            TimeoutError: If the task doesn't complete within the timeout.
        """
        with self.lock:
            if executor_name not in self.futures:
                raise KeyError(f"Executor '{executor_name}' not found")

            if task_id not in self.futures[executor_name]:
                raise KeyError(f"Task '{task_id}' not found in executor '{executor_name}'")

            future = self.futures[executor_name][task_id]

        # Wait for the result
        return future.result(timeout=timeout)

    def cancel_task(self, executor_name: str, task_id: str) -> bool:
        """Cancel a task.

        Args:
            executor_name: Name of the executor.
            task_id: Task identifier.

        Returns:
            Whether the task was successfully cancelled.
        """
        with self.lock:
            if executor_name not in self.futures:
                return False

            if task_id not in self.futures[executor_name]:
                return False

            future = self.futures[executor_name][task_id]
            cancelled = future.cancel()

            if cancelled and task_id in self.active_tasks:
                self.active_tasks.remove(task_id)

            return cancelled

    def shutdown(self, executor_name: Optional[str] = None, wait: bool = True):
        """Shutdown an executor or all executors.

        Args:
            executor_name: Name of the executor to shutdown. If None, shuts down all executors.
            wait: Whether to wait for pending tasks to complete.
        """
        with self.lock:
            if executor_name is not None:
                if executor_name in self.executors:
                    logger.debug(f"Shutting down executor '{executor_name}'")
                    self.executors[executor_name].shutdown(wait=wait)
                    del self.executors[executor_name]
                    del self.futures[executor_name]
                    del self.results[executor_name]
            else:
                logger.debug("Shutting down all executors")
                for name, executor in list(self.executors.items()):
                    executor.shutdown(wait=wait)
                self.executors.clear()
                self.futures.clear()
                self.results.clear()
                self.active_tasks.clear()

    def get_active_tasks(self) -> Set[str]:
        """Get the set of active task IDs.

        Returns:
            Set of active task IDs.
        """
        with self.lock:
            return set(self.active_tasks)

    def get_task_count(self) -> int:
        """Get the total number of active tasks.

        Returns:
            Number of active tasks.
        """
        with self.lock:
            return len(self.active_tasks)
