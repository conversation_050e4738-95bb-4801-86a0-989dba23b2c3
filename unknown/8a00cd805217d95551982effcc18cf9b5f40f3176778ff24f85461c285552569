"""
Patch tool for the Advanced AI Agent.
"""

import os
import re
import difflib
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple

class PatchTool:
    """Patch tool for file patching."""
    
    def __init__(self, workspace_dir: Optional[Path] = None):
        """Initialize the patch tool.
        
        Args:
            workspace_dir: The workspace directory to use. If None, will use the current directory.
        """
        self.workspace_dir = workspace_dir or Path.cwd()
        self.history: List[Dict[str, str]] = []
    
    def apply_patch(self, file_path: Union[str, Path], original: str, updated: str) -> Tuple[bool, str]:
        """Apply a patch to a file.
        
        Args:
            file_path: The path to the file to patch.
            original: The original content to replace.
            updated: The updated content to replace with.
            
        Returns:
            A tuple of (success, message).
        """
        # Add to history
        self.history.append({
            "action": "apply_patch",
            "file_path": str(file_path),
            "original": original,
            "updated": updated
        })
        
        try:
            # Resolve the path
            file_path = self._resolve_path(file_path)
            
            # Check if the file exists
            if not file_path.exists():
                error_message = f"File not found: {file_path}"
                self.history[-1]["error"] = error_message
                return False, error_message
            
            # Read the file content
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Check if the original content exists in the file
            if original not in content:
                error_message = f"Original content not found in file: {file_path}"
                self.history[-1]["error"] = error_message
                return False, error_message
            
            # Apply the patch
            new_content = content.replace(original, updated)
            
            # Write the updated content
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(new_content)
            
            return True, f"Patch applied to file: {file_path}"
        
        except Exception as e:
            error_message = f"Error applying patch: {e}"
            self.history[-1]["error"] = error_message
            return False, error_message
    
    def apply_git_style_patch(self, file_path: Union[str, Path], patch_content: str) -> Tuple[bool, str]:
        """Apply a git-style patch to a file.
        
        Args:
            file_path: The path to the file to patch.
            patch_content: The patch content in git-style format.
            
        Returns:
            A tuple of (success, message).
        """
        # Add to history
        self.history.append({
            "action": "apply_git_style_patch",
            "file_path": str(file_path),
            "patch_content": patch_content
        })
        
        try:
            # Resolve the path
            file_path = self._resolve_path(file_path)
            
            # Check if the file exists
            if not file_path.exists():
                error_message = f"File not found: {file_path}"
                self.history[-1]["error"] = error_message
                return False, error_message
            
            # Read the file content
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Parse the patch
            patch_blocks = self._parse_git_style_patch(patch_content)
            
            # Apply each patch block
            for original, updated in patch_blocks:
                if original not in content:
                    error_message = f"Original content not found in file: {file_path}"
                    self.history[-1]["error"] = error_message
                    return False, error_message
                
                content = content.replace(original, updated)
            
            # Write the updated content
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            
            return True, f"Git-style patch applied to file: {file_path}"
        
        except Exception as e:
            error_message = f"Error applying git-style patch: {e}"
            self.history[-1]["error"] = error_message
            return False, error_message
    
    def _parse_git_style_patch(self, patch_content: str) -> List[Tuple[str, str]]:
        """Parse a git-style patch.
        
        Args:
            patch_content: The patch content in git-style format.
            
        Returns:
            A list of (original, updated) tuples.
        """
        # Define the pattern for git-style patches
        pattern = r"<<<<<<< ORIGINAL\n(.*?)=======\n(.*?)>>>>>>> UPDATED"
        
        # Find all matches
        matches = re.finditer(pattern, patch_content, re.DOTALL)
        
        # Extract the original and updated content
        patch_blocks = []
        for match in matches:
            original = match.group(1)
            updated = match.group(2)
            patch_blocks.append((original, updated))
        
        return patch_blocks
    
    def generate_diff(self, original: str, updated: str) -> str:
        """Generate a diff between two strings.
        
        Args:
            original: The original string.
            updated: The updated string.
            
        Returns:
            The diff as a string.
        """
        # Add to history
        self.history.append({
            "action": "generate_diff",
            "original": original,
            "updated": updated
        })
        
        # Generate the diff
        diff = difflib.unified_diff(
            original.splitlines(keepends=True),
            updated.splitlines(keepends=True),
            fromfile="original",
            tofile="updated"
        )
        
        return "".join(diff)
    
    def generate_git_style_patch(self, original: str, updated: str) -> str:
        """Generate a git-style patch between two strings.
        
        Args:
            original: The original string.
            updated: The updated string.
            
        Returns:
            The git-style patch as a string.
        """
        # Add to history
        self.history.append({
            "action": "generate_git_style_patch",
            "original": original,
            "updated": updated
        })
        
        # Generate the git-style patch
        return f"<<<<<<< ORIGINAL\n{original}=======\n{updated}>>>>>>> UPDATED"
    
    def get_history(self) -> List[Dict[str, str]]:
        """Get the patch history.
        
        Returns:
            The patch history.
        """
        return self.history
    
    def _resolve_path(self, path: Union[str, Path]) -> Path:
        """Resolve a path relative to the workspace directory.
        
        Args:
            path: The path to resolve.
            
        Returns:
            The resolved path.
        """
        if isinstance(path, str):
            path = Path(path)
        
        if path.is_absolute():
            return path
        
        return self.workspace_dir / path
