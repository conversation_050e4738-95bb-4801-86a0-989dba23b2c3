"""
Advanced Cache System for the Advanced AI Agent.
Provides multi-level caching with intelligent eviction and performance optimization.
"""

import time
import threading
import hashlib
import pickle
import json
import logging
import asyncio
from typing import Dict, List, Optional, Any, Callable, Tuple, Union
from dataclasses import dataclass, asdict
from collections import OrderedDict, defaultdict
from pathlib import Path
import sqlite3
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Cache entry data structure."""
    key: str
    value: Any
    timestamp: float
    access_count: int
    last_access: float
    ttl: Optional[float]
    size: int
    priority: int = 1

@dataclass
class CacheStats:
    """Cache statistics data structure."""
    hits: int
    misses: int
    evictions: int
    total_requests: int
    hit_rate: float
    memory_usage: int
    entry_count: int

class CacheEvictionPolicy(ABC):
    """Abstract base class for cache eviction policies."""

    @abstractmethod
    def should_evict(self, entry: CacheEntry, cache_size: int, max_size: int) -> bool:
        """Determine if an entry should be evicted."""
        pass

    @abstractmethod
    def select_victim(self, entries: Dict[str, CacheEntry]) -> Optional[str]:
        """Select an entry to evict."""
        pass

class LRUEvictionPolicy(CacheEvictionPolicy):
    """Least Recently Used eviction policy."""

    def should_evict(self, entry: CacheEntry, cache_size: int, max_size: int) -> bool:
        return cache_size >= max_size

    def select_victim(self, entries: Dict[str, CacheEntry]) -> Optional[str]:
        if not entries:
            return None
        return min(entries.keys(), key=lambda k: entries[k].last_access)

class LFUEvictionPolicy(CacheEvictionPolicy):
    """Least Frequently Used eviction policy."""

    def should_evict(self, entry: CacheEntry, cache_size: int, max_size: int) -> bool:
        return cache_size >= max_size

    def select_victim(self, entries: Dict[str, CacheEntry]) -> Optional[str]:
        if not entries:
            return None
        return min(entries.keys(), key=lambda k: entries[k].access_count)

class TTLEvictionPolicy(CacheEvictionPolicy):
    """Time-To-Live eviction policy."""

    def should_evict(self, entry: CacheEntry, cache_size: int, max_size: int) -> bool:
        if entry.ttl is None:
            return False
        return time.time() - entry.timestamp > entry.ttl

    def select_victim(self, entries: Dict[str, CacheEntry]) -> Optional[str]:
        current_time = time.time()
        expired_entries = [
            key for key, entry in entries.items()
            if entry.ttl and current_time - entry.timestamp > entry.ttl
        ]
        return expired_entries[0] if expired_entries else None

class AdaptiveEvictionPolicy(CacheEvictionPolicy):
    """Adaptive eviction policy that combines multiple strategies."""

    def __init__(self):
        self.lru_policy = LRUEvictionPolicy()
        self.lfu_policy = LFUEvictionPolicy()
        self.ttl_policy = TTLEvictionPolicy()

    def should_evict(self, entry: CacheEntry, cache_size: int, max_size: int) -> bool:
        # Check TTL first
        if self.ttl_policy.should_evict(entry, cache_size, max_size):
            return True
        # Then check size limits
        return cache_size >= max_size

    def select_victim(self, entries: Dict[str, CacheEntry]) -> Optional[str]:
        # First try TTL-based eviction
        ttl_victim = self.ttl_policy.select_victim(entries)
        if ttl_victim:
            return ttl_victim

        # Then use adaptive strategy based on access patterns
        current_time = time.time()
        scored_entries = []

        for key, entry in entries.items():
            # Calculate adaptive score
            recency_score = 1.0 / (current_time - entry.last_access + 1)
            frequency_score = entry.access_count / 100.0
            priority_score = entry.priority / 10.0

            # Weighted combination
            adaptive_score = (0.4 * recency_score + 0.4 * frequency_score + 0.2 * priority_score)
            scored_entries.append((key, adaptive_score))

        if not scored_entries:
            return None

        # Return entry with lowest score
        return min(scored_entries, key=lambda x: x[1])[0]

class AdvancedCacheSystem:
    """Advanced multi-level cache system with intelligent management."""

    def __init__(self,
                 max_memory_size: int = 100 * 1024 * 1024,  # 100MB
                 max_entries: int = 10000,
                 default_ttl: Optional[float] = 3600.0,  # 1 hour
                 eviction_policy: str = "adaptive",
                 persistence_path: Optional[Path] = None):
        """Initialize the advanced cache system.

        Args:
            max_memory_size: Maximum memory usage in bytes
            max_entries: Maximum number of cache entries
            default_ttl: Default time-to-live in seconds
            eviction_policy: Eviction policy ("lru", "lfu", "ttl", "adaptive")
            persistence_path: Path for persistent cache storage
        """
        self.max_memory_size = max_memory_size
        self.max_entries = max_entries
        self.default_ttl = default_ttl
        self.persistence_path = persistence_path

        # Cache storage
        self.memory_cache: Dict[str, CacheEntry] = {}
        self.access_order = OrderedDict()  # For LRU tracking

        # Statistics
        self.stats = CacheStats(0, 0, 0, 0, 0.0, 0, 0)

        # Thread safety
        self.lock = threading.RLock()

        # Eviction policy
        self.eviction_policy = self._create_eviction_policy(eviction_policy)

        # Background cleanup thread
        self.cleanup_thread: Optional[threading.Thread] = None
        self.cleanup_running = False

        # Persistent storage
        self.db_connection: Optional[sqlite3.Connection] = None
        if persistence_path:
            self._init_persistent_storage()

        # Cache levels
        self.cache_levels = {
            "memory": self.memory_cache,
            "disk": {}
        }

        # Performance monitoring
        self.performance_metrics = defaultdict(list)

    def _create_eviction_policy(self, policy_name: str) -> CacheEvictionPolicy:
        """Create eviction policy based on name."""
        policies = {
            "lru": LRUEvictionPolicy(),
            "lfu": LFUEvictionPolicy(),
            "ttl": TTLEvictionPolicy(),
            "adaptive": AdaptiveEvictionPolicy()
        }
        return policies.get(policy_name, AdaptiveEvictionPolicy())

    def _init_persistent_storage(self):
        """Initialize persistent storage."""
        if not self.persistence_path:
            return

        self.persistence_path.parent.mkdir(parents=True, exist_ok=True)
        self.db_connection = sqlite3.connect(str(self.persistence_path), check_same_thread=False)

        # Create cache table
        self.db_connection.execute("""
            CREATE TABLE IF NOT EXISTS cache_entries (
                key TEXT PRIMARY KEY,
                value BLOB,
                timestamp REAL,
                access_count INTEGER,
                last_access REAL,
                ttl REAL,
                size INTEGER,
                priority INTEGER
            )
        """)
        self.db_connection.commit()

    def start_cleanup(self):
        """Start background cleanup thread."""
        with self.lock:
            if not self.cleanup_running:
                self.cleanup_running = True
                self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
                self.cleanup_thread.start()
                logger.info("Cache cleanup thread started")

    def stop_cleanup(self):
        """Stop background cleanup thread."""
        with self.lock:
            self.cleanup_running = False
            if self.cleanup_thread:
                self.cleanup_thread.join(timeout=5.0)
            logger.info("Cache cleanup thread stopped")

    def _cleanup_loop(self):
        """Background cleanup loop."""
        while self.cleanup_running:
            try:
                self._cleanup_expired_entries()
                self._enforce_size_limits()
                time.sleep(60)  # Cleanup every minute
            except Exception as e:
                logger.error(f"Error in cache cleanup: {e}", exc_info=True)

    def put(self, key: str, value: Any, ttl: Optional[float] = None, priority: int = 1) -> bool:
        """Store a value in the cache.

        Args:
            key: Cache key
            value: Value to store
            ttl: Time-to-live in seconds (None for default)
            priority: Priority level (higher = more important)

        Returns:
            True if stored successfully
        """
        with self.lock:
            try:
                # Calculate value size
                value_size = len(pickle.dumps(value))

                # Create cache entry
                entry = CacheEntry(
                    key=key,
                    value=value,
                    timestamp=time.time(),
                    access_count=0,
                    last_access=time.time(),
                    ttl=ttl or self.default_ttl,
                    size=value_size,
                    priority=priority
                )

                # Check if we need to evict entries
                self._make_space_for_entry(entry)

                # Store in memory cache
                self.memory_cache[key] = entry
                self.access_order[key] = time.time()

                # Update statistics
                self.stats.entry_count += 1
                self.stats.memory_usage += value_size

                # Store in persistent cache if available
                if self.db_connection:
                    self._store_to_disk(entry)

                return True

            except Exception as e:
                logger.error(f"Error storing cache entry: {e}", exc_info=True)
                return False

    def get(self, key: str) -> Optional[Any]:
        """Retrieve a value from the cache.

        Args:
            key: Cache key

        Returns:
            Cached value or None if not found
        """
        with self.lock:
            start_time = time.time()

            try:
                # Check memory cache first
                if key in self.memory_cache:
                    entry = self.memory_cache[key]

                    # Check if expired
                    if self._is_expired(entry):
                        self._remove_entry(key)
                        self.stats.misses += 1
                        return None

                    # Update access information
                    entry.access_count += 1
                    entry.last_access = time.time()
                    self.access_order[key] = entry.last_access

                    self.stats.hits += 1
                    self.stats.total_requests += 1
                    self._update_hit_rate()

                    # Record performance
                    access_time = time.time() - start_time
                    self.performance_metrics["access_times"].append(access_time)

                    return entry.value

                # Check persistent cache
                if self.db_connection:
                    disk_entry = self._load_from_disk(key)
                    if disk_entry and not self._is_expired(disk_entry):
                        # Promote to memory cache
                        self.memory_cache[key] = disk_entry
                        self.access_order[key] = time.time()

                        self.stats.hits += 1
                        self.stats.total_requests += 1
                        self._update_hit_rate()

                        return disk_entry.value

                # Cache miss
                self.stats.misses += 1
                self.stats.total_requests += 1
                self._update_hit_rate()

                return None

            except Exception as e:
                logger.error(f"Error retrieving cache entry: {e}", exc_info=True)
                self.stats.misses += 1
                self.stats.total_requests += 1
                return None

    def invalidate(self, key: str) -> bool:
        """Invalidate a cache entry.

        Args:
            key: Cache key to invalidate

        Returns:
            True if entry was found and removed
        """
        with self.lock:
            removed = False

            # Remove from memory cache
            if key in self.memory_cache:
                self._remove_entry(key)
                removed = True

            # Remove from persistent cache
            if self.db_connection:
                cursor = self.db_connection.cursor()
                cursor.execute("DELETE FROM cache_entries WHERE key = ?", (key,))
                self.db_connection.commit()
                if cursor.rowcount > 0:
                    removed = True

            return removed

    def clear(self):
        """Clear all cache entries."""
        with self.lock:
            self.memory_cache.clear()
            self.access_order.clear()

            if self.db_connection:
                self.db_connection.execute("DELETE FROM cache_entries")
                self.db_connection.commit()

            # Reset statistics
            self.stats = CacheStats(0, 0, 0, 0, 0.0, 0, 0)

    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        with self.lock:
            return CacheStats(
                hits=self.stats.hits,
                misses=self.stats.misses,
                evictions=self.stats.evictions,
                total_requests=self.stats.total_requests,
                hit_rate=self.stats.hit_rate,
                memory_usage=self.stats.memory_usage,
                entry_count=len(self.memory_cache)
            )

    def _make_space_for_entry(self, entry: CacheEntry):
        """Make space for a new cache entry."""
        # Check if we need to evict entries
        while (len(self.memory_cache) >= self.max_entries or
               self.stats.memory_usage + entry.size > self.max_memory_size):

            victim_key = self.eviction_policy.select_victim(self.memory_cache)
            if victim_key:
                self._remove_entry(victim_key)
            else:
                break

    def _remove_entry(self, key: str):
        """Remove an entry from the cache."""
        if key in self.memory_cache:
            entry = self.memory_cache[key]
            del self.memory_cache[key]
            self.access_order.pop(key, None)
            self.stats.memory_usage -= entry.size
            self.stats.entry_count -= 1
            self.stats.evictions += 1

    def _is_expired(self, entry: CacheEntry) -> bool:
        """Check if a cache entry is expired."""
        if entry.ttl is None:
            return False
        return time.time() - entry.timestamp > entry.ttl

    def _update_hit_rate(self):
        """Update the cache hit rate."""
        if self.stats.total_requests > 0:
            self.stats.hit_rate = (self.stats.hits / self.stats.total_requests) * 100

    def _store_to_disk(self, entry: CacheEntry):
        """Store an entry to persistent storage."""
        if not self.db_connection:
            return

        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO cache_entries
                (key, value, timestamp, access_count, last_access, ttl, size, priority)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                entry.key,
                pickle.dumps(entry.value),
                entry.timestamp,
                entry.access_count,
                entry.last_access,
                entry.ttl,
                entry.size,
                entry.priority
            ))
            self.db_connection.commit()
        except Exception as e:
            logger.error(f"Error storing to disk: {e}")

    def _load_from_disk(self, key: str) -> Optional[CacheEntry]:
        """Load an entry from persistent storage."""
        if not self.db_connection:
            return None

        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                SELECT key, value, timestamp, access_count, last_access, ttl, size, priority
                FROM cache_entries WHERE key = ?
            """, (key,))

            row = cursor.fetchone()
            if row:
                return CacheEntry(
                    key=row[0],
                    value=pickle.loads(row[1]),
                    timestamp=row[2],
                    access_count=row[3],
                    last_access=row[4],
                    ttl=row[5],
                    size=row[6],
                    priority=row[7]
                )
        except Exception as e:
            logger.error(f"Error loading from disk: {e}")

        return None

    def _cleanup_expired_entries(self):
        """Clean up expired entries."""
        current_time = time.time()
        expired_keys = []

        with self.lock:
            for key, entry in self.memory_cache.items():
                if self._is_expired(entry):
                    expired_keys.append(key)

        for key in expired_keys:
            self.invalidate(key)

    def _enforce_size_limits(self):
        """Enforce cache size limits."""
        with self.lock:
            while (len(self.memory_cache) > self.max_entries or
                   self.stats.memory_usage > self.max_memory_size):

                victim_key = self.eviction_policy.select_victim(self.memory_cache)
                if victim_key:
                    self._remove_entry(victim_key)
                else:
                    break
