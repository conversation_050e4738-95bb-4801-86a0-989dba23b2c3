"""
Context analyzer for the Advanced AI Agent.
"""

import os
import re
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Set, Pattern

# Set up logging
logger = logging.getLogger(__name__)

class ContextAnalyzer:
    """Analyzer for code context and patterns."""
    
    def __init__(self, workspace_dir: Optional[Path] = None):
        """Initialize the context analyzer.
        
        Args:
            workspace_dir: The workspace directory to use. If None, will use the current directory.
        """
        self.workspace_dir = workspace_dir or Path.cwd()
        self.lock = threading.RLock()
        
        # Language-specific patterns
        self.language_patterns: Dict[str, Dict[str, Pattern]] = {
            "python": {
                "import": re.compile(r"^\s*(?:from\s+(\S+)\s+)?import\s+(\S+)(?:\s+as\s+(\S+))?"),
                "function": re.compile(r"^\s*def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\("),
                "class": re.compile(r"^\s*class\s+([a-zA-Z_][a-zA-Z0-9_]*)"),
                "variable": re.compile(r"^\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*="),
                "method_call": re.compile(r"([a-zA-Z_][a-zA-Z0-9_]*)\s*\.\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\("),
                "function_call": re.compile(r"([a-zA-Z_][a-zA-Z0-9_]*)\s*\("),
            },
            "javascript": {
                "import": re.compile(r"^\s*import\s+(?:\{\s*([^}]+)\s*\}|\*\s+as\s+(\S+)|(\S+))\s+from"),
                "function": re.compile(r"^\s*(?:function|const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*(?:=\s*(?:function|\([^)]*\)\s*=>)|\()"),
                "class": re.compile(r"^\s*class\s+([a-zA-Z_$][a-zA-Z0-9_$]*)"),
                "variable": re.compile(r"^\s*(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*="),
                "method_call": re.compile(r"([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\.\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\("),
                "function_call": re.compile(r"([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\("),
            },
        }
        
        # File extension to language mapping
        self.extension_to_language = {
            ".py": "python",
            ".js": "javascript",
            ".jsx": "javascript",
            ".ts": "javascript",
            ".tsx": "javascript",
        }
        
        # Context data
        self.imports: Dict[str, Set[str]] = {}  # file -> set of imports
        self.functions: Dict[str, Set[str]] = {}  # file -> set of functions
        self.classes: Dict[str, Set[str]] = {}  # file -> set of classes
        self.variables: Dict[str, Set[str]] = {}  # file -> set of variables
        self.method_calls: Dict[str, Dict[str, Set[str]]] = {}  # file -> object -> set of methods
        self.function_calls: Dict[str, Set[str]] = {}  # file -> set of function calls
        
        # Usage frequency tracking
        self.import_frequency: Dict[str, int] = {}
        self.function_frequency: Dict[str, int] = {}
        self.class_frequency: Dict[str, int] = {}
        self.method_frequency: Dict[str, Dict[str, int]] = {}  # object -> method -> count
        
    def analyze_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Analyze a file to extract context information.
        
        Args:
            file_path: Path to the file to analyze.
            
        Returns:
            Dictionary with analysis results.
        """
        if isinstance(file_path, str):
            file_path = Path(file_path)
            
        # Resolve path if relative
        if not file_path.is_absolute():
            file_path = self.workspace_dir / file_path
            
        # Check if file exists
        if not file_path.exists() or not file_path.is_file():
            logger.warning(f"File not found: {file_path}")
            return {}
            
        # Determine language based on file extension
        extension = file_path.suffix.lower()
        language = self.extension_to_language.get(extension)
        
        if not language:
            logger.warning(f"Unsupported file type: {extension}")
            return {}
            
        try:
            # Read file content
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
                
            # Analyze content
            return self._analyze_content(str(file_path), content, language)
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}", exc_info=True)
            return {}
    
    def _analyze_content(self, file_path: str, content: str, language: str) -> Dict[str, Any]:
        """Analyze file content to extract context information.
        
        Args:
            file_path: Path to the file.
            content: File content.
            language: Programming language.
            
        Returns:
            Dictionary with analysis results.
        """
        patterns = self.language_patterns.get(language, {})
        if not patterns:
            return {}
            
        lines = content.splitlines()
        
        # Initialize result sets
        imports = set()
        functions = set()
        classes = set()
        variables = set()
        method_calls = {}
        function_calls = set()
        
        # Analyze each line
        for line in lines:
            # Check for imports
            match = patterns.get("import", re.compile("")).search(line)
            if match:
                if match.group(1):  # from X import Y
                    imports.add(match.group(1))
                if match.group(2):  # import Y or from X import Y
                    imports.add(match.group(2))
                if match.group(3):  # import Y as Z
                    imports.add(match.group(3))
                continue
                
            # Check for function definitions
            match = patterns.get("function", re.compile("")).search(line)
            if match and match.group(1):
                functions.add(match.group(1))
                continue
                
            # Check for class definitions
            match = patterns.get("class", re.compile("")).search(line)
            if match and match.group(1):
                classes.add(match.group(1))
                continue
                
            # Check for variable assignments
            match = patterns.get("variable", re.compile("")).search(line)
            if match and match.group(1):
                variables.add(match.group(1))
                continue
                
            # Check for method calls
            for match in patterns.get("method_call", re.compile("")).finditer(line):
                if match.group(1) and match.group(2):
                    obj = match.group(1)
                    method = match.group(2)
                    if obj not in method_calls:
                        method_calls[obj] = set()
                    method_calls[obj].add(method)
                    
            # Check for function calls
            for match in patterns.get("function_call", re.compile("")).finditer(line):
                if match.group(1):
                    function_calls.add(match.group(1))
        
        # Update context data
        with self.lock:
            self.imports[file_path] = imports
            self.functions[file_path] = functions
            self.classes[file_path] = classes
            self.variables[file_path] = variables
            self.method_calls[file_path] = method_calls
            self.function_calls[file_path] = function_calls
            
            # Update frequency counters
            for imp in imports:
                self.import_frequency[imp] = self.import_frequency.get(imp, 0) + 1
            for func in functions:
                self.function_frequency[func] = self.function_frequency.get(func, 0) + 1
            for cls in classes:
                self.class_frequency[cls] = self.class_frequency.get(cls, 0) + 1
            for obj, methods in method_calls.items():
                if obj not in self.method_frequency:
                    self.method_frequency[obj] = {}
                for method in methods:
                    self.method_frequency[obj][method] = self.method_frequency[obj].get(method, 0) + 1
        
        # Return analysis results
        return {
            "imports": list(imports),
            "functions": list(functions),
            "classes": list(classes),
            "variables": list(variables),
            "method_calls": {obj: list(methods) for obj, methods in method_calls.items()},
            "function_calls": list(function_calls),
        }
    
    def get_most_used_imports(self, limit: int = 10) -> List[Tuple[str, int]]:
        """Get the most frequently used imports.
        
        Args:
            limit: Maximum number of imports to return.
            
        Returns:
            List of (import, frequency) tuples, sorted by frequency.
        """
        with self.lock:
            return sorted(self.import_frequency.items(), key=lambda x: x[1], reverse=True)[:limit]
    
    def get_most_used_functions(self, limit: int = 10) -> List[Tuple[str, int]]:
        """Get the most frequently used functions.
        
        Args:
            limit: Maximum number of functions to return.
            
        Returns:
            List of (function, frequency) tuples, sorted by frequency.
        """
        with self.lock:
            return sorted(self.function_frequency.items(), key=lambda x: x[1], reverse=True)[:limit]
    
    def get_most_used_methods(self, obj: str, limit: int = 10) -> List[Tuple[str, int]]:
        """Get the most frequently used methods for an object.
        
        Args:
            obj: Object name.
            limit: Maximum number of methods to return.
            
        Returns:
            List of (method, frequency) tuples, sorted by frequency.
        """
        with self.lock:
            if obj not in self.method_frequency:
                return []
            return sorted(self.method_frequency[obj].items(), key=lambda x: x[1], reverse=True)[:limit]
