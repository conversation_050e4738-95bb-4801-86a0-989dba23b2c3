"""
Terminal utilities for the Advanced AI Agent.
"""

import os
import sys
from typing import Optional, Any

# ANSI color codes
COLORS = {
    "black": "\033[30m",
    "red": "\033[31m",
    "green": "\033[32m",
    "yellow": "\033[33m",
    "blue": "\033[34m",
    "magenta": "\033[35m",
    "cyan": "\033[36m",
    "white": "\033[37m",
    "reset": "\033[0m",
    "bold": "\033[1m",
    "underline": "\033[4m",
}

def print_colored(text: str, color: str, bold: bool = False, underline: bool = False, end: str = "\n", file: Any = sys.stdout) -> None:
    """Print colored text.
    
    Args:
        text: The text to print.
        color: The color to use.
        bold: Whether to make the text bold.
        underline: Whether to underline the text.
        end: The end character.
        file: The file to print to.
    """
    # Check if the terminal supports colors
    if not sys.stdout.isatty():
        print(text, end=end, file=file)
        return
    
    # Get the color code
    color_code = COLORS.get(color.lower(), COLORS["reset"])
    
    # Add bold and underline if requested
    if bold:
        color_code = COLORS["bold"] + color_code
    if underline:
        color_code = COLORS["underline"] + color_code
    
    # Print the colored text
    print(f"{color_code}{text}{COLORS['reset']}", end=end, file=file)

def print_error(text: str, end: str = "\n", file: Any = sys.stderr) -> None:
    """Print an error message.
    
    Args:
        text: The text to print.
        end: The end character.
        file: The file to print to.
    """
    print_colored(f"ERROR: {text}", "red", bold=True, end=end, file=file)

def print_warning(text: str, end: str = "\n", file: Any = sys.stdout) -> None:
    """Print a warning message.
    
    Args:
        text: The text to print.
        end: The end character.
        file: The file to print to.
    """
    print_colored(f"WARNING: {text}", "yellow", bold=True, end=end, file=file)

def print_success(text: str, end: str = "\n", file: Any = sys.stdout) -> None:
    """Print a success message.
    
    Args:
        text: The text to print.
        end: The end character.
        file: The file to print to.
    """
    print_colored(f"SUCCESS: {text}", "green", bold=True, end=end, file=file)

def print_info(text: str, end: str = "\n", file: Any = sys.stdout) -> None:
    """Print an info message.
    
    Args:
        text: The text to print.
        end: The end character.
        file: The file to print to.
    """
    print_colored(f"INFO: {text}", "blue", end=end, file=file)
