"""
Code predictor for the Advanced AI Agent.
"""

import os
import re
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Set, Pattern

from tools.predictive.context_analyzer import ContextAnalyzer
from tools.predictive.suggestion_cache import SuggestionCache

# Set up logging
logger = logging.getLogger(__name__)

class CodePredictor:
    """Predictor for code suggestions and completions."""
    
    def __init__(self, 
                context_analyzer: ContextAnalyzer,
                model_manager: Any,
                max_cache_size: int = 1000,
                cache_ttl: float = 300.0):
        """Initialize the code predictor.
        
        Args:
            context_analyzer: Context analyzer to use.
            model_manager: Model manager for generating predictions.
            max_cache_size: Maximum number of items to store in the cache.
            cache_ttl: Time-to-live for cache items in seconds.
        """
        self.context_analyzer = context_analyzer
        self.model_manager = model_manager
        self.suggestion_cache = SuggestionCache[List[str]](max_size=max_cache_size, ttl=cache_ttl)
        self.lock = threading.RLock()
        
        # Start the cache cleanup thread
        self.suggestion_cache.start()
        
        # Common patterns for different languages
        self.patterns = {
            "python": {
                "import_statement": re.compile(r"^\s*(?:from|import)\s+(\w+)"),
                "function_def": re.compile(r"^\s*def\s+(\w+)"),
                "class_def": re.compile(r"^\s*class\s+(\w+)"),
                "method_call": re.compile(r"(\w+)\s*\.\s*(\w*)$"),
                "function_call": re.compile(r"(\w+)\s*\($"),
            },
            "javascript": {
                "import_statement": re.compile(r"^\s*import\s+(?:\{\s*)?(\w*)"),
                "function_def": re.compile(r"^\s*(?:function|const|let|var)\s+(\w+)\s*(?:=\s*(?:function|\([^)]*\)\s*=>)|\()"),
                "class_def": re.compile(r"^\s*class\s+(\w+)"),
                "method_call": re.compile(r"(\w+)\s*\.\s*(\w*)$"),
                "function_call": re.compile(r"(\w+)\s*\($"),
            },
        }
        
        # File extension to language mapping
        self.extension_to_language = {
            ".py": "python",
            ".js": "javascript",
            ".jsx": "javascript",
            ".ts": "javascript",
            ".tsx": "javascript",
        }
    
    def predict_next_tokens(self, 
                           file_path: Union[str, Path], 
                           code_context: str,
                           max_suggestions: int = 5) -> List[str]:
        """Predict the next tokens based on code context.
        
        Args:
            file_path: Path to the file being edited.
            code_context: Current code context (usually the current line or block).
            max_suggestions: Maximum number of suggestions to return.
            
        Returns:
            List of predicted next tokens or code snippets.
        """
        if isinstance(file_path, str):
            file_path = Path(file_path)
            
        # Determine language based on file extension
        extension = file_path.suffix.lower()
        language = self.extension_to_language.get(extension, "python")  # Default to Python
        
        # Check cache first
        cache_key = f"{language}:{code_context}"
        cached_suggestions = self.suggestion_cache.get(cache_key)
        if cached_suggestions:
            logger.debug(f"Using cached suggestions for '{code_context}'")
            return cached_suggestions[:max_suggestions]
        
        # Generate suggestions based on context
        suggestions = self._generate_suggestions(file_path, code_context, language, max_suggestions)
        
        # Cache the suggestions
        self.suggestion_cache.put(cache_key, suggestions)
        
        return suggestions
    
    def _generate_suggestions(self, 
                             file_path: Path, 
                             code_context: str,
                             language: str,
                             max_suggestions: int) -> List[str]:
        """Generate code suggestions based on context.
        
        Args:
            file_path: Path to the file being edited.
            code_context: Current code context.
            language: Programming language.
            max_suggestions: Maximum number of suggestions.
            
        Returns:
            List of suggested code completions.
        """
        # Get language-specific patterns
        lang_patterns = self.patterns.get(language, self.patterns["python"])
        
        # Check for import statement
        match = lang_patterns["import_statement"].search(code_context)
        if match:
            prefix = match.group(1) or ""
            return self._suggest_imports(language, prefix, max_suggestions)
        
        # Check for method call
        match = lang_patterns["method_call"].search(code_context)
        if match:
            obj = match.group(1)
            prefix = match.group(2) or ""
            return self._suggest_methods(language, obj, prefix, max_suggestions)
        
        # Check for function call
        match = lang_patterns["function_call"].search(code_context)
        if match:
            prefix = match.group(1) or ""
            return self._suggest_functions(language, prefix, max_suggestions)
        
        # If no specific pattern matches, use the model for general prediction
        return self._predict_with_model(file_path, code_context, language, max_suggestions)
    
    def _suggest_imports(self, language: str, prefix: str, max_suggestions: int) -> List[str]:
        """Suggest import completions.
        
        Args:
            language: Programming language.
            prefix: Current import prefix.
            max_suggestions: Maximum number of suggestions.
            
        Returns:
            List of suggested import completions.
        """
        # Get most used imports from context analyzer
        most_used = self.context_analyzer.get_most_used_imports(limit=max_suggestions * 2)
        
        # Filter by prefix
        suggestions = [imp for imp, _ in most_used if imp.startswith(prefix)]
        
        # Add common libraries for the language
        if language == "python":
            common_libs = ["os", "sys", "re", "json", "time", "datetime", "pathlib", 
                          "numpy", "pandas", "matplotlib", "tensorflow", "torch", "sklearn"]
        elif language == "javascript":
            common_libs = ["react", "react-dom", "axios", "lodash", "moment", "express", 
                          "redux", "vue", "angular", "jquery", "underscore"]
        else:
            common_libs = []
            
        # Add common libraries that match the prefix
        for lib in common_libs:
            if lib.startswith(prefix) and lib not in suggestions:
                suggestions.append(lib)
                
        return suggestions[:max_suggestions]
    
    def _suggest_methods(self, language: str, obj: str, prefix: str, max_suggestions: int) -> List[str]:
        """Suggest method completions.
        
        Args:
            language: Programming language.
            obj: Object name.
            prefix: Current method prefix.
            max_suggestions: Maximum number of suggestions.
            
        Returns:
            List of suggested method completions.
        """
        # Get most used methods for this object from context analyzer
        most_used = self.context_analyzer.get_most_used_methods(obj, limit=max_suggestions * 2)
        
        # Filter by prefix
        suggestions = [method for method, _ in most_used if method.startswith(prefix)]
        
        # Add common methods for known objects
        common_methods = {}
        if language == "python":
            common_methods = {
                "str": ["strip", "split", "replace", "lower", "upper", "format", "join", "startswith", "endswith"],
                "list": ["append", "extend", "insert", "remove", "pop", "clear", "index", "count", "sort", "reverse"],
                "dict": ["get", "items", "keys", "values", "update", "pop", "clear", "copy"],
                "set": ["add", "remove", "discard", "update", "intersection", "union", "difference"],
                "file": ["read", "write", "close", "readline", "readlines", "seek", "tell"],
                "pd": ["read_csv", "DataFrame", "Series", "concat", "merge", "groupby", "apply", "head", "tail"],
                "np": ["array", "zeros", "ones", "random", "mean", "sum", "min", "max", "argmin", "argmax"],
            }
        elif language == "javascript":
            common_methods = {
                "Array": ["push", "pop", "shift", "unshift", "slice", "splice", "map", "filter", "reduce", "forEach"],
                "String": ["split", "replace", "toLowerCase", "toUpperCase", "trim", "substring", "indexOf", "match"],
                "Object": ["keys", "values", "entries", "assign", "hasOwnProperty", "toString"],
                "Promise": ["then", "catch", "finally", "all", "race", "resolve", "reject"],
                "console": ["log", "error", "warn", "info", "debug", "table", "time", "timeEnd"],
                "Math": ["abs", "ceil", "floor", "round", "max", "min", "random", "pow", "sqrt"],
            }
            
        # Add common methods that match the prefix
        if obj in common_methods:
            for method in common_methods[obj]:
                if method.startswith(prefix) and method not in suggestions:
                    suggestions.append(method)
                    
        return suggestions[:max_suggestions]
    
    def _suggest_functions(self, language: str, prefix: str, max_suggestions: int) -> List[str]:
        """Suggest function completions.
        
        Args:
            language: Programming language.
            prefix: Current function prefix.
            max_suggestions: Maximum number of suggestions.
            
        Returns:
            List of suggested function completions.
        """
        # Get most used functions from context analyzer
        most_used = self.context_analyzer.get_most_used_functions(limit=max_suggestions * 2)
        
        # Filter by prefix
        suggestions = [func for func, _ in most_used if func.startswith(prefix)]
        
        # Add common functions for the language
        if language == "python":
            common_funcs = ["print", "len", "range", "open", "str", "int", "float", "list", "dict", "set", 
                           "sum", "max", "min", "sorted", "enumerate", "zip", "map", "filter", "any", "all"]
        elif language == "javascript":
            common_funcs = ["parseInt", "parseFloat", "setTimeout", "setInterval", "clearTimeout", 
                           "clearInterval", "fetch", "console.log", "JSON.parse", "JSON.stringify"]
        else:
            common_funcs = []
            
        # Add common functions that match the prefix
        for func in common_funcs:
            if func.startswith(prefix) and func not in suggestions:
                suggestions.append(func)
                
        return suggestions[:max_suggestions]
    
    def _predict_with_model(self, file_path: Path, code_context: str, language: str, max_suggestions: int) -> List[str]:
        """Use the model to predict code completions.
        
        Args:
            file_path: Path to the file being edited.
            code_context: Current code context.
            language: Programming language.
            max_suggestions: Maximum number of suggestions.
            
        Returns:
            List of predicted code completions.
        """
        try:
            # Create a prompt for the model
            prompt = f"""
You are an AI coding assistant. Complete the following {language} code with {max_suggestions} different suggestions.
Only provide the code completions, no explanations.

Code context:
```{language}
{code_context}
```

Provide {max_suggestions} different completions, each on a new line:
"""
            
            # Generate completions
            response = self.model_manager.generate(prompt=prompt)
            
            # Parse the response
            suggestions = []
            for line in response.strip().split("\n"):
                line = line.strip()
                if line and not line.startswith("```") and not line.endswith("```"):
                    # Remove any numbering (e.g., "1. ", "2. ")
                    line = re.sub(r"^\d+\.\s*", "", line)
                    suggestions.append(line)
                    
            return suggestions[:max_suggestions]
        except Exception as e:
            logger.error(f"Error predicting with model: {e}", exc_info=True)
            return []
