"""
Enhanced Performance Monitor for the Advanced AI Agent.
Provides real-time performance monitoring, profiling, and optimization recommendations.
"""

import time
import psutil
import threading
import logging
import asyncio
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import json
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""
    timestamp: float
    cpu_usage: float
    memory_usage: float
    memory_available: float
    disk_io_read: float
    disk_io_write: float
    network_io_sent: float
    network_io_recv: float
    thread_count: int
    process_count: int
    response_time: float
    throughput: float
    error_rate: float
    cache_hit_rate: float

@dataclass
class PerformanceAlert:
    """Performance alert data structure."""
    alert_id: str
    timestamp: float
    severity: str  # critical, warning, info
    metric_name: str
    current_value: float
    threshold_value: float
    message: str
    recommendations: List[str]

@dataclass
class OptimizationSuggestion:
    """Optimization suggestion data structure."""
    suggestion_id: str
    category: str  # cpu, memory, io, network, algorithm
    priority: str  # high, medium, low
    description: str
    estimated_improvement: str
    implementation_effort: str
    code_changes: List[str]
    confidence_score: float

class EnhancedPerformanceMonitor:
    """Enhanced performance monitor with real-time analytics."""
    
    def __init__(self, workspace_dir: Path, monitoring_interval: float = 1.0):
        """Initialize the enhanced performance monitor.
        
        Args:
            workspace_dir: The workspace directory
            monitoring_interval: Monitoring interval in seconds
        """
        self.workspace_dir = workspace_dir
        self.monitoring_interval = monitoring_interval
        self.is_monitoring = False
        self.lock = threading.RLock()
        
        # Performance data storage
        self.metrics_history: deque = deque(maxlen=1000)  # Last 1000 measurements
        self.alerts: List[PerformanceAlert] = []
        self.optimization_suggestions: List[OptimizationSuggestion] = []
        
        # Performance thresholds
        self.thresholds = {
            "cpu_usage": 80.0,
            "memory_usage": 85.0,
            "response_time": 5.0,
            "error_rate": 5.0,
            "disk_io_wait": 100.0,
            "network_latency": 1000.0
        }
        
        # Monitoring thread
        self.monitor_thread: Optional[threading.Thread] = None
        
        # Performance counters
        self.request_count = 0
        self.error_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Baseline metrics for comparison
        self.baseline_metrics: Optional[PerformanceMetrics] = None
        
        # Callbacks for alerts
        self.alert_callbacks: List[Callable[[PerformanceAlert], None]] = []
        
    def start_monitoring(self):
        """Start performance monitoring."""
        with self.lock:
            if not self.is_monitoring:
                self.is_monitoring = True
                self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
                self.monitor_thread.start()
                logger.info("Enhanced performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring."""
        with self.lock:
            self.is_monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=5.0)
            logger.info("Enhanced performance monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                metrics = self._collect_metrics()
                self._analyze_metrics(metrics)
                self._check_thresholds(metrics)
                time.sleep(self.monitoring_interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}", exc_info=True)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics."""
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk_io = psutil.disk_io_counters()
        network_io = psutil.net_io_counters()
        
        # Process metrics
        process = psutil.Process()
        thread_count = process.num_threads()
        process_count = len(psutil.pids())
        
        # Application metrics
        current_time = time.time()
        response_time = self._calculate_avg_response_time()
        throughput = self._calculate_throughput()
        error_rate = self._calculate_error_rate()
        cache_hit_rate = self._calculate_cache_hit_rate()
        
        metrics = PerformanceMetrics(
            timestamp=current_time,
            cpu_usage=cpu_percent,
            memory_usage=memory.percent,
            memory_available=memory.available / (1024**3),  # GB
            disk_io_read=disk_io.read_bytes if disk_io else 0,
            disk_io_write=disk_io.write_bytes if disk_io else 0,
            network_io_sent=network_io.bytes_sent if network_io else 0,
            network_io_recv=network_io.bytes_recv if network_io else 0,
            thread_count=thread_count,
            process_count=process_count,
            response_time=response_time,
            throughput=throughput,
            error_rate=error_rate,
            cache_hit_rate=cache_hit_rate
        )
        
        with self.lock:
            self.metrics_history.append(metrics)
            
        return metrics
    
    def _analyze_metrics(self, metrics: PerformanceMetrics):
        """Analyze metrics and generate optimization suggestions."""
        suggestions = []
        
        # CPU optimization suggestions
        if metrics.cpu_usage > 70:
            suggestions.append(OptimizationSuggestion(
                suggestion_id=f"cpu_opt_{int(time.time())}",
                category="cpu",
                priority="high" if metrics.cpu_usage > 85 else "medium",
                description="High CPU usage detected",
                estimated_improvement="20-40% CPU reduction",
                implementation_effort="medium",
                code_changes=[
                    "Add async/await patterns for I/O operations",
                    "Implement caching for expensive computations",
                    "Use multiprocessing for CPU-intensive tasks"
                ],
                confidence_score=0.8
            ))
        
        # Memory optimization suggestions
        if metrics.memory_usage > 75:
            suggestions.append(OptimizationSuggestion(
                suggestion_id=f"mem_opt_{int(time.time())}",
                category="memory",
                priority="high" if metrics.memory_usage > 90 else "medium",
                description="High memory usage detected",
                estimated_improvement="30-50% memory reduction",
                implementation_effort="medium",
                code_changes=[
                    "Implement lazy loading patterns",
                    "Use generators instead of lists",
                    "Add memory pooling for large objects",
                    "Implement garbage collection optimization"
                ],
                confidence_score=0.75
            ))
        
        # Response time optimization
        if metrics.response_time > 3.0:
            suggestions.append(OptimizationSuggestion(
                suggestion_id=f"resp_opt_{int(time.time())}",
                category="performance",
                priority="high",
                description="Slow response times detected",
                estimated_improvement="50-70% response time improvement",
                implementation_effort="high",
                code_changes=[
                    "Add database query optimization",
                    "Implement request caching",
                    "Use connection pooling",
                    "Add parallel processing"
                ],
                confidence_score=0.85
            ))
        
        with self.lock:
            self.optimization_suggestions.extend(suggestions)
    
    def _check_thresholds(self, metrics: PerformanceMetrics):
        """Check metrics against thresholds and generate alerts."""
        alerts = []
        
        # Check CPU threshold
        if metrics.cpu_usage > self.thresholds["cpu_usage"]:
            alerts.append(PerformanceAlert(
                alert_id=f"cpu_alert_{int(time.time())}",
                timestamp=metrics.timestamp,
                severity="critical" if metrics.cpu_usage > 95 else "warning",
                metric_name="cpu_usage",
                current_value=metrics.cpu_usage,
                threshold_value=self.thresholds["cpu_usage"],
                message=f"CPU usage is {metrics.cpu_usage:.1f}%, exceeding threshold of {self.thresholds['cpu_usage']:.1f}%",
                recommendations=[
                    "Optimize CPU-intensive operations",
                    "Implement async processing",
                    "Add load balancing"
                ]
            ))
        
        # Check memory threshold
        if metrics.memory_usage > self.thresholds["memory_usage"]:
            alerts.append(PerformanceAlert(
                alert_id=f"mem_alert_{int(time.time())}",
                timestamp=metrics.timestamp,
                severity="critical" if metrics.memory_usage > 95 else "warning",
                metric_name="memory_usage",
                current_value=metrics.memory_usage,
                threshold_value=self.thresholds["memory_usage"],
                message=f"Memory usage is {metrics.memory_usage:.1f}%, exceeding threshold of {self.thresholds['memory_usage']:.1f}%",
                recommendations=[
                    "Implement memory optimization",
                    "Add garbage collection",
                    "Use memory pooling"
                ]
            ))
        
        # Check response time threshold
        if metrics.response_time > self.thresholds["response_time"]:
            alerts.append(PerformanceAlert(
                alert_id=f"resp_alert_{int(time.time())}",
                timestamp=metrics.timestamp,
                severity="warning",
                metric_name="response_time",
                current_value=metrics.response_time,
                threshold_value=self.thresholds["response_time"],
                message=f"Response time is {metrics.response_time:.2f}s, exceeding threshold of {self.thresholds['response_time']:.2f}s",
                recommendations=[
                    "Optimize database queries",
                    "Add caching layers",
                    "Implement request optimization"
                ]
            ))
        
        with self.lock:
            self.alerts.extend(alerts)
            
        # Trigger alert callbacks
        for alert in alerts:
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger.error(f"Error in alert callback: {e}", exc_info=True)
    
    def _calculate_avg_response_time(self) -> float:
        """Calculate average response time from recent metrics."""
        with self.lock:
            if len(self.metrics_history) < 2:
                return 0.0
            
            recent_metrics = list(self.metrics_history)[-10:]  # Last 10 measurements
            response_times = [m.response_time for m in recent_metrics if m.response_time > 0]
            
            return sum(response_times) / len(response_times) if response_times else 0.0
    
    def _calculate_throughput(self) -> float:
        """Calculate current throughput (requests per second)."""
        with self.lock:
            if len(self.metrics_history) < 2:
                return 0.0
            
            time_window = 60.0  # 1 minute window
            current_time = time.time()
            recent_requests = sum(1 for m in self.metrics_history 
                                if current_time - m.timestamp <= time_window)
            
            return recent_requests / time_window
    
    def _calculate_error_rate(self) -> float:
        """Calculate current error rate percentage."""
        if self.request_count == 0:
            return 0.0
        return (self.error_count / self.request_count) * 100
    
    def _calculate_cache_hit_rate(self) -> float:
        """Calculate cache hit rate percentage."""
        total_cache_requests = self.cache_hits + self.cache_misses
        if total_cache_requests == 0:
            return 0.0
        return (self.cache_hits / total_cache_requests) * 100
