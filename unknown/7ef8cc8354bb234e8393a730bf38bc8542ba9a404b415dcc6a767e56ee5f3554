"""
Security utilities for the Advanced AI Agent.
"""

import os
import re
import hashlib
from typing import List, Set, Optional, Any

def sanitize_input(input_str: str) -> str:
    """Sanitize user input to prevent command injection.
    
    Args:
        input_str: The input string to sanitize.
        
    Returns:
        The sanitized input string.
    """
    # Remove any shell special characters
    sanitized = re.sub(r'[;&|<>$`\\"\']', '', input_str)
    return sanitized

def is_safe_path(base_path: str, path: str) -> bool:
    """Check if a path is safe to access.
    
    Args:
        base_path: The base path that should contain the path.
        path: The path to check.
        
    Returns:
        Whether the path is safe to access.
    """
    # Normalize paths
    base_path = os.path.normpath(os.path.abspath(base_path))
    path = os.path.normpath(os.path.abspath(path))
    
    # Check if the path is within the base path
    return path.startswith(base_path)

def hash_password(password: str) -> str:
    """Hash a password.
    
    Args:
        password: The password to hash.
        
    Returns:
        The hashed password.
    """
    # Use a secure hashing algorithm
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed_password: str) -> bool:
    """Verify a password against a hash.
    
    Args:
        password: The password to verify.
        hashed_password: The hashed password to verify against.
        
    Returns:
        Whether the password matches the hash.
    """
    # Hash the password and compare
    return hash_password(password) == hashed_password

def generate_api_key() -> str:
    """Generate a secure API key.
    
    Returns:
        The generated API key.
    """
    # Generate a random API key
    return hashlib.sha256(os.urandom(32)).hexdigest()

def is_safe_command(command: str, allowed_commands: Optional[Set[str]] = None) -> bool:
    """Check if a command is safe to execute.
    
    Args:
        command: The command to check.
        allowed_commands: A set of allowed commands. If None, will use a default set.
        
    Returns:
        Whether the command is safe to execute.
    """
    # Default set of allowed commands
    if allowed_commands is None:
        allowed_commands = {
            "ls", "dir", "cd", "pwd", "echo", "cat", "type",
            "grep", "findstr", "find", "mkdir", "md", "rmdir", "rd",
            "touch", "cp", "copy", "mv", "move", "rm", "del",
            "python", "python3", "pip", "pip3", "npm", "node",
            "git", "curl", "wget"
        }
    
    # Extract the command name (first word)
    command_name = command.strip().split()[0]
    
    # Check if the command is allowed
    return command_name in allowed_commands
