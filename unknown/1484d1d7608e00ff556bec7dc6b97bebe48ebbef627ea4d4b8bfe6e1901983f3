"""
RAG (Retrieval-Augmented Generation) tool for the Advanced AI Agent.
"""

import os
import json
import pickle
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple

class RagTool:
    """RAG tool for retrieval-augmented generation."""

    def __init__(self, workspace_dir: Optional[Path] = None):
        """Initialize the RAG tool.

        Args:
            workspace_dir: The workspace directory to use. If None, will use the current directory.
        """
        self.workspace_dir = workspace_dir or Path.cwd()
        self.history: List[Dict[str, str]] = []

        # Check if the required libraries are installed
        self.is_faiss_available = self._check_faiss_available()
        self.is_sentence_transformers_available = self._check_sentence_transformers_available()

        # Initialize the index and documents
        self.index = None
        self.documents = []
        self.embeddings = None

    def _check_faiss_available(self) -> bool:
        """Check if faiss is available.

        Returns:
            Whether faiss is available.
        """
        try:
            # Just check if the module can be imported, don't actually import it
            import importlib.util
            return importlib.util.find_spec("faiss") is not None
        except ImportError:
            return False

    def _check_sentence_transformers_available(self) -> bool:
        """Check if sentence-transformers is available.

        Returns:
            Whether sentence-transformers is available.
        """
        try:
            # Just check if the module can be imported, don't actually import it
            import importlib.util
            return importlib.util.find_spec("sentence_transformers") is not None
        except ImportError:
            return False

    def create_index(self, documents: List[str]) -> Tuple[bool, str]:
        """Create an index from a list of documents.

        Args:
            documents: The documents to index.

        Returns:
            A tuple of (success, message).
        """
        # Add to history
        self.history.append({"action": "create_index", "document_count": len(documents)})

        # Check if the required libraries are installed
        if not self.is_faiss_available or not self.is_sentence_transformers_available:
            error_message = "Required libraries not available. Please install faiss-cpu and sentence-transformers."
            self.history[-1]["error"] = error_message
            return False, error_message

        try:
            import faiss
            from sentence_transformers import SentenceTransformer

            # Initialize the model
            self.embeddings = SentenceTransformer("all-MiniLM-L6-v2")

            # Compute embeddings
            embeddings = self.embeddings.encode(documents)

            # Create the index
            dimension = embeddings.shape[1]
            self.index = faiss.IndexFlatL2(dimension)
            self.index.add(embeddings)

            # Store the documents
            self.documents = documents

            return True, f"Index created with {len(documents)} documents."

        except Exception as e:
            error_message = f"Error creating index: {e}"
            self.history[-1]["error"] = error_message
            return False, error_message

    def add_documents(self, documents: List[str]) -> Tuple[bool, str]:
        """Add documents to the index.

        Args:
            documents: The documents to add.

        Returns:
            A tuple of (success, message).
        """
        # Add to history
        self.history.append({"action": "add_documents", "document_count": len(documents)})

        # Check if the required libraries are installed
        if not self.is_faiss_available or not self.is_sentence_transformers_available:
            error_message = "Required libraries not available. Please install faiss-cpu and sentence-transformers."
            self.history[-1]["error"] = error_message
            return False, error_message

        # Check if the index exists
        if self.index is None:
            error_message = "Index not created. Please create an index first."
            self.history[-1]["error"] = error_message
            return False, error_message

        try:
            # Compute embeddings
            embeddings = self.embeddings.encode(documents)

            # Add to the index
            self.index.add(embeddings)

            # Add to the documents
            self.documents.extend(documents)

            return True, f"Added {len(documents)} documents to the index."

        except Exception as e:
            error_message = f"Error adding documents: {e}"
            self.history[-1]["error"] = error_message
            return False, error_message

    def search(self, query: str, k: int = 5) -> Tuple[bool, List[Dict[str, Any]]]:
        """Search the index.

        Args:
            query: The query to search for.
            k: The number of results to return.

        Returns:
            A tuple of (success, results).
        """
        # Add to history
        self.history.append({"action": "search", "query": query, "k": k})

        # Check if the required libraries are installed
        if not self.is_faiss_available or not self.is_sentence_transformers_available:
            error_message = "Required libraries not available. Please install faiss-cpu and sentence-transformers."
            self.history[-1]["error"] = error_message
            return False, []

        # Check if the index exists
        if self.index is None:
            error_message = "Index not created. Please create an index first."
            self.history[-1]["error"] = error_message
            return False, []

        try:
            # Compute the query embedding
            query_embedding = self.embeddings.encode([query])

            # Search the index
            distances, indices = self.index.search(query_embedding, k)

            # Get the results
            results = []
            for i, idx in enumerate(indices[0]):
                if idx < len(self.documents):
                    results.append({
                        "document": self.documents[idx],
                        "distance": float(distances[0][i])
                    })

            return True, results

        except Exception as e:
            error_message = f"Error searching: {e}"
            self.history[-1]["error"] = error_message
            return False, []

    def save_index(self, path: Union[str, Path]) -> Tuple[bool, str]:
        """Save the index to a file.

        Args:
            path: The path to save the index to.

        Returns:
            A tuple of (success, message).
        """
        # Add to history
        self.history.append({"action": "save_index", "path": str(path)})

        # Check if the index exists
        if self.index is None:
            error_message = "Index not created. Please create an index first."
            self.history[-1]["error"] = error_message
            return False, error_message

        try:
            # Resolve the path
            file_path = self._resolve_path(path)

            # Create the parent directory if it doesn't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Save the index
            import faiss
            faiss.write_index(self.index, str(file_path))

            # Save the documents
            with open(f"{file_path}.documents", "wb") as f:
                pickle.dump(self.documents, f)

            return True, f"Index saved to {file_path}"

        except Exception as e:
            error_message = f"Error saving index: {e}"
            self.history[-1]["error"] = error_message
            return False, error_message

    def load_index(self, path: Union[str, Path]) -> Tuple[bool, str]:
        """Load the index from a file.

        Args:
            path: The path to load the index from.

        Returns:
            A tuple of (success, message).
        """
        # Add to history
        self.history.append({"action": "load_index", "path": str(path)})

        # Check if the required libraries are installed
        if not self.is_faiss_available or not self.is_sentence_transformers_available:
            error_message = "Required libraries not available. Please install faiss-cpu and sentence-transformers."
            self.history[-1]["error"] = error_message
            return False, error_message

        try:
            # Resolve the path
            file_path = self._resolve_path(path)

            # Check if the file exists
            if not file_path.exists():
                error_message = f"Index file not found: {file_path}"
                self.history[-1]["error"] = error_message
                return False, error_message

            # Load the index
            import faiss
            from sentence_transformers import SentenceTransformer

            self.index = faiss.read_index(str(file_path))

            # Load the documents
            documents_path = f"{file_path}.documents"
            if os.path.exists(documents_path):
                with open(documents_path, "rb") as f:
                    self.documents = pickle.load(f)
            else:
                self.documents = []

            # Initialize the model
            self.embeddings = SentenceTransformer("all-MiniLM-L6-v2")

            return True, f"Index loaded from {file_path}"

        except Exception as e:
            error_message = f"Error loading index: {e}"
            self.history[-1]["error"] = error_message
            return False, error_message

    def get_history(self) -> List[Dict[str, str]]:
        """Get the RAG history.

        Returns:
            The RAG history.
        """
        return self.history

    def _resolve_path(self, path: Union[str, Path]) -> Path:
        """Resolve a path relative to the workspace directory.

        Args:
            path: The path to resolve.

        Returns:
            The resolved path.
        """
        if isinstance(path, str):
            path = Path(path)

        if path.is_absolute():
            return path

        return self.workspace_dir / path
