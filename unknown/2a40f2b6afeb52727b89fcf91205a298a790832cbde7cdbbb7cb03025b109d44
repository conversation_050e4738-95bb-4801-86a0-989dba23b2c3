"""
Web scraper tool for the Advanced AI Agent.
This provides a robust web scraping mechanism with multiple fallback options.
"""

import re
import json
import time
import random
import urllib.parse
from typing import Dict, List, Optional, Any, Union, Tuple, Callable

import requests
from bs4 import BeautifulSoup
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

# Try to import optional dependencies
try:
    import newspaper
    NEWSPAPER_AVAILABLE = True
except ImportError:
    NEWSPAPER_AVAILABLE = False

try:
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

try:
    import trafilatura
    TRAFILATURA_AVAILABLE = True
except ImportError:
    TRAFILATURA_AVAILABLE = False

class WebScraperTool:
    """Web scraper tool for robust web content extraction."""

    def __init__(self):
        """Initialize the web scraper tool."""
        self.history: List[Dict[str, Any]] = []

        # User agent rotation
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
        ]

        # Configure session with retry mechanism
        self.session = self._create_robust_session()

    def _create_robust_session(self) -> requests.Session:
        """Create a requests session with retry mechanism.

        Returns:
            A configured requests session.
        """
        session = requests.Session()

        # Configure retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=0.5,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET", "POST"]
        )

        # Mount the adapter to the session
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # Set default headers
        session.headers.update({
            "User-Agent": random.choice(self.user_agents),
            "Accept-Language": "en-US,en;q=0.9",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "DNT": "1"
        })

        return session

    def _rotate_user_agent(self) -> None:
        """Rotate the user agent to avoid detection."""
        self.session.headers.update({
            "User-Agent": random.choice(self.user_agents)
        })

    def scrape_url(self, url: str, use_fallbacks: bool = True) -> Tuple[bool, str, Dict[str, Any]]:
        """Scrape content from a URL with multiple fallback mechanisms.

        Args:
            url: The URL to scrape.
            use_fallbacks: Whether to use fallback mechanisms if the primary method fails.

        Returns:
            A tuple of (success, content, metadata).
        """
        # Add to history
        self.history.append({
            "action": "scrape_url",
            "url": url,
            "timestamp": time.time()
        })

        # Rotate user agent
        self._rotate_user_agent()

        # Try primary method first (requests + BeautifulSoup)
        success, content, metadata = self._scrape_with_requests_bs4(url)

        # If primary method failed and fallbacks are enabled, try fallbacks
        if not success and use_fallbacks:
            # Try newspaper3k if available
            if not success and NEWSPAPER_AVAILABLE:
                success, content, metadata = self._scrape_with_newspaper(url)

            # Try playwright if available
            if not success and PLAYWRIGHT_AVAILABLE:
                success, content, metadata = self._scrape_with_playwright(url)

            # Try trafilatura if available
            if not success and TRAFILATURA_AVAILABLE:
                success, content, metadata = self._scrape_with_trafilatura(url)

        # Update history with results
        self.history[-1].update({
            "success": success,
            "metadata": metadata
        })

        return success, content, metadata

    def _scrape_with_requests_bs4(self, url: str) -> Tuple[bool, str, Dict[str, Any]]:
        """Scrape a URL using requests and BeautifulSoup.

        Args:
            url: The URL to scrape.

        Returns:
            A tuple of (success, content, metadata).
        """
        try:
            # Send the request
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            # Get the content type
            content_type = response.headers.get("Content-Type", "")

            # Initialize metadata
            metadata = {
                "url": url,
                "content_type": content_type,
                "status_code": response.status_code,
                "method": "requests_bs4"
            }

            # Parse the response based on the content type
            if "text/html" in content_type:
                # Parse HTML
                soup = BeautifulSoup(response.text, "html.parser")

                # Extract metadata
                metadata["title"] = soup.title.string.strip() if soup.title else ""

                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.extract()

                # Get the text
                text = soup.get_text()

                # Clean up the text
                lines = (line.strip() for line in text.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                text = "\n".join(chunk for chunk in chunks if chunk)

                return True, text, metadata

            elif "application/json" in content_type:
                # Parse JSON
                data = response.json()

                # Convert to string
                text = json.dumps(data, indent=2)

                return True, text, metadata

            else:
                # Return raw text
                return True, response.text, metadata

        except Exception as e:
            error_message = f"Error scraping URL with requests/BS4: {e}"
            print(error_message)
            return False, error_message, {"url": url, "error": str(e), "method": "requests_bs4"}

    def _scrape_with_newspaper(self, url: str) -> Tuple[bool, str, Dict[str, Any]]:
        """Scrape a URL using newspaper3k.

        Args:
            url: The URL to scrape.

        Returns:
            A tuple of (success, content, metadata).
        """
        try:
            # Parse the article
            article = newspaper.Article(url)
            article.download()
            article.parse()

            # Extract metadata
            metadata = {
                "url": url,
                "title": article.title,
                "authors": article.authors,
                "publish_date": str(article.publish_date) if article.publish_date else None,
                "method": "newspaper3k"
            }

            # Get the text
            text = article.text

            return True, text, metadata

        except Exception as e:
            error_message = f"Error scraping URL with newspaper3k: {e}"
            print(error_message)
            return False, error_message, {"url": url, "error": str(e), "method": "newspaper3k"}

    def _scrape_with_playwright(self, url: str) -> Tuple[bool, str, Dict[str, Any]]:
        """Scrape a URL using playwright.

        Args:
            url: The URL to scrape.

        Returns:
            A tuple of (success, content, metadata).
        """
        try:
            with sync_playwright() as p:
                # Launch browser with stealth mode
                browser = p.chromium.launch(headless=True)

                # Create a context with a more realistic browser profile
                context = browser.new_context(
                    user_agent=random.choice(self.user_agents),
                    viewport={"width": 1280, "height": 800},
                    locale="en-US"
                )

                # Create a new page
                page = context.new_page()

                # Navigate to the URL
                page.goto(url, wait_until="networkidle", timeout=30000)

                # Wait for content to load
                page.wait_for_timeout(2000)

                # Get the page title
                title = page.title()

                # Extract the text content
                text = page.evaluate("() => document.body.innerText")

                # Close the browser
                browser.close()

                # Extract metadata
                metadata = {
                    "url": url,
                    "title": title,
                    "method": "playwright"
                }

                return True, text, metadata

        except Exception as e:
            error_message = f"Error scraping URL with playwright: {e}"
            print(error_message)
            return False, error_message, {"url": url, "error": str(e), "method": "playwright"}

    def _scrape_with_trafilatura(self, url: str) -> Tuple[bool, str, Dict[str, Any]]:
        """Scrape a URL using trafilatura.

        Args:
            url: The URL to scrape.

        Returns:
            A tuple of (success, content, metadata).
        """
        try:
            # Download and extract the main content
            downloaded = trafilatura.fetch_url(url)
            if downloaded is None:
                return False, "Failed to download URL with trafilatura", {"url": url, "error": "Download failed", "method": "trafilatura"}

            # Extract the text
            text = trafilatura.extract(downloaded)

            if text is None or text.strip() == "":
                return False, "No content extracted with trafilatura", {"url": url, "error": "No content extracted", "method": "trafilatura"}

            # Extract metadata
            metadata = {
                "url": url,
                "method": "trafilatura"
            }

            return True, text, metadata

        except Exception as e:
            error_message = f"Error scraping URL with trafilatura: {e}"
            print(error_message)
            return False, error_message, {"url": url, "error": str(e), "method": "trafilatura"}

    def get_history(self) -> List[Dict[str, Any]]:
        """Get the scraper history.

        Returns:
            The scraper history.
        """
        return self.history
