"""
Browser tool for the Advanced AI Agent.
"""

import os
import time
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple

class BrowserTool:
    """Browser tool for web browsing."""

    def __init__(self):
        """Initialize the browser tool."""
        self.history: List[Dict[str, str]] = []

        # Check if playwright is installed
        self.is_playwright_available = self._check_playwright_available()

        # Check if lynx is installed
        self.is_lynx_available = self._check_lynx_available()

    def _check_playwright_available(self) -> bool:
        """Check if playwright is available.

        Returns:
            Whether playwright is available.
        """
        try:
            # Just check if the module can be imported, don't actually import it
            import importlib.util
            return importlib.util.find_spec("playwright") is not None
        except ImportError:
            return False

    def _check_lynx_available(self) -> bool:
        """Check if lynx is available.

        Returns:
            Whether lynx is available.
        """
        try:
            subprocess.run(["lynx", "--version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
            return True
        except (subprocess.SubprocessError, FileNotFoundError):
            return False

    def read_url(self, url: str) -> Tuple[bool, str]:
        """Read a URL.

        Args:
            url: The URL to read.

        Returns:
            A tuple of (success, content).
        """
        # Add to history
        self.history.append({"action": "read_url", "url": url})

        # Try to use playwright
        if self.is_playwright_available:
            try:
                from playwright.sync_api import sync_playwright

                with sync_playwright() as p:
                    browser = p.chromium.launch(headless=True)
                    page = browser.new_page()
                    page.goto(url)
                    content = page.content()
                    text = page.evaluate("() => document.body.innerText")
                    browser.close()

                return True, text
            except Exception as e:
                self.history[-1]["error"] = f"Error reading URL with playwright: {e}"

        # Try to use lynx
        if self.is_lynx_available:
            try:
                result = subprocess.run(
                    ["lynx", "-dump", "-nolist", url],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    check=True,
                    text=True
                )

                return True, result.stdout
            except subprocess.SubprocessError as e:
                self.history[-1]["error"] = f"Error reading URL with lynx: {e}"

        # Use requests as a fallback
        try:
            import requests
            from bs4 import BeautifulSoup

            response = requests.get(url)
            soup = BeautifulSoup(response.text, "html.parser")

            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.extract()

            # Get text
            text = soup.get_text()

            # Break into lines and remove leading and trailing space on each
            lines = (line.strip() for line in text.splitlines())
            # Break multi-headlines into a line each
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            # Remove blank lines
            text = "\n".join(chunk for chunk in chunks if chunk)

            return True, text
        except Exception as e:
            error_message = f"Error reading URL: {e}"
            self.history[-1]["error"] = error_message
            return False, error_message

    def screenshot_url(self, url: str, path: Optional[Union[str, Path]] = None) -> Tuple[bool, str]:
        """Take a screenshot of a URL.

        Args:
            url: The URL to screenshot.
            path: The path to save the screenshot to. If None, will save to a temporary file.

        Returns:
            A tuple of (success, path).
        """
        # Add to history
        self.history.append({"action": "screenshot_url", "url": url})

        # Check if playwright is available
        if not self.is_playwright_available:
            error_message = "Playwright is not available. Please install playwright."
            self.history[-1]["error"] = error_message
            return False, error_message

        try:
            from playwright.sync_api import sync_playwright

            # Determine the path to save the screenshot
            if path is None:
                # Create a temporary file
                fd, temp_path = tempfile.mkstemp(suffix=".png")
                os.close(fd)
                screenshot_path = temp_path
            else:
                # Resolve the path
                screenshot_path = str(path)

            # Take the screenshot
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                page = browser.new_page()
                page.goto(url)
                page.screenshot(path=screenshot_path)
                browser.close()

            return True, screenshot_path

        except Exception as e:
            error_message = f"Error taking screenshot: {e}"
            self.history[-1]["error"] = error_message
            return False, error_message

    def search(self, query: str, engine: str = "google") -> Tuple[bool, List[Dict[str, str]]]:
        """Search the web.

        Args:
            query: The query to search for.
            engine: The search engine to use.

        Returns:
            A tuple of (success, results).
        """
        # Add to history
        self.history.append({"action": "search", "query": query, "engine": engine})

        # Determine the search URL
        if engine.lower() == "google":
            url = f"https://www.google.com/search?q={query}"
        elif engine.lower() == "duckduckgo":
            url = f"https://duckduckgo.com/?q={query}"
        else:
            error_message = f"Unsupported search engine: {engine}"
            self.history[-1]["error"] = error_message
            return False, []

        # Try to use playwright
        if self.is_playwright_available:
            try:
                from playwright.sync_api import sync_playwright

                with sync_playwright() as p:
                    browser = p.chromium.launch(headless=True)

                    # Create a context with a more realistic browser profile
                    context = browser.new_context(
                        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                        viewport={"width": 1280, "height": 800},
                        locale="en-US"
                    )

                    page = context.new_page()

                    # Add headers
                    page.set_extra_http_headers({
                        "Accept-Language": "en-US,en;q=0.9",
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "Referer": "https://www.google.com/",
                        "DNT": "1"
                    })

                    # Navigate to the search page
                    response = page.goto(url, wait_until="networkidle")

                    # Wait a bit for any JavaScript to execute
                    page.wait_for_timeout(2000)

                    # Extract search results
                    results = []

                    if engine.lower() == "google":
                        # Try multiple selectors for Google search results
                        print("Extracting Google search results...")

                        # Try different selectors for result containers
                        selectors = [
                            "div.g",
                            "div.tF2Cxc",
                            "div.yuRUbf",
                            "div[data-sokoban-container]",
                            "div[jscontroller]"
                        ]

                        # Try each selector until we find results
                        result_elements = []
                        for selector in selectors:
                            result_elements = page.query_selector_all(selector)
                            if result_elements and len(result_elements) > 0:
                                print(f"Found {len(result_elements)} results with selector: {selector}")
                                break

                        # If we still don't have results, try a more generic approach
                        if not result_elements or len(result_elements) == 0:
                            print("No results found with specific selectors, trying generic h3 approach")
                            # Look for h3 elements which are typically titles
                            heading_elements = page.query_selector_all("h3")

                            for heading in heading_elements:
                                # Find the closest link
                                link = heading.evaluate("el => { return el.closest('a') || el.querySelector('a') || el.parentElement.querySelector('a'); }")

                                if link:
                                    title = heading.inner_text().strip()
                                    url = link.get_attribute("href")

                                    # Try to find a snippet near the heading
                                    snippet_element = heading.evaluate("el => { return el.parentElement.querySelector('div:not(:has(h3))') || el.parentElement.nextElementSibling; }")
                                    snippet = snippet_element.inner_text().strip() if snippet_element else ""

                                    results.append({
                                        "title": title,
                                        "url": url,
                                        "snippet": snippet
                                    })
                        else:
                            # Process the found result elements
                            for element in result_elements:
                                # Try different selectors for title
                                title_selectors = ["h3", ".LC20lb", "[role='heading']"]
                                title_element = None

                                for selector in title_selectors:
                                    title_element = element.query_selector(selector)
                                    if title_element:
                                        break

                                # Try to find the link
                                link_element = element.query_selector("a")

                                # Try different selectors for snippet
                                snippet_selectors = [
                                    "div.VwiC3b",
                                    ".lyLwlc",
                                    "[data-content-feature='1']",
                                    "div.s3v9rd",
                                    "span.aCOpRe",
                                    "div.lEBKkf"
                                ]

                                snippet_element = None
                                for selector in snippet_selectors:
                                    snippet_element = element.query_selector(selector)
                                    if snippet_element:
                                        break

                                if title_element and link_element:
                                    title = title_element.inner_text().strip()
                                    link = link_element.get_attribute("href")
                                    snippet = snippet_element.inner_text().strip() if snippet_element else ""

                                    # Only add if we have at least a title and URL
                                    if title and link:
                                        results.append({
                                            "title": title,
                                            "url": link,
                                            "snippet": snippet
                                        })

                    elif engine.lower() == "duckduckgo":
                        # Try multiple selectors for DuckDuckGo search results
                        print("Extracting DuckDuckGo search results...")

                        # Try different selectors for result containers
                        selectors = [
                            "article.result",
                            ".result__body",
                            ".nrn-react-div"
                        ]

                        # Try each selector until we find results
                        result_elements = []
                        for selector in selectors:
                            result_elements = page.query_selector_all(selector)
                            if result_elements and len(result_elements) > 0:
                                print(f"Found {len(result_elements)} results with selector: {selector}")
                                break

                        # Process the found result elements
                        for element in result_elements:
                            # Try different selectors for title
                            title_selectors = ["h2", "h3", ".result__title", ".result__a"]
                            title_element = None

                            for selector in title_selectors:
                                title_element = element.query_selector(selector)
                                if title_element:
                                    break

                            # Try to find the link
                            link_selectors = ["a.result__a", "a.result__url", "a"]
                            link_element = None

                            for selector in link_selectors:
                                link_element = element.query_selector(selector)
                                if link_element:
                                    break

                            # Try different selectors for snippet
                            snippet_selectors = [
                                "div.result__snippet",
                                ".result__snippet",
                                ".result__body"
                            ]

                            snippet_element = None
                            for selector in snippet_selectors:
                                snippet_element = element.query_selector(selector)
                                if snippet_element:
                                    break

                            if title_element and link_element:
                                title = title_element.inner_text().strip()
                                link = link_element.get_attribute("href")
                                snippet = snippet_element.inner_text().strip() if snippet_element else ""

                                results.append({
                                    "title": title,
                                    "url": link,
                                    "snippet": snippet
                                })

                    # If we still don't have results, take a screenshot for debugging
                    if not results:
                        print("No search results found. Taking a screenshot for debugging.")
                        screenshot_path = "search_debug_screenshot.png"
                        page.screenshot(path=screenshot_path)
                        print(f"Screenshot saved to {screenshot_path}")

                        # Save the HTML too
                        html_content = page.content()
                        with open("search_debug.html", "w", encoding="utf-8") as f:
                            f.write(html_content)
                        print("HTML saved to search_debug.html")

                        # Add a message to the results
                        results.append({
                            "title": "Search Error",
                            "url": url,
                            "snippet": f"No search results could be extracted. The search engine may have changed its HTML structure or blocked the request. Debug files saved."
                        })

                    browser.close()

                    return True, results

            except Exception as e:
                error_message = f"Error searching with playwright: {e}"
                print(f"Playwright search error: {e}")
                self.history[-1]["error"] = error_message

        # Use requests as a fallback
        try:
            import requests
            from bs4 import BeautifulSoup
            import urllib.parse

            # Encode the query for the URL
            encoded_query = urllib.parse.quote_plus(query)

            # Update the URL with the encoded query
            if engine.lower() == "google":
                url = f"https://www.google.com/search?q={encoded_query}"
            elif engine.lower() == "duckduckgo":
                url = f"https://duckduckgo.com/?q={encoded_query}"

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept-Language": "en-US,en;q=0.9",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Referer": "https://www.google.com/",
                "DNT": "1"
            }

            print(f"Sending request to {url}")
            response = requests.get(url, headers=headers)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, "html.parser")
            results = []

            if engine.lower() == "google":
                print("Parsing Google search results with BeautifulSoup")
                # Try multiple selector patterns to handle Google's changing layout
                search_results = (
                    soup.select("div.g") or
                    soup.select("div.tF2Cxc") or
                    soup.select("div.yuRUbf") or
                    soup.select("div[data-sokoban-container]")
                )

                print(f"Found {len(search_results)} search result containers")

                for result in search_results:
                    # Extract the title - try multiple possible selectors
                    title_elem = (
                        result.select_one("h3") or
                        result.select_one(".LC20lb") or
                        result.select_one("[role='heading']")
                    )

                    if not title_elem:
                        continue

                    title = title_elem.get_text().strip()

                    # Extract the URL - try multiple possible selectors
                    url_elem = result.select_one("a")
                    if not url_elem:
                        continue

                    url = url_elem.get("href")
                    if url.startswith("/url?q="):
                        url = url[7:]
                        url = url.split("&sa=")[0]

                    # Extract the snippet - try multiple possible selectors
                    snippet_elem = (
                        result.select_one("div.VwiC3b") or
                        result.select_one(".lyLwlc") or
                        result.select_one("[data-content-feature='1']") or
                        result.select_one("div.s3v9rd") or
                        result.select_one("span.aCOpRe")
                    )

                    snippet = snippet_elem.get_text().strip() if snippet_elem else ""

                    # Only add if we have at least a title and URL
                    if title and url:
                        results.append({
                            "title": title,
                            "url": url,
                            "snippet": snippet
                        })

                # If we couldn't find any results with the above selectors, try a fallback approach
                if not results:
                    print("No results found with primary selectors, trying fallback approach")
                    # Look for any links with headings nearby as a fallback
                    for heading in soup.select("h3"):
                        # Find the closest link
                        link = heading.find_parent("a") or heading.find_next("a")
                        if link and link.get("href"):
                            url = link.get("href")
                            if url.startswith("/url?q="):
                                url = url[7:]
                                url = url.split("&sa=")[0]

                            # Get the snippet - any nearby paragraph or div with text
                            snippet_elem = heading.find_next("div") or heading.find_next("p") or heading.find_next("span")
                            snippet = ""
                            if snippet_elem and snippet_elem.get_text() != heading.get_text():
                                snippet = snippet_elem.get_text().strip()

                            results.append({
                                "title": heading.get_text().strip(),
                                "url": url,
                                "snippet": snippet
                            })

            elif engine.lower() == "duckduckgo":
                print("Parsing DuckDuckGo search results with BeautifulSoup")
                # Try multiple selector patterns for DuckDuckGo
                search_results = (
                    soup.select("article.result") or
                    soup.select(".result__body") or
                    soup.select(".nrn-react-div")
                )

                print(f"Found {len(search_results)} search result containers")

                for result in search_results:
                    # Extract the title
                    title_elem = (
                        result.select_one("h2") or
                        result.select_one("h3") or
                        result.select_one(".result__title") or
                        result.select_one(".result__a")
                    )

                    if not title_elem:
                        continue

                    title = title_elem.get_text().strip()

                    # Extract the URL
                    link_elem = (
                        result.select_one("a.result__a") or
                        result.select_one("a.result__url") or
                        result.select_one("a")
                    )

                    if not link_elem:
                        continue

                    url = link_elem.get("href")

                    # Extract the snippet
                    snippet_elem = (
                        result.select_one("div.result__snippet") or
                        result.select_one(".result__snippet") or
                        result.select_one(".result__body")
                    )

                    snippet = snippet_elem.get_text().strip() if snippet_elem else ""

                    results.append({
                        "title": title,
                        "url": url,
                        "snippet": snippet
                    })

            # If we still don't have results, save the HTML for debugging
            if not results:
                print("No search results found. Saving HTML for debugging.")
                with open("search_debug_bs4.html", "w", encoding="utf-8") as f:
                    f.write(response.text)
                print("HTML saved to search_debug_bs4.html")

                # Add a message to the results
                results.append({
                    "title": "Search Error",
                    "url": url,
                    "snippet": "No search results could be extracted. The search engine may have changed its HTML structure or blocked the request."
                })

            return True, results

        except Exception as e:
            error_message = f"Error searching: {e}"
            print(f"BeautifulSoup search error: {e}")
            self.history[-1]["error"] = error_message

            # Return a message about the error
            return True, [{
                "title": "Search Error",
                "url": "",
                "snippet": f"An error occurred while searching: {e}"
            }]

    def get_history(self) -> List[Dict[str, str]]:
        """Get the browser history.

        Returns:
            The browser history.
        """
        return self.history
