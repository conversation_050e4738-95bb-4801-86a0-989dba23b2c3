"""
Suggestion cache for the Advanced AI Agent.
"""

import time
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Tuple, Callable, Set, Generic, TypeVar
from collections import OrderedDict

# Set up logging
logger = logging.getLogger(__name__)

# Type variable for generic suggestion type
T = TypeVar('T')

class SuggestionCache(Generic[T]):
    """Cache for storing and retrieving suggestions."""
    
    def __init__(self, max_size: int = 1000, ttl: float = 300.0):
        """Initialize the suggestion cache.
        
        Args:
            max_size: Maximum number of items to store in the cache.
            ttl: Time-to-live for cache items in seconds.
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache: OrderedDict[str, Tuple[T, float, float]] = OrderedDict()
        self.lock = threading.RLock()
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.running = False
        
    def start(self):
        """Start the cache cleanup thread."""
        with self.lock:
            if not self.running:
                self.running = True
                self.cleanup_thread.start()
                logger.debug("Suggestion cache cleanup thread started")
    
    def stop(self):
        """Stop the cache cleanup thread."""
        with self.lock:
            self.running = False
            logger.debug("Suggestion cache cleanup thread stopped")
    
    def put(self, key: str, value: T, relevance: float = 1.0):
        """Add an item to the cache.
        
        Args:
            key: Cache key.
            value: Value to store.
            relevance: Relevance score (0.0 to 1.0).
        """
        with self.lock:
            # Remove oldest item if cache is full
            if len(self.cache) >= self.max_size and key not in self.cache:
                self.cache.popitem(last=False)
            
            # Add or update item
            self.cache[key] = (value, relevance, time.time() + self.ttl)
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
    
    def get(self, key: str) -> Optional[T]:
        """Get an item from the cache.
        
        Args:
            key: Cache key.
            
        Returns:
            Cached value, or None if not found or expired.
        """
        with self.lock:
            if key not in self.cache:
                return None
            
            value, relevance, expiry = self.cache[key]
            
            # Check if expired
            if time.time() > expiry:
                del self.cache[key]
                return None
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            
            return value
    
    def get_with_relevance(self, key: str) -> Optional[Tuple[T, float]]:
        """Get an item and its relevance from the cache.
        
        Args:
            key: Cache key.
            
        Returns:
            Tuple of (value, relevance), or None if not found or expired.
        """
        with self.lock:
            if key not in self.cache:
                return None
            
            value, relevance, expiry = self.cache[key]
            
            # Check if expired
            if time.time() > expiry:
                del self.cache[key]
                return None
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            
            return (value, relevance)
    
    def remove(self, key: str):
        """Remove an item from the cache.
        
        Args:
            key: Cache key.
        """
        with self.lock:
            if key in self.cache:
                del self.cache[key]
    
    def clear(self):
        """Clear the cache."""
        with self.lock:
            self.cache.clear()
    
    def get_all(self) -> Dict[str, T]:
        """Get all non-expired items in the cache.
        
        Returns:
            Dictionary mapping keys to values.
        """
        with self.lock:
            current_time = time.time()
            return {
                key: value
                for key, (value, _, expiry) in self.cache.items()
                if current_time <= expiry
            }
    
    def get_all_with_relevance(self) -> Dict[str, Tuple[T, float]]:
        """Get all non-expired items with relevance scores.
        
        Returns:
            Dictionary mapping keys to (value, relevance) tuples.
        """
        with self.lock:
            current_time = time.time()
            return {
                key: (value, relevance)
                for key, (value, relevance, expiry) in self.cache.items()
                if current_time <= expiry
            }
    
    def _cleanup_loop(self):
        """Background thread to clean up expired items."""
        while self.running:
            try:
                self._cleanup_expired()
                time.sleep(60.0)  # Check every minute
            except Exception as e:
                logger.error(f"Error in cache cleanup: {e}", exc_info=True)
    
    def _cleanup_expired(self):
        """Remove expired items from the cache."""
        with self.lock:
            current_time = time.time()
            keys_to_remove = [
                key for key, (_, _, expiry) in self.cache.items()
                if current_time > expiry
            ]
            
            for key in keys_to_remove:
                del self.cache[key]
            
            if keys_to_remove:
                logger.debug(f"Removed {len(keys_to_remove)} expired items from suggestion cache")
