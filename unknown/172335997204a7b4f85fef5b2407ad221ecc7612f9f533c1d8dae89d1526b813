"""
Code understanding for the Advanced AI Agent.
"""

import re
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Set

# Set up logging
logger = logging.getLogger(__name__)

class CodeUnderstanding:
    """Analyzer for understanding and explaining code."""
    
    def __init__(self, model_manager: Any):
        """Initialize the code understanding analyzer.
        
        Args:
            model_manager: Model manager for AI-powered code analysis.
        """
        self.model_manager = model_manager
        self.lock = threading.RLock()
        
        # File extension to language mapping
        self.extension_to_language = {
            ".py": "python",
            ".js": "javascript",
            ".jsx": "javascript",
            ".ts": "typescript",
            ".tsx": "typescript",
            ".java": "java",
            ".c": "c",
            ".cpp": "cpp",
            ".cs": "csharp",
            ".go": "go",
            ".rs": "rust",
            ".php": "php",
            ".rb": "ruby",
            ".swift": "swift",
            ".kt": "kotlin",
            ".sql": "sql",
            ".html": "html",
            ".css": "css",
        }
    
    def explain_code(self, 
                    code: str, 
                    language: Optional[str] = None,
                    detail_level: str = "medium") -> str:
        """Explain code in natural language.
        
        Args:
            code: Code to explain.
            language: Programming language of the code. If None, will be detected.
            detail_level: Level of detail in the explanation ("low", "medium", "high").
            
        Returns:
            Natural language explanation of the code.
        """
        # Detect language if not provided
        if language is None:
            language = self._detect_language(code)
        
        try:
            # Create a prompt for the model
            prompt = f"""
You are an expert {language} programmer. Explain the following {language} code in natural language.
Provide a {detail_level} level of detail in your explanation.

Code:
```{language}
{code}
```

Explanation:
"""
            
            # Generate explanation
            response = self.model_manager.generate(prompt=prompt)
            
            return response.strip()
        except Exception as e:
            logger.error(f"Error explaining code: {e}", exc_info=True)
            return f"Error explaining code: {e}"
    
    def analyze_complexity(self, code: str, language: Optional[str] = None) -> Dict[str, Any]:
        """Analyze the complexity of code.
        
        Args:
            code: Code to analyze.
            language: Programming language of the code. If None, will be detected.
            
        Returns:
            Dictionary with complexity analysis.
        """
        # Detect language if not provided
        if language is None:
            language = self._detect_language(code)
        
        try:
            # Create a prompt for the model
            prompt = f"""
You are an expert {language} programmer. Analyze the complexity of the following {language} code.
Provide the time complexity (Big O notation) and space complexity, as well as any potential performance issues.
Format your response as JSON with the following fields:
- time_complexity: The time complexity in Big O notation
- space_complexity: The space complexity in Big O notation
- performance_issues: Array of potential performance issues
- suggestions: Array of suggestions for improvement

Code:
```{language}
{code}
```

Complexity analysis (JSON):
"""
            
            # Generate analysis
            response = self.model_manager.generate(prompt=prompt)
            
            # Parse JSON response
            import json
            try:
                # Extract JSON from response
                json_str = response.strip()
                if "```json" in json_str:
                    json_str = json_str.split("```json")[1].split("```")[0].strip()
                elif "```" in json_str:
                    json_str = json_str.split("```")[1].split("```")[0].strip()
                
                analysis = json.loads(json_str)
                return analysis
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse JSON from model response: {response}")
                
                # Fallback: extract information using regex
                time_complexity = re.search(r"[Tt]ime [Cc]omplexity:?\s*(O\([^)]+\))", response)
                space_complexity = re.search(r"[Ss]pace [Cc]omplexity:?\s*(O\([^)]+\))", response)
                
                return {
                    "time_complexity": time_complexity.group(1) if time_complexity else "Unknown",
                    "space_complexity": space_complexity.group(1) if space_complexity else "Unknown",
                    "performance_issues": [],
                    "suggestions": [],
                    "raw_response": response
                }
        except Exception as e:
            logger.error(f"Error analyzing complexity: {e}", exc_info=True)
            return {
                "time_complexity": "Error",
                "space_complexity": "Error",
                "performance_issues": [f"Error analyzing complexity: {e}"],
                "suggestions": [],
                "error": str(e)
            }
    
    def identify_patterns(self, code: str, language: Optional[str] = None) -> Dict[str, List[str]]:
        """Identify design patterns and anti-patterns in code.
        
        Args:
            code: Code to analyze.
            language: Programming language of the code. If None, will be detected.
            
        Returns:
            Dictionary with identified patterns and anti-patterns.
        """
        # Detect language if not provided
        if language is None:
            language = self._detect_language(code)
        
        try:
            # Create a prompt for the model
            prompt = f"""
You are an expert {language} programmer. Identify design patterns and anti-patterns in the following {language} code.
Format your response as JSON with the following fields:
- design_patterns: Array of identified design patterns with names and descriptions
- anti_patterns: Array of identified anti-patterns with names and descriptions
- suggestions: Array of suggestions for improvement

Code:
```{language}
{code}
```

Pattern analysis (JSON):
"""
            
            # Generate analysis
            response = self.model_manager.generate(prompt=prompt)
            
            # Parse JSON response
            import json
            try:
                # Extract JSON from response
                json_str = response.strip()
                if "```json" in json_str:
                    json_str = json_str.split("```json")[1].split("```")[0].strip()
                elif "```" in json_str:
                    json_str = json_str.split("```")[1].split("```")[0].strip()
                
                analysis = json.loads(json_str)
                return analysis
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse JSON from model response: {response}")
                
                # Fallback: extract information using regex
                design_patterns = re.findall(r"Design Pattern[s]?:?\s*([^:\n]+)", response)
                anti_patterns = re.findall(r"Anti-Pattern[s]?:?\s*([^:\n]+)", response)
                
                return {
                    "design_patterns": design_patterns if design_patterns else [],
                    "anti_patterns": anti_patterns if anti_patterns else [],
                    "suggestions": [],
                    "raw_response": response
                }
        except Exception as e:
            logger.error(f"Error identifying patterns: {e}", exc_info=True)
            return {
                "design_patterns": [],
                "anti_patterns": [f"Error identifying patterns: {e}"],
                "suggestions": [],
                "error": str(e)
            }
    
    def extract_dependencies(self, code: str, language: Optional[str] = None) -> Dict[str, List[str]]:
        """Extract dependencies and imports from code.
        
        Args:
            code: Code to analyze.
            language: Programming language of the code. If None, will be detected.
            
        Returns:
            Dictionary with extracted dependencies.
        """
        # Detect language if not provided
        if language is None:
            language = self._detect_language(code)
        
        # Define regex patterns for different languages
        patterns = {
            "python": r"^\s*(?:from\s+(\S+)\s+)?import\s+(\S+)(?:\s+as\s+(\S+))?",
            "javascript": r"^\s*import\s+(?:\{\s*([^}]+)\s*\}|\*\s+as\s+(\S+)|(\S+))\s+from\s+['\"]([^'\"]+)['\"]",
            "typescript": r"^\s*import\s+(?:\{\s*([^}]+)\s*\}|\*\s+as\s+(\S+)|(\S+))\s+from\s+['\"]([^'\"]+)['\"]",
            "java": r"^\s*import\s+([^;]+);",
            "csharp": r"^\s*using\s+([^;]+);",
            "go": r"^\s*import\s+(?:\(\s*|\s*)(?:['\"]([^'\"]+)['\"])?",
            "rust": r"^\s*(?:use|extern\s+crate)\s+([^;]+);",
        }
        
        # Get the appropriate pattern
        pattern = patterns.get(language)
        if not pattern:
            # Use the model for languages without patterns
            return self._extract_dependencies_with_model(code, language)
        
        # Extract dependencies using regex
        dependencies = []
        imports = []
        
        for line in code.splitlines():
            match = re.match(pattern, line)
            if match:
                if language == "python":
                    # Handle Python imports
                    module = match.group(1)
                    name = match.group(2)
                    alias = match.group(3)
                    
                    if module:
                        dependencies.append(module)
                        imports.append(f"from {module} import {name}" + (f" as {alias}" if alias else ""))
                    else:
                        dependencies.append(name)
                        imports.append(f"import {name}" + (f" as {alias}" if alias else ""))
                elif language in ["javascript", "typescript"]:
                    # Handle JS/TS imports
                    named_imports = match.group(1)
                    namespace_import = match.group(2)
                    default_import = match.group(3)
                    module_path = match.group(4)
                    
                    dependencies.append(module_path)
                    if named_imports:
                        imports.append(f"import {{ {named_imports} }} from '{module_path}'")
                    elif namespace_import:
                        imports.append(f"import * as {namespace_import} from '{module_path}'")
                    elif default_import:
                        imports.append(f"import {default_import} from '{module_path}'")
                else:
                    # Handle other languages
                    dep = match.group(1)
                    if dep:
                        dependencies.append(dep)
                        imports.append(line.strip())
        
        return {
            "dependencies": list(set(dependencies)),
            "imports": imports
        }
    
    def _extract_dependencies_with_model(self, code: str, language: str) -> Dict[str, List[str]]:
        """Extract dependencies using the model.
        
        Args:
            code: Code to analyze.
            language: Programming language of the code.
            
        Returns:
            Dictionary with extracted dependencies.
        """
        try:
            # Create a prompt for the model
            prompt = f"""
You are an expert {language} programmer. Extract all dependencies and imports from the following {language} code.
Format your response as JSON with the following fields:
- dependencies: Array of dependency names or packages
- imports: Array of import statements

Code:
```{language}
{code}
```

Dependencies (JSON):
"""
            
            # Generate analysis
            response = self.model_manager.generate(prompt=prompt)
            
            # Parse JSON response
            import json
            try:
                # Extract JSON from response
                json_str = response.strip()
                if "```json" in json_str:
                    json_str = json_str.split("```json")[1].split("```")[0].strip()
                elif "```" in json_str:
                    json_str = json_str.split("```")[1].split("```")[0].strip()
                
                analysis = json.loads(json_str)
                return analysis
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse JSON from model response: {response}")
                return {
                    "dependencies": [],
                    "imports": [],
                    "raw_response": response
                }
        except Exception as e:
            logger.error(f"Error extracting dependencies: {e}", exc_info=True)
            return {
                "dependencies": [],
                "imports": [],
                "error": str(e)
            }
    
    def _detect_language(self, code: str) -> str:
        """Detect the programming language of code.
        
        Args:
            code: Code to analyze.
            
        Returns:
            Detected programming language.
        """
        # Check for language-specific patterns
        if re.search(r"^\s*import\s+|^\s*from\s+\w+\s+import\s+|def\s+\w+\s*\(|class\s+\w+\s*:", code, re.MULTILINE):
            return "python"
        elif re.search(r"^\s*import\s+.*from\s+|export\s+|const\s+\w+\s*=|let\s+\w+\s*=|var\s+\w+\s*=", code, re.MULTILINE):
            # Check for TypeScript-specific features
            if re.search(r":\s*\w+\[\]|interface\s+\w+|<\w+>", code):
                return "typescript"
            return "javascript"
        elif re.search(r"public\s+class|private\s+\w+|protected\s+\w+|package\s+\w+|import\s+java\.", code, re.MULTILINE):
            return "java"
        elif re.search(r"#include\s+<\w+\.h>|int\s+main\s*\(|void\s+\w+\s*\(", code, re.MULTILINE):
            # Check for C++ specific features
            if re.search(r"std::|namespace\s+\w+|template\s*<|class\s+\w+\s*:", code):
                return "cpp"
            return "c"
        elif re.search(r"using\s+System|namespace\s+\w+|public\s+class\s+\w+\s*:", code, re.MULTILINE):
            return "csharp"
        elif re.search(r"func\s+\w+\s*\(|package\s+main|import\s+\(", code, re.MULTILINE):
            return "go"
        elif re.search(r"fn\s+\w+\s*\(|let\s+mut\s+\w+|impl\s+\w+", code, re.MULTILINE):
            return "rust"
        elif re.search(r"<\?php|\$\w+\s*=|function\s+\w+\s*\(", code, re.MULTILINE):
            return "php"
        elif re.search(r"def\s+\w+|end\b|require\s+|class\s+\w+\s*<", code, re.MULTILINE):
            return "ruby"
        elif re.search(r"func\s+\w+\s*\(|var\s+\w+\s*:|let\s+\w+\s*:", code, re.MULTILINE):
            return "swift"
        elif re.search(r"fun\s+\w+\s*\(|val\s+\w+\s*:|var\s+\w+\s*:", code, re.MULTILINE):
            return "kotlin"
        elif re.search(r"SELECT\s+|FROM\s+|WHERE\s+|INSERT\s+INTO|CREATE\s+TABLE", code, re.IGNORECASE):
            return "sql"
        elif re.search(r"<html|<div|<p>|<body|<head|<script", code, re.IGNORECASE):
            return "html"
        elif re.search(r"\.\w+\s*{|#\w+\s*{|@media|margin:|padding:", code, re.MULTILINE):
            return "css"
        
        # Default to Python if no language detected
        return "python"
