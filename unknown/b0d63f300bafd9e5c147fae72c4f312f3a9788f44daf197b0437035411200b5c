"""
Optimized Search Engine for the Advanced AI Agent.
Provides fast, multi-backend search with intelligent caching and result optimization.
"""

import asyncio
import aiohttp
import time
import json
import hashlib
import logging
import threading
from typing import Dict, List, Optional, Any, Tuple, Callable, Union
from dataclasses import dataclass, asdict
from pathlib import Path
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import urllib.parse
import requests
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Search result data structure."""
    title: str
    url: str
    snippet: str
    source: str
    relevance_score: float
    timestamp: float
    metadata: Dict[str, Any]

@dataclass
class SearchQuery:
    """Search query data structure."""
    query: str
    max_results: int
    timeout: float
    filters: Dict[str, Any]
    preferences: Dict[str, Any]

class SearchBackend:
    """Base class for search backends."""
    
    def __init__(self, name: str, timeout: float = 5.0):
        self.name = name
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        })
    
    async def search_async(self, query: str, max_results: int = 5) -> List[SearchResult]:
        """Async search method to be implemented by subclasses."""
        raise NotImplementedError
    
    def search_sync(self, query: str, max_results: int = 5) -> List[SearchResult]:
        """Synchronous search method."""
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.search_async(query, max_results))
        except RuntimeError:
            # Create new event loop if none exists
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.search_async(query, max_results))
            finally:
                loop.close()

class DuckDuckGoBackend(SearchBackend):
    """DuckDuckGo search backend."""
    
    def __init__(self):
        super().__init__("duckduckgo", timeout=3.0)
        self.api_url = "https://api.duckduckgo.com/"
    
    async def search_async(self, query: str, max_results: int = 5) -> List[SearchResult]:
        """Search using DuckDuckGo API."""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                params = {
                    "q": query,
                    "format": "json",
                    "no_html": "1",
                    "skip_disambig": "1"
                }
                
                async with session.get(self.api_url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = []
                        
                        # Process instant answer
                        if data.get("AbstractText"):
                            results.append(SearchResult(
                                title=data.get("Heading", query),
                                url=data.get("AbstractURL", ""),
                                snippet=data.get("AbstractText", ""),
                                source="duckduckgo",
                                relevance_score=0.9,
                                timestamp=time.time(),
                                metadata={"type": "instant_answer"}
                            ))
                        
                        # Process related topics
                        for topic in data.get("RelatedTopics", [])[:max_results-len(results)]:
                            if isinstance(topic, dict) and "Text" in topic:
                                results.append(SearchResult(
                                    title=topic.get("Text", "").split(" - ")[0],
                                    url=topic.get("FirstURL", ""),
                                    snippet=topic.get("Text", ""),
                                    source="duckduckgo",
                                    relevance_score=0.7,
                                    timestamp=time.time(),
                                    metadata={"type": "related_topic"}
                                ))
                        
                        return results[:max_results]
        except Exception as e:
            logger.debug(f"DuckDuckGo search failed: {e}")
        
        return []

class WikipediaBackend(SearchBackend):
    """Wikipedia search backend."""
    
    def __init__(self):
        super().__init__("wikipedia", timeout=4.0)
        self.api_url = "https://en.wikipedia.org/api/rest_v1/page/summary/"
        self.search_url = "https://en.wikipedia.org/w/api.php"
    
    async def search_async(self, query: str, max_results: int = 5) -> List[SearchResult]:
        """Search using Wikipedia API."""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                # First, search for pages
                search_params = {
                    "action": "query",
                    "format": "json",
                    "list": "search",
                    "srsearch": query,
                    "srlimit": max_results
                }
                
                async with session.get(self.search_url, params=search_params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = []
                        
                        for page in data.get("query", {}).get("search", []):
                            title = page.get("title", "")
                            snippet = page.get("snippet", "").replace("<span class=\"searchmatch\">", "").replace("</span>", "")
                            
                            results.append(SearchResult(
                                title=title,
                                url=f"https://en.wikipedia.org/wiki/{urllib.parse.quote(title)}",
                                snippet=snippet,
                                source="wikipedia",
                                relevance_score=0.8,
                                timestamp=time.time(),
                                metadata={"page_id": page.get("pageid")}
                            ))
                        
                        return results
        except Exception as e:
            logger.debug(f"Wikipedia search failed: {e}")
        
        return []

class BingBackend(SearchBackend):
    """Bing search backend (requires API key)."""
    
    def __init__(self, api_key: Optional[str] = None):
        super().__init__("bing", timeout=5.0)
        self.api_key = api_key
        self.api_url = "https://api.bing.microsoft.com/v7.0/search"
    
    async def search_async(self, query: str, max_results: int = 5) -> List[SearchResult]:
        """Search using Bing API."""
        if not self.api_key:
            return []
        
        try:
            headers = {"Ocp-Apim-Subscription-Key": self.api_key}
            params = {"q": query, "count": max_results}
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(self.api_url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = []
                        
                        for item in data.get("webPages", {}).get("value", []):
                            results.append(SearchResult(
                                title=item.get("name", ""),
                                url=item.get("url", ""),
                                snippet=item.get("snippet", ""),
                                source="bing",
                                relevance_score=0.85,
                                timestamp=time.time(),
                                metadata={"display_url": item.get("displayUrl")}
                            ))
                        
                        return results
        except Exception as e:
            logger.debug(f"Bing search failed: {e}")
        
        return []

class OptimizedSearchEngine:
    """Optimized search engine with multiple backends and intelligent caching."""
    
    def __init__(self, cache_dir: Optional[Path] = None, max_cache_size: int = 1000):
        """Initialize the optimized search engine.
        
        Args:
            cache_dir: Directory for caching search results
            max_cache_size: Maximum number of cached queries
        """
        self.cache_dir = cache_dir or Path.home() / ".ai_agent_cache" / "search"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_cache_size = max_cache_size
        
        # Initialize backends
        self.backends = [
            DuckDuckGoBackend(),
            WikipediaBackend(),
            # BingBackend()  # Add API key if available
        ]
        
        # Cache for search results
        self.result_cache: Dict[str, Tuple[List[SearchResult], float]] = {}
        self.cache_ttl = 3600  # 1 hour
        
        # Performance tracking
        self.search_stats = {
            "total_searches": 0,
            "cache_hits": 0,
            "backend_successes": {},
            "average_response_time": 0.0
        }
        
        # Thread pool for concurrent searches
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Lock for thread safety
        self.lock = threading.RLock()
    
    def search(self, query: str, max_results: int = 5, use_cache: bool = True, timeout: float = 5.0) -> List[SearchResult]:
        """Search with multiple backends and intelligent result merging.
        
        Args:
            query: Search query
            max_results: Maximum number of results
            use_cache: Whether to use cached results
            timeout: Maximum time to wait for results
            
        Returns:
            List of search results
        """
        start_time = time.time()
        
        with self.lock:
            self.search_stats["total_searches"] += 1
        
        # Check cache first
        if use_cache:
            cached_results = self._get_from_cache(query)
            if cached_results:
                with self.lock:
                    self.search_stats["cache_hits"] += 1
                return cached_results[:max_results]
        
        # Search with multiple backends concurrently
        all_results = []
        futures = []
        
        for backend in self.backends:
            future = self.executor.submit(self._search_with_backend, backend, query, max_results)
            futures.append((backend.name, future))
        
        # Collect results with timeout
        for backend_name, future in futures:
            try:
                results = future.result(timeout=timeout / len(self.backends))
                if results:
                    all_results.extend(results)
                    with self.lock:
                        self.search_stats["backend_successes"][backend_name] = \
                            self.search_stats["backend_successes"].get(backend_name, 0) + 1
            except Exception as e:
                logger.debug(f"Backend {backend_name} failed: {e}")
        
        # Merge and rank results
        merged_results = self._merge_and_rank_results(all_results, query)
        final_results = merged_results[:max_results]
        
        # Cache results
        if use_cache and final_results:
            self._save_to_cache(query, final_results)
        
        # Update performance stats
        response_time = time.time() - start_time
        with self.lock:
            current_avg = self.search_stats["average_response_time"]
            total_searches = self.search_stats["total_searches"]
            self.search_stats["average_response_time"] = \
                (current_avg * (total_searches - 1) + response_time) / total_searches
        
        return final_results
    
    def _search_with_backend(self, backend: SearchBackend, query: str, max_results: int) -> List[SearchResult]:
        """Search with a specific backend."""
        try:
            return backend.search_sync(query, max_results)
        except Exception as e:
            logger.debug(f"Backend {backend.name} search failed: {e}")
            return []
    
    def _merge_and_rank_results(self, results: List[SearchResult], query: str) -> List[SearchResult]:
        """Merge and rank results from multiple backends."""
        if not results:
            return []
        
        # Remove duplicates based on URL
        seen_urls = set()
        unique_results = []
        
        for result in results:
            if result.url not in seen_urls:
                seen_urls.add(result.url)
                unique_results.append(result)
        
        # Calculate relevance scores
        query_terms = set(query.lower().split())
        
        for result in unique_results:
            # Base score from backend
            score = result.relevance_score
            
            # Boost score based on title relevance
            title_terms = set(result.title.lower().split())
            title_overlap = len(query_terms.intersection(title_terms)) / len(query_terms)
            score += title_overlap * 0.3
            
            # Boost score based on snippet relevance
            snippet_terms = set(result.snippet.lower().split())
            snippet_overlap = len(query_terms.intersection(snippet_terms)) / len(query_terms)
            score += snippet_overlap * 0.2
            
            # Boost score for certain sources
            source_boost = {
                "wikipedia": 0.1,
                "duckduckgo": 0.05,
                "bing": 0.15
            }
            score += source_boost.get(result.source, 0)
            
            result.relevance_score = min(score, 1.0)
        
        # Sort by relevance score
        return sorted(unique_results, key=lambda x: x.relevance_score, reverse=True)
    
    def _get_cache_key(self, query: str) -> str:
        """Generate cache key for query."""
        return hashlib.md5(query.lower().encode()).hexdigest()
    
    def _get_from_cache(self, query: str) -> Optional[List[SearchResult]]:
        """Get results from cache."""
        cache_key = self._get_cache_key(query)
        
        if cache_key in self.result_cache:
            results, timestamp = self.result_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                return results
            else:
                # Remove expired entry
                del self.result_cache[cache_key]
        
        return None
    
    def _save_to_cache(self, query: str, results: List[SearchResult]):
        """Save results to cache."""
        cache_key = self._get_cache_key(query)
        
        # Enforce cache size limit
        if len(self.result_cache) >= self.max_cache_size:
            # Remove oldest entry
            oldest_key = min(self.result_cache.keys(), 
                           key=lambda k: self.result_cache[k][1])
            del self.result_cache[oldest_key]
        
        self.result_cache[cache_key] = (results, time.time())
    
    def get_stats(self) -> Dict[str, Any]:
        """Get search engine statistics."""
        with self.lock:
            return dict(self.search_stats)
    
    def clear_cache(self):
        """Clear the search cache."""
        with self.lock:
            self.result_cache.clear()
    
    def shutdown(self):
        """Shutdown the search engine."""
        self.executor.shutdown(wait=True)
