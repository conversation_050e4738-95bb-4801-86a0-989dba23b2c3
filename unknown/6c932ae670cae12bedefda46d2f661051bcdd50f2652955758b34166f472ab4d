"""
Shell command executor for the Advanced AI Agent.
This script allows you to execute shell commands directly using the ShellTool.
"""

import os
import sys
from pathlib import Path

# Add the current directory to the path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the ShellTool
from tools.shell import ShellTool

def execute_command(command, workspace_dir=None):
    """
    Execute a shell command using the ShellTool.
    
    Args:
        command: The command to execute.
        workspace_dir: The workspace directory to use. If None, will use the current directory.
        
    Returns:
        The result of the command execution.
    """
    # Initialize the shell tool
    shell_tool = ShellTool(workspace_dir=workspace_dir)
    
    # Execute the command
    try:
        stdout, stderr, return_code = shell_tool.execute(command)
        
        # Print the results
        print("\n--- Command Execution Results ---")
        print(f"Command: {command}")
        print(f"Return Code: {return_code}")
        
        if stdout:
            print("\n--- Standard Output ---")
            print(stdout)
        
        if stderr:
            print("\n--- Standard Error ---")
            print(stderr)
            
        return stdout, stderr, return_code
    
    except Exception as e:
        print(f"Error executing command: {e}")
        return None, str(e), 1

if __name__ == "__main__":
    # Check if a command was provided
    if len(sys.argv) > 1:
        # Join all arguments as the command
        command = " ".join(sys.argv[1:])
    else:
        # If no command was provided, ask for one
        command = input("Enter the command to execute: ")
    
    # Execute the command
    execute_command(command)
