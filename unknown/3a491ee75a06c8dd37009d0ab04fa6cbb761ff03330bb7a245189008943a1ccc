"""
Multi-Language Processor for the AI Code Assistant.

This module implements enhanced multi-language support for Python, JavaScript,
Java, C++, Go, Rust, and other programming languages with unified processing.
"""

import os
import json
import time
import logging
import threading
import subprocess
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import ast
import re
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

@dataclass
class LanguageFeatures:
    """Represents language-specific features and capabilities."""
    name: str
    extensions: List[str]
    compilation_required: bool
    interpreter_command: List[str]
    compiler_command: Optional[List[str]]
    package_manager: Optional[str]
    test_framework: Optional[str]
    linter: Optional[str]
    formatter: Optional[str]
    documentation_tool: Optional[str]
    dependency_file: Optional[str]

@dataclass
class CodeAnalysisResult:
    """Result of code analysis for any language."""
    language: str
    syntax_valid: bool
    complexity_score: float
    maintainability_score: float
    performance_hints: List[str]
    security_issues: List[str]
    style_violations: List[str]
    dependencies: List[str]
    exports: List[str]
    functions: List[Dict[str, Any]]
    classes: List[Dict[str, Any]]
    variables: List[Dict[str, Any]]

@dataclass
class ExecutionResult:
    """Result of code execution for any language."""
    language: str
    success: bool
    output: str
    errors: List[str]
    warnings: List[str]
    execution_time: float
    memory_usage: Optional[float]
    exit_code: int

class LanguageProcessor(ABC):
    """Abstract base class for language processors."""
    
    @abstractmethod
    def analyze_code(self, code: str, file_path: Optional[str] = None) -> CodeAnalysisResult:
        """Analyze code and return analysis results."""
        pass
    
    @abstractmethod
    def execute_code(self, code: str, timeout: Optional[float] = None) -> ExecutionResult:
        """Execute code and return execution results."""
        pass
    
    @abstractmethod
    def format_code(self, code: str) -> str:
        """Format code according to language standards."""
        pass
    
    @abstractmethod
    def lint_code(self, code: str) -> List[str]:
        """Lint code and return issues."""
        pass

class PythonProcessor(LanguageProcessor):
    """Python language processor."""
    
    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir
        self.features = LanguageFeatures(
            name="python",
            extensions=[".py"],
            compilation_required=False,
            interpreter_command=["python"],
            compiler_command=None,
            package_manager="pip",
            test_framework="pytest",
            linter="pylint",
            formatter="black",
            documentation_tool="sphinx",
            dependency_file="requirements.txt"
        )
    
    def analyze_code(self, code: str, file_path: Optional[str] = None) -> CodeAnalysisResult:
        """Analyze Python code."""
        result = CodeAnalysisResult(
            language="python",
            syntax_valid=True,
            complexity_score=0.0,
            maintainability_score=0.0,
            performance_hints=[],
            security_issues=[],
            style_violations=[],
            dependencies=[],
            exports=[],
            functions=[],
            classes=[],
            variables=[]
        )
        
        try:
            tree = ast.parse(code)
            result.syntax_valid = True
            
            # Analyze AST
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_info = {
                        "name": node.name,
                        "line": node.lineno,
                        "args": [arg.arg for arg in node.args.args],
                        "docstring": ast.get_docstring(node),
                        "complexity": self._calculate_function_complexity(node)
                    }
                    result.functions.append(func_info)
                
                elif isinstance(node, ast.ClassDef):
                    class_info = {
                        "name": node.name,
                        "line": node.lineno,
                        "methods": [],
                        "docstring": ast.get_docstring(node),
                        "bases": [base.id for base in node.bases if isinstance(base, ast.Name)]
                    }
                    result.classes.append(class_info)
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            result.dependencies.append(alias.name)
                    else:
                        module = node.module or ""
                        for alias in node.names:
                            result.dependencies.append(f"{module}.{alias.name}" if module else alias.name)
            
            # Calculate metrics
            result.complexity_score = self._calculate_complexity(tree)
            result.maintainability_score = self._calculate_maintainability(code, tree)
            
            # Check for performance issues
            result.performance_hints = self._check_performance_issues(tree)
            
            # Check for security issues
            result.security_issues = self._check_security_issues(tree)
            
        except SyntaxError as e:
            result.syntax_valid = False
            result.security_issues.append(f"Syntax error: {e}")
        
        return result
    
    def execute_code(self, code: str, timeout: Optional[float] = None) -> ExecutionResult:
        """Execute Python code."""
        start_time = time.time()
        
        try:
            # Create temporary file
            temp_file = self.workspace_dir / f"temp_python_{int(time.time())}.py"
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(code)
            
            # Execute
            process = subprocess.run(
                ["python", str(temp_file)],
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.workspace_dir
            )
            
            execution_time = time.time() - start_time
            
            result = ExecutionResult(
                language="python",
                success=process.returncode == 0,
                output=process.stdout,
                errors=process.stderr.splitlines() if process.stderr else [],
                warnings=[],
                execution_time=execution_time,
                memory_usage=None,
                exit_code=process.returncode
            )
            
            # Clean up
            if temp_file.exists():
                temp_file.unlink()
            
            return result
            
        except subprocess.TimeoutExpired:
            return ExecutionResult(
                language="python",
                success=False,
                output="",
                errors=["Execution timed out"],
                warnings=[],
                execution_time=timeout or 0,
                memory_usage=None,
                exit_code=-1
            )
        except Exception as e:
            return ExecutionResult(
                language="python",
                success=False,
                output="",
                errors=[str(e)],
                warnings=[],
                execution_time=time.time() - start_time,
                memory_usage=None,
                exit_code=-1
            )
    
    def format_code(self, code: str) -> str:
        """Format Python code using black."""
        try:
            # Simple formatting - in production, use black
            lines = code.splitlines()
            formatted_lines = []
            
            for line in lines:
                # Remove trailing whitespace
                formatted_line = line.rstrip()
                formatted_lines.append(formatted_line)
            
            return '\n'.join(formatted_lines)
        except Exception:
            return code
    
    def lint_code(self, code: str) -> List[str]:
        """Lint Python code."""
        issues = []
        
        try:
            tree = ast.parse(code)
            
            # Simple linting rules
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    if not ast.get_docstring(node):
                        issues.append(f"Function '{node.name}' at line {node.lineno} lacks docstring")
                    
                    if len(node.args.args) > 5:
                        issues.append(f"Function '{node.name}' at line {node.lineno} has too many parameters")
                
                elif isinstance(node, ast.ClassDef):
                    if not ast.get_docstring(node):
                        issues.append(f"Class '{node.name}' at line {node.lineno} lacks docstring")
        
        except SyntaxError as e:
            issues.append(f"Syntax error: {e}")
        
        return issues
    
    def _calculate_function_complexity(self, node: ast.FunctionDef) -> int:
        """Calculate cyclomatic complexity of a function."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def _calculate_complexity(self, tree: ast.AST) -> float:
        """Calculate overall code complexity."""
        total_complexity = 0
        function_count = 0
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                total_complexity += self._calculate_function_complexity(node)
                function_count += 1
        
        return total_complexity / max(function_count, 1) / 10.0  # Normalize
    
    def _calculate_maintainability(self, code: str, tree: ast.AST) -> float:
        """Calculate maintainability score."""
        lines = code.splitlines()
        non_empty_lines = [line for line in lines if line.strip()]
        
        # Simple heuristics
        avg_line_length = sum(len(line) for line in non_empty_lines) / max(len(non_empty_lines), 1)
        comment_ratio = len([line for line in lines if line.strip().startswith("#")]) / max(len(lines), 1)
        
        # Prefer shorter lines and more comments
        length_score = max(0.0, 1.0 - (avg_line_length / 120))
        comment_score = min(comment_ratio * 2, 1.0)
        
        return (length_score + comment_score) / 2
    
    def _check_performance_issues(self, tree: ast.AST) -> List[str]:
        """Check for performance issues."""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.For):
                # Check for nested loops
                for child in ast.walk(node):
                    if isinstance(child, ast.For) and child != node:
                        issues.append(f"Nested loop detected at line {node.lineno} - consider optimization")
                        break
        
        return issues
    
    def _check_security_issues(self, tree: ast.AST) -> List[str]:
        """Check for security issues."""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    if node.func.id in ["eval", "exec"]:
                        issues.append(f"Dangerous function '{node.func.id}' used at line {node.lineno}")
        
        return issues

class JavaScriptProcessor(LanguageProcessor):
    """JavaScript language processor."""
    
    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir
        self.features = LanguageFeatures(
            name="javascript",
            extensions=[".js", ".jsx"],
            compilation_required=False,
            interpreter_command=["node"],
            compiler_command=None,
            package_manager="npm",
            test_framework="jest",
            linter="eslint",
            formatter="prettier",
            documentation_tool="jsdoc",
            dependency_file="package.json"
        )
    
    def analyze_code(self, code: str, file_path: Optional[str] = None) -> CodeAnalysisResult:
        """Analyze JavaScript code."""
        result = CodeAnalysisResult(
            language="javascript",
            syntax_valid=True,
            complexity_score=0.5,
            maintainability_score=0.5,
            performance_hints=[],
            security_issues=[],
            style_violations=[],
            dependencies=[],
            exports=[],
            functions=[],
            classes=[],
            variables=[]
        )
        
        # Simple regex-based analysis
        # In production, use a proper JavaScript parser
        
        # Find function declarations
        function_pattern = r'function\s+(\w+)\s*\([^)]*\)\s*\{'
        for match in re.finditer(function_pattern, code):
            line_number = code[:match.start()].count('\n') + 1
            result.functions.append({
                "name": match.group(1),
                "line": line_number,
                "type": "function"
            })
        
        # Find class declarations
        class_pattern = r'class\s+(\w+)\s*\{'
        for match in re.finditer(class_pattern, code):
            line_number = code[:match.start()].count('\n') + 1
            result.classes.append({
                "name": match.group(1),
                "line": line_number,
                "type": "class"
            })
        
        # Find imports
        import_patterns = [
            r'import\s+.*?\s+from\s+[\'"]([^\'"]+)[\'"]',
            r'require\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',
        ]
        
        for pattern in import_patterns:
            for match in re.finditer(pattern, code):
                result.dependencies.append(match.group(1))
        
        return result
    
    def execute_code(self, code: str, timeout: Optional[float] = None) -> ExecutionResult:
        """Execute JavaScript code."""
        start_time = time.time()
        
        try:
            # Create temporary file
            temp_file = self.workspace_dir / f"temp_js_{int(time.time())}.js"
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(code)
            
            # Execute
            process = subprocess.run(
                ["node", str(temp_file)],
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.workspace_dir
            )
            
            execution_time = time.time() - start_time
            
            result = ExecutionResult(
                language="javascript",
                success=process.returncode == 0,
                output=process.stdout,
                errors=process.stderr.splitlines() if process.stderr else [],
                warnings=[],
                execution_time=execution_time,
                memory_usage=None,
                exit_code=process.returncode
            )
            
            # Clean up
            if temp_file.exists():
                temp_file.unlink()
            
            return result
            
        except Exception as e:
            return ExecutionResult(
                language="javascript",
                success=False,
                output="",
                errors=[str(e)],
                warnings=[],
                execution_time=time.time() - start_time,
                memory_usage=None,
                exit_code=-1
            )
    
    def format_code(self, code: str) -> str:
        """Format JavaScript code."""
        # Simple formatting
        return code.strip()
    
    def lint_code(self, code: str) -> List[str]:
        """Lint JavaScript code."""
        issues = []
        
        # Simple linting rules
        if "eval(" in code:
            issues.append("Use of eval() is discouraged for security reasons")
        
        if "var " in code:
            issues.append("Consider using 'let' or 'const' instead of 'var'")
        
        return issues

class JavaProcessor(LanguageProcessor):
    """Java language processor (stub)."""
    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir
        self.features = LanguageFeatures(
            name="java",
            extensions=[".java"],
            compilation_required=True,
            interpreter_command=["java"],
            compiler_command=["javac"],
            package_manager="maven",
            test_framework="junit",
            linter="checkstyle",
            formatter="google-java-format",
            documentation_tool="javadoc",
            dependency_file="pom.xml"
        )
    def analyze_code(self, code: str, file_path: Optional[str] = None) -> CodeAnalysisResult:
        return CodeAnalysisResult(
            language="java",
            syntax_valid=True,
            complexity_score=0.5,
            maintainability_score=0.5,
            performance_hints=[],
            security_issues=[],
            style_violations=[],
            dependencies=[],
            exports=[],
            functions=[],
            classes=[],
            variables=[]
        )
    def execute_code(self, code: str, timeout: Optional[float] = None) -> ExecutionResult:
        return ExecutionResult(
            language="java",
            success=False,
            output="",
            errors=["Java execution not implemented yet"],
            warnings=[],
            execution_time=0.0,
            memory_usage=None,
            exit_code=-1
        )
    def format_code(self, code: str) -> str:
        return code
    def lint_code(self, code: str) -> List[str]:
        return ["Java linting not implemented yet"]

class CppProcessor(LanguageProcessor):
    """C++ language processor (stub)."""
    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir
        self.features = LanguageFeatures(
            name="cpp",
            extensions=[".cpp", ".hpp", ".cc", ".cxx"],
            compilation_required=True,
            interpreter_command=["./a.out"],
            compiler_command=["g++"],
            package_manager="conan",
            test_framework="gtest",
            linter="cpplint",
            formatter="clang-format",
            documentation_tool="doxygen",
            dependency_file="conanfile.txt"
        )
    def analyze_code(self, code: str, file_path: Optional[str] = None) -> CodeAnalysisResult:
        return CodeAnalysisResult(
            language="cpp",
            syntax_valid=True,
            complexity_score=0.5,
            maintainability_score=0.5,
            performance_hints=[],
            security_issues=[],
            style_violations=[],
            dependencies=[],
            exports=[],
            functions=[],
            classes=[],
            variables=[]
        )
    def execute_code(self, code: str, timeout: Optional[float] = None) -> ExecutionResult:
        return ExecutionResult(
            language="cpp",
            success=False,
            output="",
            errors=["C++ execution not implemented yet"],
            warnings=[],
            execution_time=0.0,
            memory_usage=None,
            exit_code=-1
        )
    def format_code(self, code: str) -> str:
        return code
    def lint_code(self, code: str) -> List[str]:
        return ["C++ linting not implemented yet"]

class GoProcessor(LanguageProcessor):
    """Go language processor (stub)."""
    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir
        self.features = LanguageFeatures(
            name="go",
            extensions=[".go"],
            compilation_required=False,
            interpreter_command=["go", "run"],
            compiler_command=["go", "build"],
            package_manager="go mod",
            test_framework="go test",
            linter="golint",
            formatter="gofmt",
            documentation_tool="godoc",
            dependency_file="go.mod"
        )
    def analyze_code(self, code: str, file_path: Optional[str] = None) -> CodeAnalysisResult:
        return CodeAnalysisResult(
            language="go",
            syntax_valid=True,
            complexity_score=0.5,
            maintainability_score=0.5,
            performance_hints=[],
            security_issues=[],
            style_violations=[],
            dependencies=[],
            exports=[],
            functions=[],
            classes=[],
            variables=[]
        )
    def execute_code(self, code: str, timeout: Optional[float] = None) -> ExecutionResult:
        return ExecutionResult(
            language="go",
            success=False,
            output="",
            errors=["Go execution not implemented yet"],
            warnings=[],
            execution_time=0.0,
            memory_usage=None,
            exit_code=-1
        )
    def format_code(self, code: str) -> str:
        return code
    def lint_code(self, code: str) -> List[str]:
        return ["Go linting not implemented yet"]

class RustProcessor(LanguageProcessor):
    """Rust language processor (stub)."""
    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir
        self.features = LanguageFeatures(
            name="rust",
            extensions=[".rs"],
            compilation_required=True,
            interpreter_command=["cargo", "run"],
            compiler_command=["cargo", "build"],
            package_manager="cargo",
            test_framework="cargo test",
            linter="clippy",
            formatter="rustfmt",
            documentation_tool="rustdoc",
            dependency_file="Cargo.toml"
        )
    def analyze_code(self, code: str, file_path: Optional[str] = None) -> CodeAnalysisResult:
        return CodeAnalysisResult(
            language="rust",
            syntax_valid=True,
            complexity_score=0.5,
            maintainability_score=0.5,
            performance_hints=[],
            security_issues=[],
            style_violations=[],
            dependencies=[],
            exports=[],
            functions=[],
            classes=[],
            variables=[]
        )
    def execute_code(self, code: str, timeout: Optional[float] = None) -> ExecutionResult:
        return ExecutionResult(
            language="rust",
            success=False,
            output="",
            errors=["Rust execution not implemented yet"],
            warnings=[],
            execution_time=0.0,
            memory_usage=None,
            exit_code=-1
        )
    def format_code(self, code: str) -> str:
        return code
    def lint_code(self, code: str) -> List[str]:
        return ["Rust linting not implemented yet"]

class MultiLanguageProcessor:
    """Multi-language processor that handles various programming languages."""
    
    def __init__(self, workspace_dir: Path):
        """Initialize the multi-language processor.
        
        Args:
            workspace_dir: The workspace directory
        """
        self.workspace_dir = workspace_dir
        self.processors: Dict[str, LanguageProcessor] = {}
        self.language_features: Dict[str, LanguageFeatures] = {}
        self.lock = threading.RLock()
        
        # Initialize language processors
        self._initialize_processors()
    
    def _initialize_processors(self):
        """Initialize language processors."""
        # Python processor
        python_processor = PythonProcessor(self.workspace_dir)
        self.processors["python"] = python_processor
        self.language_features["python"] = python_processor.features
        
        # JavaScript processor
        js_processor = JavaScriptProcessor(self.workspace_dir)
        self.processors["javascript"] = js_processor
        self.language_features["javascript"] = js_processor.features
        
        # Java processor
        java_processor = JavaProcessor(self.workspace_dir)
        self.processors["java"] = java_processor
        self.language_features["java"] = java_processor.features
        
        # C++ processor
        cpp_processor = CppProcessor(self.workspace_dir)
        self.processors["cpp"] = cpp_processor
        self.language_features["cpp"] = cpp_processor.features
        
        # Go processor
        go_processor = GoProcessor(self.workspace_dir)
        self.processors["go"] = go_processor
        self.language_features["go"] = go_processor.features
        
        # Rust processor
        rust_processor = RustProcessor(self.workspace_dir)
        self.processors["rust"] = rust_processor
        self.language_features["rust"] = rust_processor.features
    
    def detect_language(self, code: str, file_path: Optional[str] = None) -> str:
        """Detect the programming language of the code.
        
        Args:
            code: The code to analyze
            file_path: Optional file path for extension-based detection
            
        Returns:
            Detected language name
        """
        # First try file extension
        if file_path:
            extension = Path(file_path).suffix.lower()
            for lang, features in self.language_features.items():
                if extension in features.extensions:
                    return lang
        
        # Fallback to content-based detection
        return self._detect_language_by_content(code)
    
    def _detect_language_by_content(self, code: str) -> str:
        """Detect language by analyzing code content."""
        code_lower = code.lower()
        
        # Python indicators
        if any(keyword in code for keyword in ["def ", "import ", "from ", "class ", "if __name__"]):
            return "python"
        
        # JavaScript indicators
        if any(keyword in code for keyword in ["function ", "var ", "let ", "const ", "console.log"]):
            return "javascript"
        
        # Java indicators
        if any(keyword in code for keyword in ["public class", "public static void main", "System.out"]):
            return "java"
        
        # C++ indicators
        if any(keyword in code for keyword in ["#include", "std::", "cout", "int main"]):
            return "cpp"
        
        # Default to python
        return "python"
    
    def analyze_code(self, code: str, language: Optional[str] = None, 
                    file_path: Optional[str] = None) -> CodeAnalysisResult:
        """Analyze code in any supported language.
        
        Args:
            code: The code to analyze
            language: Optional language specification
            file_path: Optional file path
            
        Returns:
            Code analysis result
        """
        with self.lock:
            if language is None:
                language = self.detect_language(code, file_path)
            
            if language in self.processors:
                return self.processors[language].analyze_code(code, file_path)
            else:
                # Return basic analysis for unsupported languages
                return CodeAnalysisResult(
                    language=language,
                    syntax_valid=True,
                    complexity_score=0.5,
                    maintainability_score=0.5,
                    performance_hints=[],
                    security_issues=[],
                    style_violations=[],
                    dependencies=[],
                    exports=[],
                    functions=[],
                    classes=[],
                    variables=[]
                )
    
    def execute_code(self, code: str, language: Optional[str] = None,
                    timeout: Optional[float] = None) -> ExecutionResult:
        """Execute code in any supported language.
        
        Args:
            code: The code to execute
            language: Optional language specification
            timeout: Optional execution timeout
            
        Returns:
            Execution result
        """
        with self.lock:
            if language is None:
                language = self.detect_language(code)
            
            if language in self.processors:
                return self.processors[language].execute_code(code, timeout)
            else:
                return ExecutionResult(
                    language=language,
                    success=False,
                    output="",
                    errors=[f"Language '{language}' not supported for execution"],
                    warnings=[],
                    execution_time=0.0,
                    memory_usage=None,
                    exit_code=-1
                )
    
    def format_code(self, code: str, language: Optional[str] = None) -> str:
        """Format code in any supported language.
        
        Args:
            code: The code to format
            language: Optional language specification
            
        Returns:
            Formatted code
        """
        with self.lock:
            if language is None:
                language = self.detect_language(code)
            
            if language in self.processors:
                return self.processors[language].format_code(code)
            else:
                return code
    
    def lint_code(self, code: str, language: Optional[str] = None) -> List[str]:
        """Lint code in any supported language.
        
        Args:
            code: The code to lint
            language: Optional language specification
            
        Returns:
            List of linting issues
        """
        with self.lock:
            if language is None:
                language = self.detect_language(code)
            
            if language in self.processors:
                return self.processors[language].lint_code(code)
            else:
                return []
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages.
        
        Returns:
            List of supported language names
        """
        return list(self.processors.keys())
    
    def get_language_features(self, language: str) -> Optional[LanguageFeatures]:
        """Get features for a specific language.
        
        Args:
            language: The language name
            
        Returns:
            Language features or None if not supported
        """
        return self.language_features.get(language)
    
    def is_language_supported(self, language: str) -> bool:
        """Check if a language is supported.
        
        Args:
            language: The language name
            
        Returns:
            True if supported
        """
        return language in self.processors
