"""
Code tool for the Advanced AI Agent.
"""

import sys
import io
import os
import subprocess
import tempfile
import traceback
import platform
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple

class CodeTool:
    """Code tool for executing code in various programming languages."""

    def __init__(self):
        """Initialize the Code tool."""
        self.history: List[Dict[str, str]] = []

        # Define supported languages and their execution commands
        self.languages = {
            "python": {
                "extension": ".py",
                "command": ["python", "{file}"],
                "comment": "#"
            },
            "javascript": {
                "extension": ".js",
                "command": ["node", "{file}"],
                "comment": "//"
            },
            "typescript": {
                "extension": ".ts",
                "command": ["npx", "ts-node", "{file}"],
                "comment": "//"
            },
            "java": {
                "extension": ".java",
                "command": ["java", "{file}"],
                "compile_command": ["javac", "{file}"],
                "comment": "//"
            },
            "c": {
                "extension": ".c",
                "command": ["{executable}"],
                "compile_command": ["gcc", "{file}", "-o", "{executable}"],
                "comment": "//"
            },
            "cpp": {
                "extension": ".cpp",
                "command": ["{executable}"],
                "compile_command": ["g++", "{file}", "-o", "{executable}"],
                "comment": "//"
            },
            "csharp": {
                "extension": ".cs",
                "command": ["dotnet", "run", "{file}"],
                "comment": "//"
            },
            "go": {
                "extension": ".go",
                "command": ["go", "run", "{file}"],
                "comment": "//"
            },
            "ruby": {
                "extension": ".rb",
                "command": ["ruby", "{file}"],
                "comment": "#"
            },
            "php": {
                "extension": ".php",
                "command": ["php", "{file}"],
                "comment": "//"
            },
            "rust": {
                "extension": ".rs",
                "command": ["{executable}"],
                "compile_command": ["rustc", "{file}", "-o", "{executable}"],
                "comment": "//"
            },
            "shell": {
                "extension": ".ps1" if platform.system() == "Windows" else ".sh",
                "command": ["powershell", "-File", "{file}"] if platform.system() == "Windows" else ["bash", "{file}"],
                "comment": "#"
            },
            "powershell": {
                "extension": ".ps1",
                "command": ["powershell", "-File", "{file}"],
                "comment": "#"
            },
            "r": {
                "extension": ".r",
                "command": ["Rscript", "{file}"],
                "comment": "#"
            },
            "swift": {
                "extension": ".swift",
                "command": ["swift", "{file}"],
                "comment": "//"
            },
            "kotlin": {
                "extension": ".kt",
                "command": ["kotlin", "{file}"],
                "compile_command": ["kotlinc", "{file}", "-include-runtime", "-d", "{executable}.jar"],
                "comment": "//"
            },
            "scala": {
                "extension": ".scala",
                "command": ["scala", "{file}"],
                "comment": "//"
            },
            "perl": {
                "extension": ".pl",
                "command": ["perl", "{file}"],
                "comment": "#"
            },
            "html": {
                "extension": ".html",
                "command": None,  # No direct execution, just save the file
                "comment": "<!--"
            },
            "css": {
                "extension": ".css",
                "command": None,  # No direct execution, just save the file
                "comment": "/*"
            },
            "sql": {
                "extension": ".sql",
                "command": ["sqlite3", "-init", "{file}", ":memory:", ".quit"],
                "comment": "--"
            }
        }

    def execute(self, code: str, language: str = "python") -> Tuple[str, str, int]:
        """Execute code in the specified language.

        Args:
            code: The code to execute.
            language: The programming language to use.

        Returns:
            A tuple of (stdout, stderr, return_code).
        """
        # Add to history
        self.history.append({"code": code, "language": language})

        # Check if the language is supported
        language = language.lower().strip()
        if language not in self.languages:
            error_message = f"Unsupported language: {language}. Supported languages: {', '.join(self.languages.keys())}"
            self.history[-1]["error"] = error_message
            return "", error_message, 1

        # Get language configuration
        lang_config = self.languages[language]

        # Create temporary files
        temp_file_path = None
        stdout_file_name = None
        stderr_file_name = None
        executable = None

        try:
            # Create a temporary file with appropriate extension
            temp_file = tempfile.NamedTemporaryFile(suffix=lang_config["extension"], delete=False)
            temp_file_path = temp_file.name

            # Write the code to the file
            try:
                temp_file.write(code.encode('utf-8'))
            except UnicodeEncodeError:
                # Fallback to a more permissive encoding if UTF-8 fails
                temp_file.write(code.encode('latin-1'))
            temp_file.close()

            # Create temporary files for stdout and stderr
            stdout_file = tempfile.NamedTemporaryFile(mode="w+", delete=False)
            stderr_file = tempfile.NamedTemporaryFile(mode="w+", delete=False)
            stdout_file_name = stdout_file.name
            stderr_file_name = stderr_file.name
            stdout_file.close()
            stderr_file.close()

            # Compile if necessary
            if "compile_command" in lang_config:
                executable = temp_file_path + ".exe" if os.name == "nt" else temp_file_path + ".out"
                compile_command = [cmd.format(file=temp_file_path, executable=executable) for cmd in lang_config["compile_command"]]

                try:
                    # Execute the compile command with timeout
                    compile_process = subprocess.run(
                        compile_command,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        timeout=30,  # 30 second timeout for compilation
                        universal_newlines=True,
                        check=False
                    )

                    # Check if compilation was successful
                    if compile_process.returncode != 0:
                        # Add to history
                        self.history[-1]["stdout"] = compile_process.stdout
                        self.history[-1]["stderr"] = compile_process.stderr
                        self.history[-1]["return_code"] = compile_process.returncode

                        return compile_process.stdout, f"Compilation error: {compile_process.stderr}", compile_process.returncode
                except subprocess.TimeoutExpired:
                    return "", "Compilation timed out after 30 seconds", 1
                except Exception as e:
                    return "", f"Compilation error: {str(e)}", 1
            else:
                executable = None

            # Skip execution for languages that don't have a command
            if lang_config["command"] is None:
                # Add to history
                self.history[-1]["stdout"] = f"File saved to {temp_file_path}"
                self.history[-1]["stderr"] = ""
                self.history[-1]["return_code"] = 0

                return f"File saved to {temp_file_path}", "", 0

            # Execute the code
            if executable:
                command = [cmd.format(file=temp_file_path, executable=executable) for cmd in lang_config["command"]]
            else:
                command = [cmd.format(file=temp_file_path) for cmd in lang_config["command"]]

            try:
                # Execute the command with timeout
                process = subprocess.run(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=30,  # 30 second timeout for execution
                    universal_newlines=True,
                    check=False
                )

                # Add to history
                self.history[-1]["stdout"] = process.stdout
                self.history[-1]["stderr"] = process.stderr
                self.history[-1]["return_code"] = process.returncode

                return process.stdout, process.stderr, process.returncode
            except subprocess.TimeoutExpired:
                return "", "Execution timed out after 30 seconds", 1
            except Exception as e:
                return "", f"Execution error: {str(e)}", 1

        except Exception as e:
            error_message = f"Error executing code: {str(e)}"
            self.history[-1]["error"] = error_message
            return "", error_message, 1

        finally:
            # Clean up temporary files
            try:
                if temp_file_path and os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                if stdout_file_name and os.path.exists(stdout_file_name):
                    os.unlink(stdout_file_name)
                if stderr_file_name and os.path.exists(stderr_file_name):
                    os.unlink(stderr_file_name)
                if executable and os.path.exists(executable):
                    os.unlink(executable)
            except Exception:
                pass  # Ignore cleanup errors

    def get_history(self) -> List[Dict[str, str]]:
        """Get the execution history.

        Returns:
            The execution history.
        """
        return self.history

    def get_supported_languages(self) -> List[str]:
        """Get the list of supported languages.

        Returns:
            The list of supported languages.
        """
        return list(self.languages.keys())
