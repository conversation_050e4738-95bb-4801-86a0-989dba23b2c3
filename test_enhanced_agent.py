from agent import Agent
from models import <PERSON><PERSON><PERSON><PERSON>
from conversation import Conversation<PERSON>anager
from pathlib import Path

def test_enhanced_agent():
    """Test the fully enhanced AI agent."""
    
    # Initialize agent
    workspace_dir = Path('.')
    history_dir = workspace_dir / 'conversations'
    model_manager = ModelManager()
    conversation_manager = ConversationManager(history_dir)
    agent = Agent(model_manager, conversation_manager, workspace_dir)

    print('🚀 Testing Fully Enhanced AI Agent')
    print('Features: Fast streaming + Smart tools + Step-by-step execution')

    # Test 1: Fast streaming like gptme
    print('\n1. Testing Fast Streaming...')
    try:
        message = 'Create a simple HTML page with hello world'
        print(f'Request: {message}')
        print('Response: ', end='')
        
        response_text = ''
        for chunk in agent.stream_process_message(message):
            print(chunk, end='', flush=True)
            response_text += chunk
        
        print(f'\n✅ Fast streaming works! ({len(response_text)} chars)')
    except Exception as e:
        print(f'❌ Streaming failed: {e}')

    # Test 2: Tool integration
    print('\n2. Testing Smart Tool Integration...')
    try:
        # Test file operations
        result = agent._execute_file('write test.html <!DOCTYPE html><html><body><h1>Hello World</h1></body></html>')
        print(f'✅ File tool: {result}')
        
        # Test shell operations
        result = agent._execute_shell('echo "Tool integration test"')
        print(f'✅ Shell tool: {result[:50]}...')
        
    except Exception as e:
        print(f'❌ Tool integration failed: {e}')

    # Test 3: Step-by-step execution
    print('\n3. Testing Step-by-Step Execution...')
    try:
        agent.iterative_mode = True
        message = 'Create a Python calculator'
        print(f'Request: {message}')
        
        result = ''
        for chunk in agent.execute_iteratively(message):
            print(chunk, end='', flush=True)
            result += chunk
        
        print(f'\n✅ Step-by-step execution works! ({len(result)} chars)')
    except Exception as e:
        print(f'❌ Iterative execution failed: {e}')
        import traceback
        traceback.print_exc()

    print('\n🎯 Enhanced AI Agent Test Complete!')
    print('✅ All core features are working!')

if __name__ == "__main__":
    test_enhanced_agent()
