"""
Self-Analysis Loop System for the AI Code Assistant.

This module implements a recursive analysis mechanism that executes code,
analyzes results, identifies what worked/failed, and adapts the next action
based on situational context.
"""

import time
import json
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)

class AnalysisState(Enum):
    """States of the analysis loop."""
    INITIALIZING = "initializing"
    EXECUTING = "executing"
    ANALYZING = "analyzing"
    ADAPTING = "adapting"
    OPTIMIZING = "optimizing"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class AnalysisResult:
    """Result of an analysis iteration."""
    iteration: int
    state: AnalysisState
    success: bool
    execution_time: float
    output: Any
    errors: List[str]
    warnings: List[str]
    metrics: Dict[str, Any]
    insights: List[str]
    next_actions: List[str]
    confidence_score: float
    timestamp: float

@dataclass
class AnalysisContext:
    """Context for the analysis loop."""
    task_description: str
    code: str
    language: str
    expected_outcome: Optional[str]
    constraints: List[str]
    optimization_goals: List[str]
    max_iterations: int
    timeout_seconds: float

class SelfAnalyzer:
    """Self-analyzing system that learns and adapts through iterative analysis."""

    def __init__(self, model_manager, workspace_dir: Path):
        """Initialize the self-analyzer.
        
        Args:
            model_manager: The model manager for AI interactions
            workspace_dir: The workspace directory
        """
        self.model_manager = model_manager
        self.workspace_dir = workspace_dir
        self.analysis_history: List[AnalysisResult] = []
        self.learning_patterns: Dict[str, Any] = {}
        self.optimization_strategies: Dict[str, Callable] = {}
        self.lock = threading.RLock()
        
        # Initialize optimization strategies
        self._initialize_optimization_strategies()

    def _initialize_optimization_strategies(self):
        """Initialize optimization strategies."""
        self.optimization_strategies = {
            "performance": self._optimize_for_performance,
            "readability": self._optimize_for_readability,
            "maintainability": self._optimize_for_maintainability,
            "security": self._optimize_for_security,
            "efficiency": self._optimize_for_efficiency,
        }

    def analyze_and_iterate(self, context: AnalysisContext) -> List[AnalysisResult]:
        """Run the self-analysis loop until optimal solution is reached.
        
        Args:
            context: The analysis context
            
        Returns:
            List of analysis results from all iterations
        """
        with self.lock:
            logger.info(f"Starting self-analysis loop for: {context.task_description}")
            
            results = []
            current_code = context.code
            iteration = 0
            start_time = time.time()
            
            while iteration < context.max_iterations:
                if time.time() - start_time > context.timeout_seconds:
                    logger.warning("Analysis loop timed out")
                    break
                
                iteration += 1
                logger.info(f"Analysis iteration {iteration}")
                
                # Execute and analyze
                result = self._execute_analysis_iteration(
                    context, current_code, iteration
                )
                results.append(result)
                
                # Check if we've reached optimal solution
                if self._is_optimal_solution(result, context):
                    logger.info(f"Optimal solution reached in {iteration} iterations")
                    break
                
                # Adapt for next iteration
                if result.success and result.next_actions:
                    current_code = self._adapt_code(current_code, result, context)
                else:
                    logger.warning(f"Iteration {iteration} failed, attempting recovery")
                    current_code = self._recover_from_failure(current_code, result, context)
            
            # Learn from the analysis session
            self._learn_from_session(results, context)
            
            return results

    def _execute_analysis_iteration(self, context: AnalysisContext, code: str, iteration: int) -> AnalysisResult:
        """Execute a single analysis iteration.
        
        Args:
            context: The analysis context
            code: The code to analyze
            iteration: The iteration number
            
        Returns:
            The analysis result
        """
        start_time = time.time()
        
        try:
            # Execute the code
            execution_result = self._execute_code(code, context.language)
            
            # Analyze the execution result
            analysis = self._analyze_execution_result(execution_result, context)
            
            # Generate insights and next actions
            insights = self._generate_insights(execution_result, analysis, context)
            next_actions = self._determine_next_actions(execution_result, analysis, insights, context)
            
            # Calculate confidence score
            confidence = self._calculate_confidence_score(execution_result, analysis, insights)
            
            execution_time = time.time() - start_time
            
            return AnalysisResult(
                iteration=iteration,
                state=AnalysisState.COMPLETED,
                success=execution_result.get("success", False),
                execution_time=execution_time,
                output=execution_result.get("output"),
                errors=execution_result.get("errors", []),
                warnings=execution_result.get("warnings", []),
                metrics=analysis.get("metrics", {}),
                insights=insights,
                next_actions=next_actions,
                confidence_score=confidence,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error in analysis iteration {iteration}: {e}")
            return AnalysisResult(
                iteration=iteration,
                state=AnalysisState.FAILED,
                success=False,
                execution_time=time.time() - start_time,
                output=None,
                errors=[str(e)],
                warnings=[],
                metrics={},
                insights=[],
                next_actions=[],
                confidence_score=0.0,
                timestamp=time.time()
            )

    def _execute_code(self, code: str, language: str) -> Dict[str, Any]:
        """Execute code and capture results.
        
        Args:
            code: The code to execute
            language: The programming language
            
        Returns:
            Execution result dictionary
        """
        # This would integrate with the existing CodeTool
        # For now, return a mock result
        return {
            "success": True,
            "output": "Mock execution output",
            "errors": [],
            "warnings": [],
            "execution_time": 0.1,
            "memory_usage": 1024,
            "cpu_usage": 5.0
        }

    def _analyze_execution_result(self, execution_result: Dict[str, Any], context: AnalysisContext) -> Dict[str, Any]:
        """Analyze the execution result.
        
        Args:
            execution_result: The execution result
            context: The analysis context
            
        Returns:
            Analysis dictionary
        """
        analysis = {
            "metrics": {
                "success_rate": 1.0 if execution_result.get("success") else 0.0,
                "execution_time": execution_result.get("execution_time", 0),
                "memory_efficiency": self._calculate_memory_efficiency(execution_result),
                "cpu_efficiency": self._calculate_cpu_efficiency(execution_result),
            },
            "quality_score": self._calculate_quality_score(execution_result, context),
            "optimization_potential": self._assess_optimization_potential(execution_result, context),
        }
        
        return analysis

    def _generate_insights(self, execution_result: Dict[str, Any], analysis: Dict[str, Any], context: AnalysisContext) -> List[str]:
        """Generate insights from the analysis.
        
        Args:
            execution_result: The execution result
            analysis: The analysis result
            context: The analysis context
            
        Returns:
            List of insights
        """
        insights = []
        
        # Performance insights
        if analysis["metrics"]["execution_time"] > 1.0:
            insights.append("Code execution time is high, consider optimization")
        
        # Memory insights
        if analysis["metrics"]["memory_efficiency"] < 0.7:
            insights.append("Memory usage could be optimized")
        
        # Quality insights
        if analysis["quality_score"] < 0.8:
            insights.append("Code quality could be improved")
        
        return insights

    def _determine_next_actions(self, execution_result: Dict[str, Any], analysis: Dict[str, Any], 
                               insights: List[str], context: AnalysisContext) -> List[str]:
        """Determine next actions based on analysis.
        
        Args:
            execution_result: The execution result
            analysis: The analysis result
            insights: The generated insights
            context: The analysis context
            
        Returns:
            List of next actions
        """
        actions = []
        
        # Based on optimization goals
        for goal in context.optimization_goals:
            if goal in self.optimization_strategies:
                actions.append(f"apply_{goal}_optimization")
        
        # Based on insights
        for insight in insights:
            if "optimization" in insight.lower():
                actions.append("optimize_performance")
            elif "quality" in insight.lower():
                actions.append("improve_code_quality")
        
        return actions

    def _calculate_confidence_score(self, execution_result: Dict[str, Any], 
                                   analysis: Dict[str, Any], insights: List[str]) -> float:
        """Calculate confidence score for the analysis.
        
        Args:
            execution_result: The execution result
            analysis: The analysis result
            insights: The generated insights
            
        Returns:
            Confidence score between 0 and 1
        """
        base_score = 0.5
        
        # Increase confidence if execution was successful
        if execution_result.get("success"):
            base_score += 0.3
        
        # Increase confidence based on quality score
        quality_score = analysis.get("quality_score", 0.5)
        base_score += quality_score * 0.2
        
        # Decrease confidence if there are many errors
        error_count = len(execution_result.get("errors", []))
        base_score -= min(error_count * 0.1, 0.3)
        
        return max(0.0, min(1.0, base_score))

    def _is_optimal_solution(self, result: AnalysisResult, context: AnalysisContext) -> bool:
        """Check if the current solution is optimal.
        
        Args:
            result: The analysis result
            context: The analysis context
            
        Returns:
            True if solution is optimal
        """
        # Check if all optimization goals are met
        if result.confidence_score >= 0.9 and result.success:
            return True
        
        # Check if no significant improvements can be made
        if len(result.next_actions) == 0 and result.success:
            return True
        
        return False

    def _adapt_code(self, code: str, result: AnalysisResult, context: AnalysisContext) -> str:
        """Adapt code based on analysis result.
        
        Args:
            code: The current code
            result: The analysis result
            context: The analysis context
            
        Returns:
            Adapted code
        """
        adapted_code = code
        
        # Apply optimizations based on next actions
        for action in result.next_actions:
            if action in self.optimization_strategies:
                adapted_code = self.optimization_strategies[action](adapted_code, context)
        
        return adapted_code

    def _recover_from_failure(self, code: str, result: AnalysisResult, context: AnalysisContext) -> str:
        """Recover from analysis failure.
        
        Args:
            code: The current code
            result: The failed analysis result
            context: The analysis context
            
        Returns:
            Recovered code
        """
        # Implement recovery strategies
        logger.info("Attempting to recover from analysis failure")
        
        # Try to fix common errors
        if result.errors:
            for error in result.errors:
                if "syntax" in error.lower():
                    code = self._fix_syntax_errors(code, context.language)
                elif "import" in error.lower():
                    code = self._fix_import_errors(code, context.language)
        
        return code

    def _learn_from_session(self, results: List[AnalysisResult], context: AnalysisContext):
        """Learn from the analysis session.
        
        Args:
            results: All analysis results from the session
            context: The analysis context
        """
        # Extract learning patterns
        successful_iterations = [r for r in results if r.success]
        failed_iterations = [r for r in results if not r.success]
        
        # Update learning patterns
        pattern_key = f"{context.language}_{context.task_description[:50]}"
        self.learning_patterns[pattern_key] = {
            "success_rate": len(successful_iterations) / len(results),
            "avg_iterations": len(results),
            "common_insights": self._extract_common_insights(results),
            "effective_actions": self._extract_effective_actions(results),
        }
        
        logger.info(f"Learned new patterns for {pattern_key}")

    # Optimization strategy methods
    def _optimize_for_performance(self, code: str, context: AnalysisContext) -> str:
        """Optimize code for performance."""
        # Implement performance optimizations
        return code

    def _optimize_for_readability(self, code: str, context: AnalysisContext) -> str:
        """Optimize code for readability."""
        # Implement readability optimizations
        return code

    def _optimize_for_maintainability(self, code: str, context: AnalysisContext) -> str:
        """Optimize code for maintainability."""
        # Implement maintainability optimizations
        return code

    def _optimize_for_security(self, code: str, context: AnalysisContext) -> str:
        """Optimize code for security."""
        # Implement security optimizations
        return code

    def _optimize_for_efficiency(self, code: str, context: AnalysisContext) -> str:
        """Optimize code for efficiency."""
        # Implement efficiency optimizations
        return code

    # Helper methods
    def _calculate_memory_efficiency(self, execution_result: Dict[str, Any]) -> float:
        """Calculate memory efficiency score."""
        memory_usage = execution_result.get("memory_usage", 0)
        # Simple heuristic - lower memory usage is better
        return max(0.0, 1.0 - (memory_usage / 10000))

    def _calculate_cpu_efficiency(self, execution_result: Dict[str, Any]) -> float:
        """Calculate CPU efficiency score."""
        cpu_usage = execution_result.get("cpu_usage", 0)
        # Simple heuristic - lower CPU usage is better
        return max(0.0, 1.0 - (cpu_usage / 100))

    def _calculate_quality_score(self, execution_result: Dict[str, Any], context: AnalysisContext) -> float:
        """Calculate code quality score."""
        # Simple quality score based on success and error count
        base_score = 0.8 if execution_result.get("success") else 0.2
        error_penalty = len(execution_result.get("errors", [])) * 0.1
        return max(0.0, base_score - error_penalty)

    def _assess_optimization_potential(self, execution_result: Dict[str, Any], context: AnalysisContext) -> float:
        """Assess optimization potential."""
        # Simple heuristic based on performance metrics
        time_score = 1.0 - min(execution_result.get("execution_time", 0) / 10.0, 1.0)
        memory_score = self._calculate_memory_efficiency(execution_result)
        return (time_score + memory_score) / 2

    def _fix_syntax_errors(self, code: str, language: str) -> str:
        """Fix common syntax errors."""
        # Implement syntax error fixes
        return code

    def _fix_import_errors(self, code: str, language: str) -> str:
        """Fix common import errors."""
        # Implement import error fixes
        return code

    def _extract_common_insights(self, results: List[AnalysisResult]) -> List[str]:
        """Extract common insights from results."""
        all_insights = []
        for result in results:
            all_insights.extend(result.insights)
        
        # Count frequency and return most common
        insight_counts = {}
        for insight in all_insights:
            insight_counts[insight] = insight_counts.get(insight, 0) + 1
        
        return sorted(insight_counts.keys(), key=lambda x: insight_counts[x], reverse=True)[:5]

    def _extract_effective_actions(self, results: List[AnalysisResult]) -> List[str]:
        """Extract effective actions from results."""
        effective_actions = []
        for i, result in enumerate(results[:-1]):
            if results[i + 1].confidence_score > result.confidence_score:
                effective_actions.extend(result.next_actions)
        
        return list(set(effective_actions))
