"""
Information synthesizer tool for the Advanced AI Agent.
This provides functionality to process and combine information from multiple sources.
"""

import re
import json
import time
from typing import Dict, List, Optional, Any, Union, Tuple, Set

# Try to import optional dependencies
try:
    import nltk
    from nltk.tokenize import sent_tokenize
    from nltk.corpus import stopwords
    NLTK_AVAILABLE = True
    
    # Download required NLTK data
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt', quiet=True)
    
    try:
        nltk.data.find('corpora/stopwords')
    except LookupError:
        nltk.download('stopwords', quiet=True)
        
except ImportError:
    NLTK_AVAILABLE = False

try:
    from sumy.parsers.plaintext import PlaintextParser
    from sumy.nlp.tokenizers import Tokenizer
    from sumy.summarizers.lsa import LsaSummarizer
    from sumy.summarizers.lex_rank import LexRankSummarizer
    from sumy.nlp.stemmers import Stemmer
    from sumy.utils import get_stop_words
    SUMY_AVAILABLE = True
except ImportError:
    SUMY_AVAILABLE = False

class InformationSynthesizer:
    """Information synthesizer for processing and combining information."""
    
    def __init__(self, language: str = "english"):
        """Initialize the information synthesizer.
        
        Args:
            language: The language to use for text processing.
        """
        self.language = language
        self.history: List[Dict[str, Any]] = []
        
        # Initialize stopwords if NLTK is available
        self.stopwords: Set[str] = set()
        if NLTK_AVAILABLE:
            self.stopwords = set(stopwords.words(language))
    
    def extract_key_information(self, text: str, max_length: int = 1000) -> str:
        """Extract key information from text.
        
        Args:
            text: The text to extract information from.
            max_length: The maximum length of the extracted information.
            
        Returns:
            The extracted key information.
        """
        # Add to history
        self.history.append({
            "action": "extract_key_information",
            "text_length": len(text),
            "max_length": max_length,
            "timestamp": time.time()
        })
        
        # If text is already shorter than max_length, return it as is
        if len(text) <= max_length:
            return text
        
        # Try different summarization methods based on available libraries
        if SUMY_AVAILABLE:
            summary = self._summarize_with_sumy(text, max_length)
            self.history[-1]["method"] = "sumy"
            return summary
        elif NLTK_AVAILABLE:
            summary = self._summarize_with_nltk(text, max_length)
            self.history[-1]["method"] = "nltk"
            return summary
        else:
            # Fallback to simple extraction
            summary = self._simple_extract(text, max_length)
            self.history[-1]["method"] = "simple"
            return summary
    
    def _summarize_with_sumy(self, text: str, max_length: int) -> str:
        """Summarize text using sumy.
        
        Args:
            text: The text to summarize.
            max_length: The maximum length of the summary.
            
        Returns:
            The summarized text.
        """
        # Create a parser for the text
        parser = PlaintextParser.from_string(text, Tokenizer(self.language))
        
        # Create a stemmer for the language
        stemmer = Stemmer(self.language)
        
        # Create summarizers
        lsa_summarizer = LsaSummarizer(stemmer)
        lsa_summarizer.stop_words = get_stop_words(self.language)
        
        lex_rank_summarizer = LexRankSummarizer(stemmer)
        lex_rank_summarizer.stop_words = get_stop_words(self.language)
        
        # Estimate the number of sentences needed
        avg_sentence_length = len(text) / max(1, len(parser.document.sentences))
        num_sentences = min(
            len(parser.document.sentences),
            max(3, int(max_length / avg_sentence_length))
        )
        
        # Get summaries from both summarizers
        lsa_summary = lsa_summarizer(parser.document, num_sentences)
        lex_rank_summary = lex_rank_summarizer(parser.document, num_sentences)
        
        # Combine the summaries (prioritize sentences that appear in both)
        combined_summary = []
        lsa_sentences = set(str(sentence) for sentence in lsa_summary)
        
        # First add sentences that appear in both summaries
        for sentence in lex_rank_summary:
            if str(sentence) in lsa_sentences:
                combined_summary.append(str(sentence))
        
        # Then add remaining sentences from LSA until we reach the desired count
        for sentence in lsa_summary:
            if str(sentence) not in combined_summary and len(combined_summary) < num_sentences:
                combined_summary.append(str(sentence))
        
        # Then add remaining sentences from LexRank until we reach the desired count
        for sentence in lex_rank_summary:
            if str(sentence) not in combined_summary and len(combined_summary) < num_sentences:
                combined_summary.append(str(sentence))
        
        # Join the sentences
        summary = " ".join(combined_summary)
        
        # If still too long, truncate
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
        
        return summary
    
    def _summarize_with_nltk(self, text: str, max_length: int) -> str:
        """Summarize text using NLTK.
        
        Args:
            text: The text to summarize.
            max_length: The maximum length of the summary.
            
        Returns:
            The summarized text.
        """
        # Tokenize the text into sentences
        sentences = sent_tokenize(text)
        
        # Calculate sentence scores based on word frequency
        word_frequencies = {}
        
        # Tokenize each sentence into words and count frequencies
        for sentence in sentences:
            for word in nltk.word_tokenize(sentence.lower()):
                if word not in self.stopwords and word.isalnum():
                    if word not in word_frequencies:
                        word_frequencies[word] = 1
                    else:
                        word_frequencies[word] += 1
        
        # Normalize word frequencies
        max_frequency = max(word_frequencies.values()) if word_frequencies else 1
        for word in word_frequencies:
            word_frequencies[word] = word_frequencies[word] / max_frequency
        
        # Calculate sentence scores
        sentence_scores = {}
        for i, sentence in enumerate(sentences):
            for word in nltk.word_tokenize(sentence.lower()):
                if word in word_frequencies:
                    if i not in sentence_scores:
                        sentence_scores[i] = word_frequencies[word]
                    else:
                        sentence_scores[i] += word_frequencies[word]
        
        # Estimate the number of sentences needed
        avg_sentence_length = len(text) / max(1, len(sentences))
        num_sentences = min(
            len(sentences),
            max(3, int(max_length / avg_sentence_length))
        )
        
        # Get the top sentences
        top_sentences = sorted(sentence_scores.items(), key=lambda x: x[1], reverse=True)[:num_sentences]
        top_sentences = sorted(top_sentences, key=lambda x: x[0])  # Sort by original order
        
        # Join the top sentences
        summary = " ".join(sentences[i] for i, _ in top_sentences)
        
        # If still too long, truncate
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
        
        return summary
    
    def _simple_extract(self, text: str, max_length: int) -> str:
        """Extract information using a simple approach.
        
        Args:
            text: The text to extract information from.
            max_length: The maximum length of the extracted information.
            
        Returns:
            The extracted information.
        """
        # Split into paragraphs
        paragraphs = text.split("\n\n")
        
        # Keep the first paragraph and as many subsequent paragraphs as will fit
        result = paragraphs[0]
        
        for paragraph in paragraphs[1:]:
            if len(result) + len(paragraph) + 2 <= max_length:
                result += "\n\n" + paragraph
            else:
                break
        
        # If still too long, truncate
        if len(result) > max_length:
            result = result[:max_length] + "..."
        
        return result
    
    def combine_information(self, texts: List[str], max_length: int = 2000) -> str:
        """Combine information from multiple sources.
        
        Args:
            texts: The texts to combine.
            max_length: The maximum length of the combined information.
            
        Returns:
            The combined information.
        """
        # Add to history
        self.history.append({
            "action": "combine_information",
            "num_texts": len(texts),
            "text_lengths": [len(text) for text in texts],
            "max_length": max_length,
            "timestamp": time.time()
        })
        
        # If no texts, return empty string
        if not texts:
            return ""
        
        # If only one text, extract key information from it
        if len(texts) == 1:
            return self.extract_key_information(texts[0], max_length)
        
        # Extract key information from each text
        extracted_texts = []
        per_text_length = max_length // len(texts)
        
        for text in texts:
            extracted = self.extract_key_information(text, per_text_length)
            extracted_texts.append(extracted)
        
        # Combine the extracted texts
        combined = "\n\n".join(extracted_texts)
        
        # If still too long, extract key information from the combined text
        if len(combined) > max_length:
            combined = self.extract_key_information(combined, max_length)
        
        return combined
    
    def format_information(self, information: str, format_type: str = "markdown") -> str:
        """Format information in a structured manner.
        
        Args:
            information: The information to format.
            format_type: The type of formatting to apply.
            
        Returns:
            The formatted information.
        """
        # Add to history
        self.history.append({
            "action": "format_information",
            "format_type": format_type,
            "information_length": len(information),
            "timestamp": time.time()
        })
        
        if format_type == "markdown":
            return self._format_as_markdown(information)
        elif format_type == "json":
            return self._format_as_json(information)
        elif format_type == "html":
            return self._format_as_html(information)
        else:
            return information
    
    def _format_as_markdown(self, information: str) -> str:
        """Format information as markdown.
        
        Args:
            information: The information to format.
            
        Returns:
            The formatted information.
        """
        # Split into paragraphs
        paragraphs = information.split("\n\n")
        
        # Format the first paragraph as a heading
        if paragraphs:
            paragraphs[0] = f"## {paragraphs[0]}"
        
        # Add bullet points to other paragraphs
        for i in range(1, len(paragraphs)):
            # Skip if paragraph is already formatted
            if paragraphs[i].startswith("#") or paragraphs[i].startswith("*") or paragraphs[i].startswith("-"):
                continue
                
            # Split into sentences
            if NLTK_AVAILABLE:
                sentences = sent_tokenize(paragraphs[i])
            else:
                sentences = re.split(r'(?<=[.!?])\s+', paragraphs[i])
            
            # Format as bullet points if multiple sentences
            if len(sentences) > 1:
                paragraphs[i] = "\n".join(f"* {sentence}" for sentence in sentences)
        
        # Join the paragraphs
        return "\n\n".join(paragraphs)
    
    def _format_as_json(self, information: str) -> str:
        """Format information as JSON.
        
        Args:
            information: The information to format.
            
        Returns:
            The formatted information.
        """
        # Split into paragraphs
        paragraphs = information.split("\n\n")
        
        # Create a JSON structure
        result = {
            "title": paragraphs[0] if paragraphs else "",
            "content": paragraphs[1:] if len(paragraphs) > 1 else []
        }
        
        # Convert to JSON string
        return json.dumps(result, indent=2)
    
    def _format_as_html(self, information: str) -> str:
        """Format information as HTML.
        
        Args:
            information: The information to format.
            
        Returns:
            The formatted information.
        """
        # Split into paragraphs
        paragraphs = information.split("\n\n")
        
        # Format the first paragraph as a heading
        html = "<div class='information'>\n"
        
        if paragraphs:
            html += f"  <h2>{paragraphs[0]}</h2>\n"
        
        # Format other paragraphs
        for i in range(1, len(paragraphs)):
            html += f"  <p>{paragraphs[i]}</p>\n"
        
        html += "</div>"
        
        return html
    
    def get_history(self) -> List[Dict[str, Any]]:
        """Get the synthesizer history.
        
        Returns:
            The synthesizer history.
        """
        return self.history
