"""
Result collector for the Advanced AI Agent.
"""

import time
import logging
import threading
from typing import Dict, List, Optional, Any, Union, Tuple, Callable, Set, Generic, TypeVar

from tools.threading.thread_manager import ThreadManager

# Set up logging
logger = logging.getLogger(__name__)

# Type variable for generic result type
T = TypeVar('T')

class ResultCollector(Generic[T]):
    """Collector for aggregating results from parallel tasks."""
    
    def __init__(self, thread_manager: ThreadManager):
        """Initialize the result collector.
        
        Args:
            thread_manager: Thread manager to use for task execution.
        """
        self.thread_manager = thread_manager
        self.results: Dict[str, T] = {}
        self.callbacks: Dict[str, List[Callable[[str, T], None]]] = {}
        self.lock = threading.RLock()
        
    def register_result(self, task_id: str, result: T):
        """Register a result from a completed task.
        
        Args:
            task_id: Task identifier.
            result: Result of the task.
        """
        with self.lock:
            self.results[task_id] = result
            
            # Call callbacks
            if task_id in self.callbacks:
                for callback in self.callbacks[task_id]:
                    try:
                        callback(task_id, result)
                    except Exception as e:
                        logger.error(f"Error in callback for task '{task_id}': {e}", exc_info=True)
    
    def get_result(self, task_id: str) -> Optional[T]:
        """Get the result of a task.
        
        Args:
            task_id: Task identifier.
            
        Returns:
            Result of the task, or None if not available.
        """
        with self.lock:
            return self.results.get(task_id)
    
    def add_callback(self, task_id: str, callback: Callable[[str, T], None]):
        """Add a callback for when a result is registered.
        
        Args:
            task_id: Task identifier.
            callback: Function to call with (task_id, result) when the result is registered.
        """
        with self.lock:
            if task_id not in self.callbacks:
                self.callbacks[task_id] = []
            self.callbacks[task_id].append(callback)
            
            # Call immediately if result already exists
            if task_id in self.results:
                try:
                    callback(task_id, self.results[task_id])
                except Exception as e:
                    logger.error(f"Error in callback for task '{task_id}': {e}", exc_info=True)
    
    def wait_for_result(self, task_id: str, timeout: Optional[float] = None) -> Optional[T]:
        """Wait for a result to be available.
        
        Args:
            task_id: Task identifier.
            timeout: Maximum time to wait in seconds.
            
        Returns:
            Result of the task, or None if timeout occurred.
        """
        start_time = time.time()
        while timeout is None or time.time() - start_time < timeout:
            with self.lock:
                if task_id in self.results:
                    return self.results[task_id]
            time.sleep(0.1)
        return None
    
    def wait_for_results(self, task_ids: List[str], timeout: Optional[float] = None) -> Dict[str, T]:
        """Wait for multiple results to be available.
        
        Args:
            task_ids: List of task identifiers.
            timeout: Maximum time to wait in seconds.
            
        Returns:
            Dictionary mapping task IDs to results for available results.
        """
        start_time = time.time()
        remaining_ids = set(task_ids)
        
        while remaining_ids and (timeout is None or time.time() - start_time < timeout):
            with self.lock:
                for task_id in list(remaining_ids):
                    if task_id in self.results:
                        remaining_ids.remove(task_id)
            
            if not remaining_ids:
                break
                
            time.sleep(0.1)
        
        with self.lock:
            return {task_id: self.results[task_id] for task_id in task_ids if task_id in self.results}
    
    def clear_result(self, task_id: str):
        """Clear a result.
        
        Args:
            task_id: Task identifier.
        """
        with self.lock:
            if task_id in self.results:
                del self.results[task_id]
    
    def clear_all_results(self):
        """Clear all results."""
        with self.lock:
            self.results.clear()
    
    def get_all_results(self) -> Dict[str, T]:
        """Get all results.
        
        Returns:
            Dictionary mapping task IDs to results.
        """
        with self.lock:
            return dict(self.results)
