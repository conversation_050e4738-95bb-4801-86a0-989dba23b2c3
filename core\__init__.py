"""
Core AI Code Analysis and Generation System.

This package contains the advanced AI-powered code analysis and generation system
with self-analyzing capabilities, intelligent error handling, and RAG-enhanced generation.
"""

from .ai_code_assistant import AICodeAssistant
from .self_analyzer import SelfAnalyzer
from .execution_monitor import ExecutionMonitor
from .adaptive_controller import Adaptive<PERSON>ontroller
from .optimization_engine import Optimi<PERSON><PERSON>ng<PERSON>
from .semantic_indexer import SemanticIndexer
from .dependency_analyzer import DependencyAnalyzer
from .multi_language_processor import MultiLanguageProcessor
from .rag_enhanced_generator import RAGEnhancedGenerator
from .intelligent_refactoring import IntelligentRefactoring
from .context_aware_completion import ContextAwareCompletion
from .error_detector import ErrorDetector
from .predictive_debugger import PredictiveDebugger
from .performance_analyzer import PerformanceAnalyzer
from .optimization_recommender import OptimizationRecommender
from .learning_system import LearningSystem

__all__ = [
    "AICodeAssistant",
    "SelfAnalyzer",
    "ExecutionMonitor",
    "AdaptiveController",
    "OptimizationEngine",
    "SemanticIndexer",
    "DependencyAnalyzer",
    "MultiLanguageProcessor",
    "RAGEnhancedGenerator",
    "IntelligentRefactoring",
    "ContextAwareCompletion",
    "ErrorDetector",
    "PredictiveDebugger",
    "PerformanceAnalyzer",
    "OptimizationRecommender",
    "LearningSystem",
]
