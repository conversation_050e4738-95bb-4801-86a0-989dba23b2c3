"""
Iterative Execution Manager integrating IterativeController with SelfAnalyzingIntelligence.
Provides robust step-by-step execution with self-analysis capabilities.
"""

import logging
import time
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path

from .iterative_controller import (
    IterativeController, ExecutionStep, StepStatus, IterativeSession
)
from .self_analyzing_intelligence import (
    SelfAnalyzingIntelligence, AnalysisType, AnalysisResult, DecisionType
)
from .execution_monitor import ExecutionMonitor
from .enhanced_performance_monitor import EnhancedPerformanceMonitor

logger = logging.getLogger(__name__)

class IterativeExecutionManager:
    """
    Manager that combines iterative execution control with self-analysis capabilities.
    """
    
    def __init__(self, model_manager, workspace_dir: Optional[Path] = None):
        """Initialize the iterative execution manager.
        
        Args:
            model_manager: The model manager for AI operations
            workspace_dir: Optional workspace directory
        """
        self.workspace_dir = workspace_dir or Path.cwd()
        
        # Initialize core components
        self.iterative_controller = IterativeController(workspace_dir)
        self.self_analyzer = SelfAnalyzingIntelligence(model_manager, workspace_dir)
        self.execution_monitor = ExecutionMonitor()
        self.performance_monitor = EnhancedPerformanceMonitor(workspace_dir)
        
        # Register analysis callback
        self.self_analyzer.add_analysis_callback(self._on_analysis_complete)
        
        logger.info("Initialized IterativeExecutionManager with self-analysis capabilities")

    def execute_with_analysis(self, 
                            task_description: str,
                            steps: List[Dict[str, Any]],
                            success_criteria: List[str],
                            constraints: List[str] = None) -> Dict[str, Any]:
        """
        Execute a task with step-by-step analysis and monitoring.
        
        Args:
            task_description: Description of the task to execute
            steps: List of execution steps with their parameters
            success_criteria: Criteria for determining success
            constraints: Optional constraints to consider
            
        Returns:
            Execution results and analysis
        """
        session_id = self.iterative_controller.start_session(task_description)
        
        # Add steps to controller
        for step in steps:
            self.iterative_controller.add_step(
                description=step['description'],
                operation=step['operation'],
                parameters=step.get('parameters', {})
            )
        
        results = []
        analysis_history = []
        
        # Start monitoring
        self.execution_monitor.start_monitoring()
        self.performance_monitor.start_monitoring()
        
        try:
            while True:
                # Execute next step
                success, status, result = self.iterative_controller.execute_next_step()
                results.append({
                    'success': success,
                    'status': status,
                    'result': result
                })
                
                if not success:
                    logger.warning(f"Step failed: {status}")
                    break
                
                # Analyze results
                analysis_result = self.self_analyzer.analyze_and_improve(
                    initial_input=str(result),
                    analysis_type=AnalysisType.CODE_EXECUTION,
                    success_criteria=success_criteria,
                    constraints=constraints
                )
                analysis_history.extend(analysis_result)
                
                # Check if complete
                if self._should_complete(analysis_result):
                    logger.info("Execution completed successfully")
                    break
                    
                # Apply improvements if needed
                if self._should_improve(analysis_result):
                    self._apply_improvements(analysis_result[-1])
                
            # Collect final metrics
            execution_metrics = self.execution_monitor.get_metrics()
            performance_metrics = self.performance_monitor.get_metrics()
            
            return {
                'session_id': session_id,
                'success': all(r['success'] for r in results),
                'results': results,
                'analysis_history': [asdict(a) for a in analysis_history],
                'execution_metrics': execution_metrics,
                'performance_metrics': performance_metrics,
                'improvements_applied': self._count_improvements(analysis_history)
            }
            
        finally:
            self.execution_monitor.stop_monitoring()
            self.performance_monitor.stop_monitoring()
    
    def _should_complete(self, analysis_results: List[AnalysisResult]) -> bool:
        """Determine if execution should complete based on analysis."""
        if not analysis_results:
            return False
            
        latest = analysis_results[-1]
        return (
            latest.success and
            latest.confidence_score >= 0.8 and
            all(a.decision_type != DecisionType.DEBUG for a in analysis_results[-3:])
        )
    
    def _should_improve(self, analysis_results: List[AnalysisResult]) -> bool:
        """Determine if improvements should be applied."""
        if not analysis_results:
            return False
            
        latest = analysis_results[-1]
        return (
            not latest.success or
            latest.confidence_score < 0.8 or
            len(latest.recommendations) > 0
        )
    
    def _apply_improvements(self, analysis_result: AnalysisResult):
        """Apply improvements based on analysis."""
        if not analysis_result.recommendations:
            return
            
        logger.info(f"Applying {len(analysis_result.recommendations)} improvements")
        
        for recommendation in analysis_result.recommendations:
            try:
                # Parse and apply recommendation
                self._implement_recommendation(recommendation)
                logger.info(f"Applied improvement: {recommendation}")
            except Exception as e:
                logger.error(f"Failed to apply improvement: {e}")
    
    def _implement_recommendation(self, recommendation: str):
        """Implement a specific improvement recommendation."""
        # TODO: Implement recommendation application logic
        # This would involve parsing the recommendation and making appropriate changes
        pass
    
    def _count_improvements(self, analysis_history: List[AnalysisResult]) -> int:
        """Count number of improvements applied."""
        return sum(1 for a in analysis_history if a.recommendations)
    
    def _on_analysis_complete(self, result: AnalysisResult):
        """Handle completed analysis result."""
        if result.success:
            logger.info(f"Analysis successful with confidence {result.confidence_score:.2f}")
        else:
            logger.warning(f"Analysis indicates issues: {', '.join(result.findings)}")
            
        # Record metrics
        self.performance_monitor.record_event(
            event_type="analysis_complete",
            metrics={
                "success": result.success,
                "confidence": result.confidence_score,
                "execution_time": result.execution_time
            }
        )
