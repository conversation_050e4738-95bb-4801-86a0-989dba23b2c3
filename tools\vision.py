"""
Vision tool for the Advanced AI Agent.
"""

import os
import io
import base64
import platform
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple

from PIL import Image, ImageGrab

class VisionTool:
    """Vision tool for image processing."""
    
    def __init__(self, workspace_dir: Optional[Path] = None):
        """Initialize the vision tool.
        
        Args:
            workspace_dir: The workspace directory to use. If None, will use the current directory.
        """
        self.workspace_dir = workspace_dir or Path.cwd()
        self.history: List[Dict[str, str]] = []
    
    def take_screenshot(self, path: Optional[Union[str, Path]] = None) -> Path:
        """Take a screenshot.
        
        Args:
            path: The path to save the screenshot to. If None, will save to a temporary file.
            
        Returns:
            The path to the screenshot.
        """
        # Add to history
        self.history.append({"action": "take_screenshot"})
        
        try:
            # Determine the path to save the screenshot
            if path is None:
                # Create a temporary file
                fd, temp_path = tempfile.mkstemp(suffix=".png")
                os.close(fd)
                screenshot_path = Path(temp_path)
            else:
                # Resolve the path
                screenshot_path = self._resolve_path(path)
                screenshot_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Take the screenshot based on the platform
            system = platform.system()
            
            if system == "Windows":
                # Use PIL's ImageGrab on Windows
                screenshot = ImageGrab.grab()
                screenshot.save(str(screenshot_path))
            
            elif system == "Darwin":  # macOS
                # Use screencapture on macOS
                subprocess.run(["screencapture", "-x", str(screenshot_path)], check=True)
            
            elif system == "Linux":
                # Try to use gnome-screenshot or scrot on Linux
                try:
                    subprocess.run(["gnome-screenshot", "-f", str(screenshot_path)], check=True)
                except (subprocess.SubprocessError, FileNotFoundError):
                    try:
                        subprocess.run(["scrot", str(screenshot_path)], check=True)
                    except (subprocess.SubprocessError, FileNotFoundError):
                        raise RuntimeError("No screenshot tool available on Linux. Please install gnome-screenshot or scrot.")
            
            else:
                raise RuntimeError(f"Unsupported platform: {system}")
            
            # Add to history
            self.history[-1]["path"] = str(screenshot_path)
            
            return screenshot_path
        
        except Exception as e:
            error_message = f"Error taking screenshot: {e}"
            self.history[-1]["error"] = error_message
            raise RuntimeError(error_message)
    
    def load_image(self, path: Union[str, Path]) -> Image.Image:
        """Load an image.
        
        Args:
            path: The path to the image to load.
            
        Returns:
            The loaded image.
        """
        # Add to history
        self.history.append({"action": "load_image", "path": str(path)})
        
        try:
            # Resolve the path
            image_path = self._resolve_path(path)
            
            # Check if the file exists
            if not image_path.exists():
                raise FileNotFoundError(f"Image not found: {image_path}")
            
            # Load the image
            image = Image.open(image_path)
            
            return image
        
        except Exception as e:
            error_message = f"Error loading image: {e}"
            self.history[-1]["error"] = error_message
            raise RuntimeError(error_message)
    
    def save_image(self, image: Image.Image, path: Union[str, Path]) -> Path:
        """Save an image.
        
        Args:
            image: The image to save.
            path: The path to save the image to.
            
        Returns:
            The path to the saved image.
        """
        # Add to history
        self.history.append({"action": "save_image", "path": str(path)})
        
        try:
            # Resolve the path
            image_path = self._resolve_path(path)
            
            # Create the parent directory if it doesn't exist
            image_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save the image
            image.save(str(image_path))
            
            return image_path
        
        except Exception as e:
            error_message = f"Error saving image: {e}"
            self.history[-1]["error"] = error_message
            raise RuntimeError(error_message)
    
    def image_to_base64(self, image: Union[Image.Image, str, Path]) -> str:
        """Convert an image to a base64 string.
        
        Args:
            image: The image to convert. Can be a PIL Image, a path to an image, or a base64 string.
            
        Returns:
            The base64 string.
        """
        # Add to history
        self.history.append({"action": "image_to_base64"})
        
        try:
            # Convert the image to a PIL Image if it's a path
            if isinstance(image, (str, Path)):
                image = self.load_image(image)
            
            # Convert the image to base64
            buffer = io.BytesIO()
            image.save(buffer, format="PNG")
            base64_string = base64.b64encode(buffer.getvalue()).decode("utf-8")
            
            return base64_string
        
        except Exception as e:
            error_message = f"Error converting image to base64: {e}"
            self.history[-1]["error"] = error_message
            raise RuntimeError(error_message)
    
    def base64_to_image(self, base64_string: str) -> Image.Image:
        """Convert a base64 string to an image.
        
        Args:
            base64_string: The base64 string to convert.
            
        Returns:
            The converted image.
        """
        # Add to history
        self.history.append({"action": "base64_to_image"})
        
        try:
            # Convert the base64 string to an image
            image_data = base64.b64decode(base64_string)
            image = Image.open(io.BytesIO(image_data))
            
            return image
        
        except Exception as e:
            error_message = f"Error converting base64 to image: {e}"
            self.history[-1]["error"] = error_message
            raise RuntimeError(error_message)
    
    def get_history(self) -> List[Dict[str, str]]:
        """Get the vision history.
        
        Returns:
            The vision history.
        """
        return self.history
    
    def _resolve_path(self, path: Union[str, Path]) -> Path:
        """Resolve a path relative to the workspace directory.
        
        Args:
            path: The path to resolve.
            
        Returns:
            The resolved path.
        """
        if isinstance(path, str):
            path = Path(path)
        
        if path.is_absolute():
            return path
        
        return self.workspace_dir / path
