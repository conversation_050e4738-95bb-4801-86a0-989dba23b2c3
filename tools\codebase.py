"""
Codebase tool for the Advanced AI Agent.
"""

import os
import re
import glob
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Set, Tuple

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from tools.file import FileTool

class CodebaseTool:
    """Codebase tool for code analysis and search."""

    def __init__(self, workspace_dir: Optional[Path] = None):
        """Initialize the codebase tool.

        Args:
            workspace_dir: The workspace directory to use. If None, will use the current directory.
        """
        self.workspace_dir = workspace_dir or Path.cwd()
        self.file_tool = FileTool(workspace_dir)

        # File extensions to consider as code files
        self.code_extensions = {
            ".py", ".js", ".ts", ".jsx", ".tsx", ".html", ".css", ".scss",
            ".java", ".c", ".cpp", ".h", ".hpp", ".cs", ".go", ".rs", ".php",
            ".rb", ".swift", ".kt", ".scala", ".sh", ".bash", ".ps1", ".sql"
        }

        # Directories to ignore
        self.ignore_dirs = {
            "node_modules", "venv", ".venv", "env", ".env", ".git", "__pycache__",
            "dist", "build", "target", "out", "bin", "obj", ".idea", ".vscode"
        }

    def find_files(self, pattern: str = "*", include_hidden: bool = False) -> List[str]:
        """Find files in the workspace.

        Args:
            pattern: The pattern to match files against.
            include_hidden: Whether to include hidden files.

        Returns:
            A list of file paths.
        """
        # Resolve the pattern
        if not os.path.isabs(pattern):
            pattern = os.path.join(str(self.workspace_dir), pattern)

        # Find files
        files = []
        for path in glob.glob(pattern, recursive=True):
            # Convert to Path object
            path_obj = Path(path)

            # Skip directories
            if path_obj.is_dir():
                continue

            # Skip hidden files if not included
            if not include_hidden and path_obj.name.startswith("."):
                continue

            # Skip files in ignored directories
            if any(ignore_dir in path_obj.parts for ignore_dir in self.ignore_dirs):
                continue

            # Add to the list
            files.append(os.path.relpath(path, str(self.workspace_dir)))

        return files

    def find_code_files(self, include_hidden: bool = False) -> List[str]:
        """Find code files in the workspace.

        Args:
            include_hidden: Whether to include hidden files.

        Returns:
            A list of code file paths.
        """
        # Find all files
        all_files = self.find_files(include_hidden=include_hidden)

        # Filter for code files
        code_files = [
            file for file in all_files
            if Path(file).suffix.lower() in self.code_extensions
        ]

        return code_files

    def search_code(self, pattern: str, file_pattern: str = "*") -> Dict[str, List[str]]:
        """Search for a pattern in code files.

        Args:
            pattern: The pattern to search for.
            file_pattern: The pattern of files to search in.

        Returns:
            A dictionary mapping file paths to lists of matching lines.
        """
        # Find files matching the file pattern
        files = self.find_files(file_pattern)

        # Filter for code files
        code_files = [
            file for file in files
            if Path(file).suffix.lower() in self.code_extensions
        ]

        # Search for the pattern in the files
        return self.file_tool.grep_files(pattern, file_pattern)

    def analyze_file(self, path: Union[str, Path]) -> Dict[str, Any]:
        """Analyze a code file.

        Args:
            path: The path to the file to analyze.

        Returns:
            Analysis results.
        """
        try:
            # Convert to Path object and resolve relative to workspace
            if isinstance(path, str):
                if not path.startswith('/') and not path.startswith('C:'):
                    file_path = self.workspace_dir / path
                else:
                    file_path = Path(path)
            else:
                file_path = path

            # Check if file exists, try alternative paths if not
            if not file_path.exists():
                alt_paths = [
                    self.workspace_dir / str(path),
                    Path.cwd() / str(path),
                    Path(str(path))
                ]

                for alt_path in alt_paths:
                    if alt_path.exists() and alt_path.is_file():
                        file_path = alt_path
                        break
                else:
                    return {
                        "error": f"File '{path}' not found in workspace or current directory",
                        "searched_paths": [str(p) for p in alt_paths]
                    }

            # Read the file with error handling
            try:
                content = self.file_tool.read_file(str(file_path))
            except Exception as e:
                return {
                    "error": f"Could not read file '{path}': {str(e)}",
                    "file_path": str(file_path)
                }

            # Get the file extension
            file_ext = file_path.suffix.lower()

            # Analyze based on the file type
            analysis_result = {}
            if file_ext == ".py":
                analysis_result = self._analyze_python(content)
            elif file_ext in {".js", ".ts", ".jsx", ".tsx"}:
                analysis_result = self._analyze_javascript(content)
            else:
                # Generic analysis
                analysis_result = self._analyze_generic(content)

            # Add file metadata
            analysis_result.update({
                "file_path": str(file_path),
                "file_name": file_path.name,
                "file_extension": file_ext,
                "file_size": len(content),
                "language": self._detect_language(file_ext),
                "complexity_score": self._calculate_complexity(content, file_ext)
            })

            return analysis_result

        except Exception as e:
            return {
                "error": f"Error analyzing file '{path}': {str(e)}",
                "file_path": str(path)
            }

    def _detect_language(self, file_ext: str) -> str:
        """Detect programming language from file extension."""
        language_map = {
            ".py": "python",
            ".js": "javascript",
            ".ts": "typescript",
            ".jsx": "javascript",
            ".tsx": "typescript",
            ".java": "java",
            ".c": "c",
            ".cpp": "cpp",
            ".h": "c",
            ".hpp": "cpp",
            ".cs": "csharp",
            ".go": "go",
            ".rs": "rust",
            ".php": "php",
            ".rb": "ruby",
            ".swift": "swift",
            ".kt": "kotlin",
            ".scala": "scala",
            ".sh": "bash",
            ".bash": "bash",
            ".ps1": "powershell",
            ".sql": "sql",
            ".html": "html",
            ".css": "css",
            ".scss": "scss"
        }
        return language_map.get(file_ext, "unknown")

    def _calculate_complexity(self, content: str, file_ext: str) -> float:
        """Calculate a simple complexity score for the code."""
        lines = content.splitlines()
        non_empty_lines = [line for line in lines if line.strip()]

        if not non_empty_lines:
            return 0.0

        # Count complexity indicators
        complexity_keywords = ["if", "for", "while", "try", "except", "switch", "case", "catch"]
        complexity_count = 0

        for line in non_empty_lines:
            line_lower = line.lower()
            for keyword in complexity_keywords:
                if keyword in line_lower:
                    complexity_count += 1

        # Normalize by line count
        return min(complexity_count / len(non_empty_lines), 1.0)

    def _analyze_python(self, content: str) -> Dict[str, Any]:
        """Analyze Python code.

        Args:
            content: The code content.

        Returns:
            Analysis results.
        """
        # Extract imports
        import_pattern = r"^\s*(import|from)\s+([^\s]+)"
        imports = []
        for line in content.splitlines():
            match = re.match(import_pattern, line)
            if match:
                imports.append(line.strip())

        # Extract classes
        class_pattern = r"^\s*class\s+([^\s(:]+)"
        classes = []
        for line in content.splitlines():
            match = re.match(class_pattern, line)
            if match:
                classes.append(match.group(1))

        # Extract functions
        function_pattern = r"^\s*def\s+([^\s(]+)"
        functions = []
        for line in content.splitlines():
            match = re.match(function_pattern, line)
            if match:
                functions.append(match.group(1))

        return {
            "imports": imports,
            "classes": classes,
            "functions": functions,
            "line_count": len(content.splitlines())
        }

    def _analyze_javascript(self, content: str) -> Dict[str, Any]:
        """Analyze JavaScript/TypeScript code.

        Args:
            content: The code content.

        Returns:
            Analysis results.
        """
        # Extract imports
        import_pattern = r"^\s*(import|export)\s+"
        imports = []
        for line in content.splitlines():
            if re.match(import_pattern, line):
                imports.append(line.strip())

        # Extract classes
        class_pattern = r"^\s*(export\s+)?(class)\s+([^\s{]+)"
        classes = []
        for line in content.splitlines():
            match = re.match(class_pattern, line)
            if match:
                classes.append(match.group(3))

        # Extract functions
        function_pattern = r"^\s*(export\s+)?(function|const|let|var)\s+([^\s=({]+)"
        functions = []
        for line in content.splitlines():
            match = re.match(function_pattern, line)
            if match and match.group(2) != "const" and match.group(2) != "let" and match.group(2) != "var":
                functions.append(match.group(3))
            elif match and "=>" in line:
                functions.append(match.group(3))

        return {
            "imports": imports,
            "classes": classes,
            "functions": functions,
            "line_count": len(content.splitlines())
        }

    def _analyze_generic(self, content: str) -> Dict[str, Any]:
        """Analyze generic code.

        Args:
            content: The code content.

        Returns:
            Analysis results.
        """
        # Count lines
        lines = content.splitlines()
        line_count = len(lines)

        # Count non-empty lines
        non_empty_lines = sum(1 for line in lines if line.strip())

        # Count comment lines (approximate)
        comment_lines = sum(1 for line in lines if line.strip().startswith(("#", "//", "/*", "*", "*/")))

        return {
            "line_count": line_count,
            "non_empty_line_count": non_empty_lines,
            "comment_line_count": comment_lines
        }
